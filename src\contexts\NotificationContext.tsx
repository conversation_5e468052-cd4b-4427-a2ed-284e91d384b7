
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { Bell } from 'lucide-react';

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'wallet_funded' | 'card_funded' | 'group_card_added' | 'group_card_invitation' | 'group_card_accepted' | 'group_card_declined' | 'transaction' | 'system' | 'off_ramp_initiated' | 'off_ramp_processing' | 'off_ramp_completed' | 'off_ramp_failed' | 'merchant_payment';
  read: boolean;
  created_at: string;
  metadata?: Record<string, any>;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  addNotification: (notification: Omit<Notification, 'id' | 'created_at' | 'read'>) => Promise<void>;
  loading: boolean;
  refreshNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Default values for when the hook is used outside the provider
const defaultValues: NotificationContextType = {
  notifications: [],
  unreadCount: 0,
  markAsRead: async () => {},
  markAllAsRead: async () => {},
  addNotification: async () => {},
  loading: false,
  refreshNotifications: async () => {},
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  // Return default values instead of throwing an error
  return context || defaultValues;
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  // Function to fetch notifications
  const fetchNotifications = async () => {
    if (!user) {
      setNotifications([]);
      setUnreadCount(0);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Try to fetch real data
      try {
        const { data, error } = await supabase
          .from('notifications')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(50);

        if (!error && data) {
          // Type cast the notifications to match our interface
          const typedNotifications: Notification[] = data.map(notification => ({
            ...notification,
            type: notification.type as Notification['type'],
            metadata: notification.metadata as Record<string, any> | undefined
          }));
          
          setNotifications(typedNotifications);
          setUnreadCount(typedNotifications.filter(n => !n.read).length);
        } else {
          // Only set empty notifications if there was an error
          console.log('Error fetching notifications:', error);
          setNotifications([]);
          setUnreadCount(0);
        }
      } catch (fetchError) {
        console.log('Could not fetch notifications, using empty array');
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Error in fetchNotifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch notifications when user changes
  useEffect(() => {
    fetchNotifications();

    // Set up real-time subscription for notifications
    if (user) {
      console.log('🔄 Setting up real-time notification subscription for user:', user.id);
      const channel = supabase
        .channel('notifications')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            console.log('🔔 New notification received via real-time:', payload);
            const newNotification = payload.new as Notification;

            setNotifications(prev => {
              console.log('📝 Adding notification to list:', newNotification.title);
              return [newNotification, ...prev];
            });
            setUnreadCount(prev => {
              const newCount = prev + 1;
              console.log('🔴 Unread count updated:', newCount);
              return newCount;
            });

            // Show toast notification
            console.log('🍞 Showing toast notification:', newNotification.title);
            toast({
              title: newNotification.title,
              description: newNotification.message,
            });
          }
        )
        .subscribe((status) => {
          console.log('📡 Notification subscription status:', status);
        });

      return () => {
        console.log('🔌 Removing notification subscription');
        supabase.removeChannel(channel);
      };
    }
  }, [user]);

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    if (!user) return;

    // Update local state immediately
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
    setUnreadCount(prev => Math.max(0, prev - 1));

    // Try to update in the database
    try {
      await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id)
        .eq('user_id', user.id);
    } catch (error) {
      console.error('Error in markAsRead:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user || notifications.length === 0) return;

    // Update local state immediately
    setNotifications(prev =>
      prev.map(n => ({ ...n, read: true }))
    );
    setUnreadCount(0);

    // Try to update in the database
    try {
      await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false);
    } catch (error) {
      console.error('Error in markAllAsRead:', error);
    }
  };

  // Add a new notification
  const addNotification = async (notification: Omit<Notification, 'id' | 'created_at' | 'read'>) => {
    if (!user) return;

    // Add to local state immediately with a temporary ID
    const tempId = `temp-${Date.now()}`;
    const newNotification: Notification = {
      id: tempId,
      ...notification,
      user_id: user.id,
      read: false,
      created_at: new Date().toISOString(),
    };

    setNotifications(prev => [newNotification, ...prev]);
    setUnreadCount(prev => prev + 1);

    // Show a toast notification
    toast({
      title: newNotification.title,
      description: newNotification.message,
    });

    // Try to add to the database
    try {
      await supabase
        .from('notifications')
        .insert({
          ...notification,
          user_id: user.id,
          read: false,
        });
    } catch (error) {
      console.error('Error in addNotification:', error);
    }
  };

  const refreshNotifications = fetchNotifications;

  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    addNotification,
    loading,
    refreshNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
