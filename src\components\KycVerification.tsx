
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Shield, CheckCircle, AlertCircle, Clock } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

const KycVerification = () => {
  const { user } = useAuth();
  const [kycStatus, setKycStatus] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    checkKycStatus();
  }, [user]);

  const checkKycStatus = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      
      // First check local profile data
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('kyc_status')
        .eq('id', user.id)
        .single();
      
      if (profileError) throw profileError;
      
      if (profile && profile.kyc_status) {
        setKycStatus(profile.kyc_status);
      }
      
      // Then check with Onfido for latest status
      const { data: sessionData } = await supabase.auth.getSession();
      const token = sessionData?.session?.access_token;
      
      if (!token) throw new Error("Authentication required");
      
      const response = await fetch('/api/kyc-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ action: 'check_status' })
      });
      
      if (!response.ok) {
        throw new Error('Failed to check KYC status');
      }
      
      const data = await response.json();
      setKycStatus(data.status);
    } catch (error) {
      console.error('Error checking KYC status:', error);
      toast({
        title: "Error",
        description: "Failed to check verification status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const startKycVerification = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      
      const { data: sessionData } = await supabase.auth.getSession();
      const token = sessionData?.session?.access_token;
      
      if (!token) throw new Error("Authentication required");
      
      const response = await fetch('/api/kyc-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ action: 'create_applicant' })
      });
      
      if (!response.ok) {
        throw new Error('Failed to start KYC verification');
      }
      
      const { sdkToken, workflowRunId } = await response.json();
      
      // In a real implementation, you would initialize the Onfido SDK here
      // For example:
      // Onfido.init({
      //   token: sdkToken,
      //   containerId: 'onfido-mount',
      //   onComplete: () => {
      //     setKycStatus('pending');
      //     toast({
      //       title: "Verification Submitted",
      //       description: "Your identity verification has been submitted for review.",
      //     });
      //   }
      // });
      
      // For now, we'll just simulate the flow
      setKycStatus('pending');
      toast({
        title: "Verification Started",
        description: "Your identity verification process has started. In a real implementation, the Onfido SDK would open here.",
      });
      
    } catch (error) {
      console.error('Error starting KYC verification:', error);
      toast({
        title: "Error",
        description: "Failed to start verification process. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderStatus = () => {
    switch (kycStatus) {
      case 'verified':
        return (
          <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
            <CheckCircle className="h-5 w-5" />
            <span>Verified</span>
          </div>
        );
      case 'rejected':
        return (
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <span>Verification Failed</span>
          </div>
        );
      case 'pending':
        return (
          <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400">
            <Clock className="h-5 w-5" />
            <span>Pending Review</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Shield className="h-5 w-5" />
            <span>Not Verified</span>
          </div>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-primary" /> 
          Identity Verification
        </CardTitle>
        <CardDescription>
          Verify your identity to unlock all features of SOLPAY
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-muted/50 p-4 rounded-md flex items-start gap-4">
          <Shield className="h-6 w-6 text-primary mt-1" />
          <div className="flex-1">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">KYC Verification</h3>
              {renderStatus()}
            </div>
            
            <p className="text-sm text-muted-foreground mt-1">
              {kycStatus === 'verified'
                ? "Your identity has been verified. You have full access to all SOLPAY features."
                : kycStatus === 'pending'
                ? "Your verification is being processed. This usually takes 24-48 hours."
                : kycStatus === 'rejected'
                ? "Your verification was not successful. Please try again with valid documents."
                : "Complete identity verification to protect your account and access all features."}
            </p>
            
            {kycStatus !== 'verified' && kycStatus !== 'pending' && (
              <div className="mt-3">
                <Button 
                  onClick={startKycVerification} 
                  disabled={isLoading}
                >
                  {isLoading ? "Processing..." : "Start Verification"}
                </Button>
              </div>
            )}
            
            {kycStatus === 'rejected' && (
              <div className="mt-3">
                <Button 
                  variant="outline"
                  onClick={startKycVerification} 
                  disabled={isLoading}
                >
                  Try Again
                </Button>
              </div>
            )}
            
            {/* This div would be used to mount the Onfido SDK in a real implementation */}
            <div id="onfido-mount" className="mt-4"></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default KycVerification;
