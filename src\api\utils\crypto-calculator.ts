/**
 * Crypto Calculator Utilities
 * 
 * Handles cryptocurrency price fetching and conversion calculations
 */

interface ExchangeRates {
  [crypto: string]: {
    usd: number;
    ngn: number;
    timestamp: number;
  };
}

interface CryptoAmount {
  crypto: string;
  amount: number;
  usd_equivalent: number;
  ngn_equivalent: number;
}

// Cache for exchange rates (5 minute cache)
let ratesCache: ExchangeRates | null = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get current exchange rates for cryptocurrencies
 */
export const getCurrentExchangeRates = async (cryptocurrencies: string[]): Promise<ExchangeRates> => {
  const now = Date.now();
  
  // Return cached rates if still valid
  if (ratesCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return ratesCache;
  }

  try {
    // In production, you would fetch from a real API like CoinGecko, CoinMarketCap, etc.
    // For now, we'll use mock data with realistic fluctuations
    const rates: ExchangeRates = {};
    
    for (const crypto of cryptocurrencies) {
      rates[crypto] = await fetchCryptoPrice(crypto);
    }

    // Update cache
    ratesCache = rates;
    cacheTimestamp = now;

    return rates;
  } catch (error) {
    console.error('Error fetching exchange rates:', error);
    
    // Return fallback rates if API fails
    return getFallbackRates(cryptocurrencies);
  }
};

/**
 * Calculate crypto amount needed for a USD amount
 */
export const calculateCryptoAmount = async (
  usdAmount: number,
  cryptocurrency: string
): Promise<CryptoAmount> => {
  const rates = await getCurrentExchangeRates([cryptocurrency]);
  const cryptoRate = rates[cryptocurrency];

  if (!cryptoRate) {
    throw new Error(`Exchange rate not available for ${cryptocurrency}`);
  }

  const cryptoAmount = usdAmount / cryptoRate.usd;
  const ngnEquivalent = usdAmount * (cryptoRate.ngn / cryptoRate.usd);

  return {
    crypto: cryptocurrency,
    amount: parseFloat(cryptoAmount.toFixed(8)), // 8 decimal places for crypto
    usd_equivalent: usdAmount,
    ngn_equivalent: parseFloat(ngnEquivalent.toFixed(2))
  };
};

/**
 * Calculate NGN amount for crypto settlement
 */
export const calculateNgnSettlement = async (
  cryptoAmount: number,
  cryptocurrency: string
): Promise<number> => {
  const rates = await getCurrentExchangeRates([cryptocurrency]);
  const cryptoRate = rates[cryptocurrency];

  if (!cryptoRate) {
    throw new Error(`Exchange rate not available for ${cryptocurrency}`);
  }

  const usdValue = cryptoAmount * cryptoRate.usd;
  const ngnValue = usdValue * (cryptoRate.ngn / cryptoRate.usd);

  return parseFloat(ngnValue.toFixed(2));
};

/**
 * Fetch crypto price from external API (mock implementation)
 */
const fetchCryptoPrice = async (cryptocurrency: string): Promise<{ usd: number; ngn: number; timestamp: number }> => {
  // Mock prices with realistic fluctuations
  const basePrices: { [key: string]: number } = {
    'SOL': 149.50,
    'USDC': 0.9998,
    'ETH': 2340.75,
    'BTC': 43250.00
  };

  const basePrice = basePrices[cryptocurrency];
  if (!basePrice) {
    throw new Error(`Unsupported cryptocurrency: ${cryptocurrency}`);
  }

  // Add realistic price fluctuation (±2%)
  const fluctuation = (Math.random() - 0.5) * 0.04; // ±2%
  const currentPrice = basePrice * (1 + fluctuation);

  // Mock NGN rate (1 USD = ~800 NGN with fluctuation)
  const baseNgnRate = 800;
  const ngnFluctuation = (Math.random() - 0.5) * 0.02; // ±1%
  const currentNgnRate = baseNgnRate * (1 + ngnFluctuation);

  return {
    usd: parseFloat(currentPrice.toFixed(6)),
    ngn: parseFloat((currentPrice * currentNgnRate).toFixed(2)),
    timestamp: Date.now()
  };
};

/**
 * Get fallback rates when API is unavailable
 */
const getFallbackRates = (cryptocurrencies: string[]): ExchangeRates => {
  const fallbackRates: ExchangeRates = {};
  const timestamp = Date.now();

  const fallbackPrices: { [key: string]: number } = {
    'SOL': 150.00,
    'USDC': 1.00,
    'ETH': 2350.00,
    'BTC': 43000.00
  };

  for (const crypto of cryptocurrencies) {
    const usdPrice = fallbackPrices[crypto] || 1.00;
    fallbackRates[crypto] = {
      usd: usdPrice,
      ngn: usdPrice * 800, // 1 USD = 800 NGN
      timestamp
    };
  }

  return fallbackRates;
};

/**
 * Format crypto amount for display
 */
export const formatCryptoAmount = (amount: number, cryptocurrency: string): string => {
  const decimals = cryptocurrency === 'BTC' ? 8 : 
                   cryptocurrency === 'ETH' ? 6 :
                   cryptocurrency === 'SOL' ? 4 : 2;

  return amount.toFixed(decimals);
};

/**
 * Format currency amount for display
 */
export const formatCurrencyAmount = (amount: number, currency: string): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount / 100); // Convert from cents
};

/**
 * Validate crypto amount precision
 */
export const validateCryptoAmount = (amount: number, cryptocurrency: string): boolean => {
  const maxDecimals = cryptocurrency === 'BTC' ? 8 : 
                      cryptocurrency === 'ETH' ? 18 :
                      cryptocurrency === 'SOL' ? 9 : 6;

  const decimals = (amount.toString().split('.')[1] || '').length;
  return decimals <= maxDecimals;
};

/**
 * Get minimum payment amount for cryptocurrency
 */
export const getMinimumAmount = (cryptocurrency: string): number => {
  const minimums: { [key: string]: number } = {
    'BTC': 0.00001, // 1000 satoshis
    'ETH': 0.001,   // 0.001 ETH
    'SOL': 0.01,    // 0.01 SOL
    'USDC': 1.00    // $1 USDC
  };

  return minimums[cryptocurrency] || 0.01;
};
