-- Cross-Chain Database Schema
-- Add these tables to your Supabase database

-- Cross-chain wallets table
CREATE TABLE IF NOT EXISTS cross_chain_wallets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  chain VARCHAR(20) NOT NULL,
  address VARCHAR(255) NOT NULL,
  public_key VARCHAR(255),
  encrypted_private_key TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  balances JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, chain, address)
);

-- Cross-chain transactions table
CREATE TABLE IF NOT EXISTS cross_chain_transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  from_chain VARCHAR(20) NOT NULL,
  to_chain VARCHAR(20) NOT NULL,
  from_token VARCHAR(20) NOT NULL,
  to_token VARCHAR(20) NOT NULL,
  from_amount DECIMAL(20,8) NOT NULL,
  to_amount DECIMAL(20,8) NOT NULL,
  from_address VARCHAR(255) NOT NULL,
  to_address VARCHAR(255) NOT NULL,
  bridge_provider VARCHAR(20) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  tx_hash VARCHAR(255),
  bridge_tx_hash VARCHAR(255),
  destination_tx_hash VARCHAR(255),
  estimated_time INTEGER, -- minutes
  fees JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  
  CHECK (status IN ('pending', 'bridging', 'completed', 'failed', 'refunded'))
);

-- Cross-chain off-ramp transactions
CREATE TABLE IF NOT EXISTS cross_chain_off_ramp_transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  from_chain VARCHAR(20) NOT NULL,
  from_token VARCHAR(20) NOT NULL,
  from_amount DECIMAL(20,8) NOT NULL,
  target_currency VARCHAR(10) NOT NULL,
  target_amount DECIMAL(20,2) NOT NULL,
  exchange_rate DECIMAL(20,8) NOT NULL,
  bank_name VARCHAR(255) NOT NULL,
  account_number VARCHAR(50) NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  bridge_transaction_id UUID REFERENCES cross_chain_transactions(id),
  status VARCHAR(20) DEFAULT 'pending',
  tx_hash VARCHAR(255),
  reference VARCHAR(100) UNIQUE,
  fees JSONB NOT NULL,
  route JSONB NOT NULL, -- Array of steps
  estimated_time INTEGER, -- minutes
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  
  CHECK (status IN ('pending', 'bridging', 'processing', 'completed', 'failed'))
);

-- Supported chains configuration
CREATE TABLE IF NOT EXISTS supported_chains (
  id VARCHAR(20) PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  rpc_url VARCHAR(255) NOT NULL,
  explorer_url VARCHAR(255) NOT NULL,
  chain_id INTEGER, -- For EVM chains
  logo VARCHAR(255),
  native_currency JSONB NOT NULL,
  supported_tokens JSONB DEFAULT '[]'::jsonb,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bridge providers configuration
CREATE TABLE IF NOT EXISTS bridge_providers (
  id VARCHAR(20) PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  api_url VARCHAR(255),
  supported_chains JSONB NOT NULL, -- Array of chain pairs
  fee_structure JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cross-chain exchange rates (extends existing exchange_rates)
ALTER TABLE exchange_rates ADD COLUMN IF NOT EXISTS chain VARCHAR(20);
ALTER TABLE exchange_rates ADD COLUMN IF NOT EXISTS token_address VARCHAR(255);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_cross_chain_wallets_user_chain ON cross_chain_wallets(user_id, chain);
CREATE INDEX IF NOT EXISTS idx_cross_chain_wallets_address ON cross_chain_wallets(address);
CREATE INDEX IF NOT EXISTS idx_cross_chain_transactions_user ON cross_chain_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_cross_chain_transactions_status ON cross_chain_transactions(status);
CREATE INDEX IF NOT EXISTS idx_cross_chain_off_ramp_user ON cross_chain_off_ramp_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_cross_chain_off_ramp_status ON cross_chain_off_ramp_transactions(status);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_chain_token ON exchange_rates(chain, from_currency);

-- Row Level Security (RLS) policies
ALTER TABLE cross_chain_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE cross_chain_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE cross_chain_off_ramp_transactions ENABLE ROW LEVEL SECURITY;

-- Users can only access their own wallets
CREATE POLICY "Users can view their own cross-chain wallets" ON cross_chain_wallets
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own cross-chain wallets" ON cross_chain_wallets
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own cross-chain wallets" ON cross_chain_wallets
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can only access their own transactions
CREATE POLICY "Users can view their own cross-chain transactions" ON cross_chain_transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own cross-chain transactions" ON cross_chain_transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own cross-chain transactions" ON cross_chain_transactions
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can only access their own off-ramp transactions
CREATE POLICY "Users can view their own cross-chain off-ramp transactions" ON cross_chain_off_ramp_transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own cross-chain off-ramp transactions" ON cross_chain_off_ramp_transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own cross-chain off-ramp transactions" ON cross_chain_off_ramp_transactions
  FOR UPDATE USING (auth.uid() = user_id);

-- Public read access for chain and provider configs
ALTER TABLE supported_chains ENABLE ROW LEVEL SECURITY;
ALTER TABLE bridge_providers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view supported chains" ON supported_chains
  FOR SELECT USING (true);

CREATE POLICY "Anyone can view bridge providers" ON bridge_providers
  FOR SELECT USING (true);

-- Insert initial chain configurations
INSERT INTO supported_chains (id, name, symbol, rpc_url, explorer_url, chain_id, logo, native_currency) VALUES
('solana', 'Solana', 'SOL', 'https://api.mainnet-beta.solana.com', 'https://solscan.io', NULL, '/crypto-logos/sol.svg', '{"name": "Solana", "symbol": "SOL", "decimals": 9}'),
('ethereum', 'Ethereum', 'ETH', 'https://eth-mainnet.g.alchemy.com/v2/your-api-key', 'https://etherscan.io', 1, '/crypto-logos/eth.svg', '{"name": "Ethereum", "symbol": "ETH", "decimals": 18}'),
('polygon', 'Polygon', 'MATIC', 'https://polygon-rpc.com', 'https://polygonscan.com', 137, '/crypto-logos/matic.svg', '{"name": "Polygon", "symbol": "MATIC", "decimals": 18}'),
('bsc', 'BNB Smart Chain', 'BNB', 'https://bsc-dataseed.binance.org', 'https://bscscan.com', 56, '/crypto-logos/bnb.svg', '{"name": "BNB", "symbol": "BNB", "decimals": 18}'),
('arbitrum', 'Arbitrum', 'ETH', 'https://arb1.arbitrum.io/rpc', 'https://arbiscan.io', 42161, '/crypto-logos/arbitrum.svg', '{"name": "Ethereum", "symbol": "ETH", "decimals": 18}'),
('avalanche', 'Avalanche', 'AVAX', 'https://api.avax.network/ext/bc/C/rpc', 'https://snowtrace.io', 43114, '/crypto-logos/avax.svg', '{"name": "Avalanche", "symbol": "AVAX", "decimals": 18}'),
('base', 'Base', 'ETH', 'https://mainnet.base.org', 'https://basescan.org', 8453, '/crypto-logos/base.svg', '{"name": "Ethereum", "symbol": "ETH", "decimals": 18}')
ON CONFLICT (id) DO NOTHING;

-- Insert initial bridge provider configurations
INSERT INTO bridge_providers (id, name, api_url, supported_chains, fee_structure) VALUES
('wormhole', 'Wormhole', 'https://api.wormhole.com', '["solana-ethereum", "ethereum-polygon", "solana-bsc"]', '{"base_fee": 0.0025, "network_fee_multiplier": 1.5}'),
('layerzero', 'LayerZero', 'https://api.layerzero.network', '["ethereum-polygon", "ethereum-arbitrum", "ethereum-avalanche"]', '{"base_fee": 0.002, "network_fee_multiplier": 1.2}'),
('allbridge', 'Allbridge', 'https://api.allbridge.io', '["solana-ethereum", "solana-polygon", "ethereum-bsc"]', '{"base_fee": 0.003, "network_fee_multiplier": 1.3}')
ON CONFLICT (id) DO NOTHING;

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_cross_chain_wallets_updated_at BEFORE UPDATE ON cross_chain_wallets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_supported_chains_updated_at BEFORE UPDATE ON supported_chains
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bridge_providers_updated_at BEFORE UPDATE ON bridge_providers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- MERCHANT QR PAYMENT SYSTEM TABLES
-- =====================================================

-- Merchant accounts for QR payments
CREATE TABLE merchant_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  business_name VARCHAR(255) NOT NULL,
  business_type VARCHAR(100),
  business_description TEXT,
  business_address TEXT,
  contact_phone VARCHAR(20),
  contact_email VARCHAR(255),

  -- Bank account details (same as off-ramp)
  bank_name VARCHAR(100) NOT NULL,
  account_number VARCHAR(20) NOT NULL,
  account_name VARCHAR(255) NOT NULL,

  -- QR code details
  qr_code_id VARCHAR(50) UNIQUE NOT NULL,
  qr_code_url TEXT,
  qr_code_data TEXT, -- JSON data embedded in QR

  -- Settings
  is_active BOOLEAN DEFAULT true,
  accepts_sol BOOLEAN DEFAULT true,
  accepts_usdc BOOLEAN DEFAULT true,
  min_payment_amount DECIMAL(10, 2) DEFAULT 100.00, -- Minimum NGN amount
  max_payment_amount DECIMAL(10, 2) DEFAULT 500000.00, -- Maximum NGN amount

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- QR code payments
CREATE TABLE qr_payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  merchant_id UUID REFERENCES merchant_accounts(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Payment details
  amount_crypto DECIMAL(18, 8) NOT NULL,
  crypto_symbol VARCHAR(10) NOT NULL,
  amount_ngn DECIMAL(15, 2) NOT NULL,
  exchange_rate DECIMAL(15, 6) NOT NULL,

  -- Transaction details
  transaction_hash VARCHAR(255),
  blockchain_network VARCHAR(20) NOT NULL,

  -- Payment processing
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded')),
  payment_method VARCHAR(20) DEFAULT 'qr_scan' CHECK (payment_method IN ('qr_scan', 'telegram_bot', 'voice_command')),

  -- Fees
  merchant_fee DECIMAL(10, 2) DEFAULT 0,
  customer_fee DECIMAL(10, 2) DEFAULT 0,
  platform_fee DECIMAL(10, 2) DEFAULT 0,

  -- Additional data
  payment_reference VARCHAR(100),
  customer_note TEXT,
  merchant_note TEXT,

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Telegram bot users
CREATE TABLE telegram_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  telegram_id BIGINT UNIQUE NOT NULL,
  telegram_username VARCHAR(100),
  telegram_first_name VARCHAR(100),
  telegram_last_name VARCHAR(100),

  -- Bot settings
  is_active BOOLEAN DEFAULT true,
  language_code VARCHAR(10) DEFAULT 'en',
  notifications_enabled BOOLEAN DEFAULT true,

  -- Security
  verification_code VARCHAR(10),
  is_verified BOOLEAN DEFAULT false,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Voice command logs
CREATE TABLE voice_commands (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Voice data
  command_text TEXT NOT NULL,
  original_audio_url TEXT,
  language_detected VARCHAR(10),
  confidence_score DECIMAL(3, 2), -- 0.00 to 1.00

  -- Processing
  intent VARCHAR(50), -- 'balance', 'offramp', 'deposit', etc.
  entities JSONB, -- Extracted entities (amount, token, bank, etc.)
  action_taken VARCHAR(100),
  success BOOLEAN DEFAULT false,

  -- Response
  response_text TEXT,
  response_audio_url TEXT,

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security for new tables
ALTER TABLE merchant_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE qr_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE telegram_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_commands ENABLE ROW LEVEL SECURITY;

-- RLS Policies for merchant_accounts
CREATE POLICY "Users can view their own merchant accounts" ON merchant_accounts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own merchant accounts" ON merchant_accounts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own merchant accounts" ON merchant_accounts
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for qr_payments
CREATE POLICY "Merchants can view payments to their accounts" ON qr_payments
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM merchant_accounts WHERE id = merchant_id
    ) OR auth.uid() = customer_id
  );

CREATE POLICY "Customers can insert payments" ON qr_payments
  FOR INSERT WITH CHECK (auth.uid() = customer_id);

CREATE POLICY "System can update payment status" ON qr_payments
  FOR UPDATE USING (true); -- Allow system updates for payment processing

-- RLS Policies for telegram_users
CREATE POLICY "Users can view their own telegram data" ON telegram_users
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own telegram data" ON telegram_users
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own telegram data" ON telegram_users
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for voice_commands
CREATE POLICY "Users can view their own voice commands" ON voice_commands
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own voice commands" ON voice_commands
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Add triggers for new tables
CREATE TRIGGER update_merchant_accounts_updated_at BEFORE UPDATE ON merchant_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_qr_payments_updated_at BEFORE UPDATE ON qr_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_telegram_users_updated_at BEFORE UPDATE ON telegram_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Indexes for performance
CREATE INDEX idx_merchant_accounts_user_id ON merchant_accounts(user_id);
CREATE INDEX idx_merchant_accounts_qr_code_id ON merchant_accounts(qr_code_id);
CREATE INDEX idx_qr_payments_merchant_id ON qr_payments(merchant_id);
CREATE INDEX idx_qr_payments_customer_id ON qr_payments(customer_id);
CREATE INDEX idx_qr_payments_status ON qr_payments(status);
CREATE INDEX idx_telegram_users_telegram_id ON telegram_users(telegram_id);
CREATE INDEX idx_telegram_users_user_id ON telegram_users(user_id);
CREATE INDEX idx_voice_commands_user_id ON voice_commands(user_id);
