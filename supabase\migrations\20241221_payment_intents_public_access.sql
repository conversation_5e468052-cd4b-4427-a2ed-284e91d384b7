-- Migration: Add public access policy for payment intents
-- This allows unauthenticated users to view payment links

-- Drop existing restrictive policy
DROP POLICY IF EXISTS "Merchants can manage their own payment intents" ON payment_intents;

-- Create new policies that allow public access for viewing payment intents
-- but still restrict creation/modification to authenticated merchants

-- Policy 1: Allow public read access to payment intents (for payment links)
CREATE POLICY "Public can view payment intents" ON payment_intents
  FOR SELECT USING (true);

-- Policy 2: Only authenticated merchants can create payment intents
CREATE POLICY "Merchants can create their own payment intents" ON payment_intents
  FOR INSERT WITH CHECK (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

-- Policy 3: Only authenticated merchants can update their own payment intents
CREATE POLICY "Merchants can update their own payment intents" ON payment_intents
  FOR UPDATE USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

-- Policy 4: Only authenticated merchants can delete their own payment intents
CREATE POLICY "Merchants can delete their own payment intents" ON payment_intents
  FOR DELETE USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );

-- Also add public read access to payment_gateway_merchants table
-- so that payment pages can display merchant information
DROP POLICY IF EXISTS "Payment gateway merchants can manage their own data" ON payment_gateway_merchants;

-- Policy 1: Allow public read access to merchant info (for payment pages)
CREATE POLICY "Public can view merchant info" ON payment_gateway_merchants
  FOR SELECT USING (true);

-- Policy 2: Only authenticated users can create/update/delete their own merchant data
CREATE POLICY "Users can manage their own merchant data" ON payment_gateway_merchants
  FOR ALL USING (user_id = auth.uid());

-- Add public read access to merchant_wallet_configs table
-- so that payment pages can display available payment methods
ALTER TABLE merchant_wallet_configs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public can view merchant wallet configs" ON merchant_wallet_configs
  FOR SELECT USING (true);

CREATE POLICY "Merchants can manage their own wallet configs" ON merchant_wallet_configs
  FOR ALL USING (
    payment_gateway_merchant_id IN (
      SELECT id FROM payment_gateway_merchants WHERE user_id = auth.uid()
    )
  );
