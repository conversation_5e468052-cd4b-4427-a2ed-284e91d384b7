/**
 * ID Generation Utilities
 * 
 * Generates unique IDs for payment intents, payment links, etc.
 */

import crypto from 'crypto';

/**
 * Generate a payment intent ID (pi_...)
 */
export const generatePaymentIntentId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomBytes = crypto.randomBytes(8).toString('hex');
  return `pi_${timestamp}_${randomBytes}`;
};

/**
 * Generate a client secret for payment intent
 */
export const generateClientSecret = (paymentIntentId: string): string => {
  const randomBytes = crypto.randomBytes(16).toString('hex');
  return `${paymentIntentId}_secret_${randomBytes}`;
};

/**
 * Generate a payment link ID (plink_...)
 */
export const generatePaymentLinkId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomBytes = crypto.randomBytes(6).toString('hex');
  return `plink_${timestamp}_${randomBytes}`;
};

/**
 * Generate a URL slug for payment links
 */
export const generateUrlSlug = (title?: string): string => {
  if (title) {
    // Create slug from title
    const baseSlug = title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    
    // Add random suffix to ensure uniqueness
    const randomSuffix = crypto.randomBytes(4).toString('hex');
    return `${baseSlug}-${randomSuffix}`;
  }
  
  // Generate random slug
  const randomBytes = crypto.randomBytes(8).toString('hex');
  return `payment-${randomBytes}`;
};

/**
 * Generate API key
 */
export const generateApiKey = (keyType: 'test' | 'live', keyPrefix: 'pk' | 'sk'): string => {
  const randomBytes = crypto.randomBytes(24).toString('hex');
  return `${keyPrefix}_${keyType}_${randomBytes}`;
};

/**
 * Generate webhook secret
 */
export const generateWebhookSecret = (): string => {
  const randomBytes = crypto.randomBytes(32).toString('hex');
  return `whsec_${randomBytes}`;
};

/**
 * Generate payment method ID (pm_...)
 */
export const generatePaymentMethodId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomBytes = crypto.randomBytes(6).toString('hex');
  return `pm_${timestamp}_${randomBytes}`;
};

/**
 * Hash API key for storage
 */
export const hashApiKey = (apiKey: string): string => {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
};

/**
 * Generate webhook signature
 */
export const generateWebhookSignature = (payload: string, secret: string): string => {
  const timestamp = Math.floor(Date.now() / 1000);
  const signedPayload = `${timestamp}.${payload}`;
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
};

/**
 * Verify webhook signature
 */
export const verifyWebhookSignature = (
  payload: string,
  signature: string,
  secret: string,
  tolerance: number = 300 // 5 minutes
): boolean => {
  try {
    const elements = signature.split(',');
    let timestamp: number | null = null;
    let v1Signature: string | null = null;

    for (const element of elements) {
      const [key, value] = element.split('=');
      if (key === 't') {
        timestamp = parseInt(value, 10);
      } else if (key === 'v1') {
        v1Signature = value;
      }
    }

    if (!timestamp || !v1Signature) {
      return false;
    }

    // Check timestamp tolerance
    const now = Math.floor(Date.now() / 1000);
    if (Math.abs(now - timestamp) > tolerance) {
      return false;
    }

    // Verify signature
    const signedPayload = `${timestamp}.${payload}`;
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(signedPayload, 'utf8')
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(v1Signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    return false;
  }
};
