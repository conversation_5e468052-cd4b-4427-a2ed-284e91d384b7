/**
 * Test script for cross-chain bridge functionality
 */

import { CrossChainBridgeService } from '../services/crossChainBridgeService';
import { CrossChainOffRampService } from '../services/crossChainOffRampService';
import { SupportedChain } from '../types/crossChain';

async function testBridgeProviders() {
  console.log('🧪 Testing Cross-Chain Bridge Providers...\n');

  // Test Wormhole
  console.log('🌀 Testing Wormhole Bridge...');
  try {
    const wormholeQuotes = await CrossChainBridgeService.getBridgeQuotes(
      SupportedChain.SOLANA,
      SupportedChain.ETHEREUM,
      'USDC',
      'USDC',
      '100'
    );
    console.log('✅ Wormhole quotes:', wormholeQuotes.length);
    if (wormholeQuotes.length > 0) {
      console.log('   Best quote:', wormholeQuotes[0]);
    }
  } catch (error) {
    console.error('❌ Wormhole test failed:', error);
  }

  // Test LayerZero
  console.log('\n⚡ Testing LayerZero Bridge...');
  try {
    const layerZeroQuotes = await CrossChainBridgeService.getBridgeQuotes(
      SupportedChain.ETHEREUM,
      SupportedChain.POLYGON,
      'USDC',
      'USDC',
      '100'
    );
    console.log('✅ LayerZero quotes:', layerZeroQuotes.length);
    if (layerZeroQuotes.length > 0) {
      console.log('   Best quote:', layerZeroQuotes[0]);
    }
  } catch (error) {
    console.error('❌ LayerZero test failed:', error);
  }

  // Test Allbridge
  console.log('\n🌉 Testing Allbridge...');
  try {
    const allbridgeQuotes = await CrossChainBridgeService.getBridgeQuotes(
      SupportedChain.SOLANA,
      SupportedChain.POLYGON,
      'USDC',
      'USDC',
      '100'
    );
    console.log('✅ Allbridge quotes:', allbridgeQuotes.length);
    if (allbridgeQuotes.length > 0) {
      console.log('   Best quote:', allbridgeQuotes[0]);
    }
  } catch (error) {
    console.error('❌ Allbridge test failed:', error);
  }
}

async function testCrossChainOffRamp() {
  console.log('\n💰 Testing Cross-Chain Off-Ramp...\n');

  try {
    const quotes = await CrossChainOffRampService.getOffRampQuotes(
      'test-user-id',
      'NGN'
    );
    console.log('✅ Off-ramp quotes:', quotes.length);
    
    if (quotes.length > 0) {
      console.log('   Sample quote:', quotes[0]);
    }
  } catch (error) {
    console.error('❌ Cross-chain off-ramp test failed:', error);
  }
}

async function runTests() {
  console.log('🚀 Starting Cross-Chain Bridge Tests\n');
  console.log('=' .repeat(50));
  
  await testBridgeProviders();
  await testCrossChainOffRamp();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ Tests completed!');
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  runTests().catch(console.error);
}

export { runTests, testBridgeProviders, testCrossChainOffRamp };
