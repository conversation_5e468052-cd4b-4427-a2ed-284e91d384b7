/**
 * Payment Intent Management Component
 * 
 * Interface for merchants to create and manage payment intents
 * for their crypto payment gateway integration
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Plus,
  Copy,
  Eye,
  QrCode,
  ExternalLink,
  CreditCard,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react';
import { PaymentGatewayService, PaymentIntent, PaymentIntentCreateRequest } from '@/services/paymentGatewayService';

interface PaymentIntentManagementProps {
  merchantId: string;
}

const PaymentIntentManagement: React.FC<PaymentIntentManagementProps> = ({ merchantId }) => {
  const [paymentIntents, setPaymentIntents] = useState<PaymentIntent[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newPaymentIntent, setNewPaymentIntent] = useState<PaymentIntent | null>(null);
  const { toast } = useToast();

  // Form state for creating payment intent
  const [formData, setFormData] = useState<PaymentIntentCreateRequest>({
    amount: 0,
    currency: 'USD',
    accepted_cryptocurrencies: ['SOL', 'USDC-SOL'],
    settlement_currency: 'NGN',
    metadata: {},
    description: '',
    success_url: '',
    cancel_url: '',
    customer_email: ''
  });

  useEffect(() => {
    loadPaymentIntents();
  }, [merchantId]);

  const loadPaymentIntents = async () => {
    setLoading(true);
    try {
      // This would call an API to list payment intents
      // For now, we'll simulate with empty array
      setPaymentIntents([]);
    } catch (error) {
      console.error('Error loading payment intents:', error);
      toast({
        title: "Error",
        description: "Failed to load payment intents",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createPaymentIntent = async () => {
    if (!formData.amount || formData.amount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid amount",
        variant: "destructive",
      });
      return;
    }

    setCreating(true);
    try {
      const result = await PaymentGatewayService.createPaymentIntent(merchantId, formData);

      if (result.success && result.payment_intent) {
        setNewPaymentIntent(result.payment_intent);
        setShowCreateDialog(false);
        await loadPaymentIntents();
        
        // Reset form
        setFormData({
          amount: 0,
          currency: 'USD',
          accepted_cryptocurrencies: ['SOL', 'USDC-SOL'],
          settlement_currency: 'NGN',
          metadata: {},
          description: '',
          success_url: '',
          cancel_url: '',
          customer_email: ''
        });

        toast({
          title: "Payment Intent Created! 🎉",
          description: "Your payment intent has been created successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create payment intent",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);
      toast({
        title: "Error",
        description: "Failed to create payment intent",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied! 📋",
      description: `${label} copied to clipboard`,
    });
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'requires_payment_method':
        return 'bg-yellow-100 text-yellow-800';
      case 'canceled':
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'succeeded':
        return <CheckCircle className="h-4 w-4" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'requires_payment_method':
        return <Clock className="h-4 w-4" />;
      case 'canceled':
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Payment Intents</h2>
          <p className="text-gray-600">Create and manage payment requests for your customers</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Payment Intent
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Payment Intent</DialogTitle>
              <DialogDescription>
                Create a payment request that your customers can pay with cryptocurrency
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              {/* Amount and Currency */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="amount">Amount</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.amount || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="NGN">NGN</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Payment for..."
                  rows={3}
                />
              </div>

              {/* Accepted Cryptocurrencies by Chain */}
              <div>
                <Label>Accepted Cryptocurrencies</Label>
                <div className="space-y-4 mt-2">
                  {/* Solana */}
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <img src="/solana.png" alt="Solana" className="h-5 w-5" />
                      <span className="font-medium">Solana Network</span>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {['SOL', 'USDC-SOL', 'USDT-SOL'].map((crypto) => (
                        <div key={crypto} className="flex items-center space-x-2">
                          <Checkbox
                            id={crypto}
                            checked={formData.accepted_cryptocurrencies?.includes(crypto)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setFormData(prev => ({
                                  ...prev,
                                  accepted_cryptocurrencies: [...(prev.accepted_cryptocurrencies || []), crypto]
                                }));
                              } else {
                                setFormData(prev => ({
                                  ...prev,
                                  accepted_cryptocurrencies: prev.accepted_cryptocurrencies?.filter(c => c !== crypto) || []
                                }));
                              }
                            }}
                          />
                          <Label htmlFor={crypto} className="text-sm font-medium">
                            {crypto.replace('-SOL', '')}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Ethereum */}
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <img src="/ethereum.png" alt="Ethereum" className="h-5 w-5" />
                      <span className="font-medium">Ethereum Network</span>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {['ETH', 'USDC-ETH', 'USDT-ETH'].map((crypto) => (
                        <div key={crypto} className="flex items-center space-x-2">
                          <Checkbox
                            id={crypto}
                            checked={formData.accepted_cryptocurrencies?.includes(crypto)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setFormData(prev => ({
                                  ...prev,
                                  accepted_cryptocurrencies: [...(prev.accepted_cryptocurrencies || []), crypto]
                                }));
                              } else {
                                setFormData(prev => ({
                                  ...prev,
                                  accepted_cryptocurrencies: prev.accepted_cryptocurrencies?.filter(c => c !== crypto) || []
                                }));
                              }
                            }}
                          />
                          <Label htmlFor={crypto} className="text-sm font-medium">
                            {crypto.replace('-ETH', '')}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Polygon */}
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <img src="/polygon.png" alt="Polygon" className="h-5 w-5" />
                      <span className="font-medium">Polygon Network</span>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {['MATIC', 'USDC-MATIC', 'USDT-MATIC'].map((crypto) => (
                        <div key={crypto} className="flex items-center space-x-2">
                          <Checkbox
                            id={crypto}
                            checked={formData.accepted_cryptocurrencies?.includes(crypto)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setFormData(prev => ({
                                  ...prev,
                                  accepted_cryptocurrencies: [...(prev.accepted_cryptocurrencies || []), crypto]
                                }));
                              } else {
                                setFormData(prev => ({
                                  ...prev,
                                  accepted_cryptocurrencies: prev.accepted_cryptocurrencies?.filter(c => c !== crypto) || []
                                }));
                              }
                            }}
                          />
                          <Label htmlFor={crypto} className="text-sm font-medium">
                            {crypto.replace('-MATIC', '')}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Settlement Currency */}
              <div>
                <Label htmlFor="settlement">Settlement Currency</Label>
                <Select 
                  value={formData.settlement_currency} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, settlement_currency: value as 'NGN' | 'crypto' }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="NGN">Nigerian Naira (NGN)</SelectItem>
                    <SelectItem value="crypto">Keep as Cryptocurrency</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* URLs */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="success_url">Success URL (Optional)</Label>
                  <Input
                    id="success_url"
                    type="url"
                    value={formData.success_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, success_url: e.target.value }))}
                    placeholder="https://yoursite.com/success"
                  />
                </div>
                <div>
                  <Label htmlFor="cancel_url">Cancel URL (Optional)</Label>
                  <Input
                    id="cancel_url"
                    type="url"
                    value={formData.cancel_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, cancel_url: e.target.value }))}
                    placeholder="https://yoursite.com/cancel"
                  />
                </div>
              </div>

              {/* Customer Email */}
              <div>
                <Label htmlFor="customer_email">Customer Email (Optional)</Label>
                <Input
                  id="customer_email"
                  type="email"
                  value={formData.customer_email}
                  onChange={(e) => setFormData(prev => ({ ...prev, customer_email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={createPaymentIntent} disabled={creating}>
                  {creating ? 'Creating...' : 'Create Payment Intent'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* New Payment Intent Display Dialog */}
      {newPaymentIntent && (
        <Dialog open={!!newPaymentIntent} onOpenChange={() => setNewPaymentIntent(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Intent Created
              </DialogTitle>
              <DialogDescription>
                Share this payment link with your customer or integrate it into your application
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>Payment Intent ID</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={newPaymentIntent.id}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(newPaymentIntent.id, 'Payment Intent ID')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label>Payment URL</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={newPaymentIntent.payment_url}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(newPaymentIntent.payment_url, 'Payment URL')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(newPaymentIntent.payment_url, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {newPaymentIntent.qr_code && (
                <div className="text-center">
                  <Label>QR Code</Label>
                  <div className="mt-2">
                    <img 
                      src={newPaymentIntent.qr_code} 
                      alt="Payment QR Code" 
                      className="mx-auto w-48 h-48 border rounded-lg"
                    />
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Amount:</span> {formatAmount(newPaymentIntent.amount, newPaymentIntent.currency)}
                </div>
                <div>
                  <span className="font-medium">Status:</span> 
                  <Badge className={`ml-2 ${getStatusColor(newPaymentIntent.status)}`}>
                    {newPaymentIntent.status.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Payment Intents List */}
      {paymentIntents.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <CreditCard className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Intents</h3>
            <p className="text-gray-600 text-center mb-4">
              Create your first payment intent to start accepting crypto payments
            </p>
            <Button onClick={() => setShowCreateDialog(true)}>
              Create Payment Intent
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {paymentIntents.map((intent) => (
            <Card key={intent.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{intent.id}</CardTitle>
                    <CardDescription>
                      {formatAmount(intent.amount, intent.currency)} • Created {new Date(intent.created_at).toLocaleDateString()}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(intent.status)}>
                      {getStatusIcon(intent.status)}
                      <span className="ml-1">{intent.status.replace('_', ' ')}</span>
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(intent.payment_url, 'Payment URL')}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(intent.payment_url, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default PaymentIntentManagement;
