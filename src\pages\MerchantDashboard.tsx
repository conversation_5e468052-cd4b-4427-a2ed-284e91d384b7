import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import QRCode from 'qrcode';
import MerchantOverview from '@/components/merchant/MerchantOverview';
import {
  Store,
  QrCode,
  Download,
  Share2,
  Copy,
  CheckCircle,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  Trash2,
  AlertTriangle,
  Settings,
  History,
  DollarSign,
  Key,
  Link
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON>ertDialog<PERSON><PERSON><PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface MerchantAccount {
  id: string;
  business_name: string;
  business_type: string;
  business_description: string;
  business_address: string;
  business_phone: string;
  business_email: string;
  bank_name: string;
  account_number: string;
  account_name: string;
  qr_code_id: string;
  accepts_sol: boolean;
  accepts_usdc: boolean;
  min_payment_amount: number;
  max_payment_amount: number;
  is_verified: boolean;
  created_at: string;
  collection_preference: 'naira' | 'crypto';
  crypto_wallet_address?: string;
}

const MerchantDashboard: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [merchant, setMerchant] = useState<MerchantAccount | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadMerchantAccount();
    }
  }, [user]);

  const loadMerchantAccount = async () => {
    try {
      const { data, error } = await supabase
        .from('merchant_accounts')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (error) {
        console.error('Error loading merchant account:', error);
        return;
      }

      if (data) {
        setMerchant(data);
        await generateQRCode(data);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateQRCode = async (merchantData: MerchantAccount) => {
    try {
      const paymentUrl = `${window.location.origin}/pay/${merchantData.qr_code_id}`;
      const qrCodeDataUrl = await QRCode.toDataURL(paymentUrl, {
        width: 300,
        margin: 2,
        color: { dark: '#000000', light: '#FFFFFF' }
      });
      setQrCodeUrl(qrCodeDataUrl);
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeUrl || !merchant) return;
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `${merchant.business_name.replace(/\s+/g, '_')}_QR_Code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast({
      title: "QR Code Downloaded! 📱",
      description: "You can now print and display your QR code",
    });
  };

  const copyPaymentLink = () => {
    if (!merchant) return;
    const paymentUrl = `${window.location.origin}/pay/${merchant.qr_code_id}`;
    navigator.clipboard.writeText(paymentUrl);
    toast({
      title: "Payment Link Copied! 🔗",
      description: "Share this link with customers",
    });
  };

  const shareQRCode = async () => {
    if (!merchant) return;
    const paymentUrl = `${window.location.origin}/pay/${merchant.qr_code_id}`;
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Pay ${merchant.business_name}`,
          text: `Scan to pay ${merchant.business_name} with crypto`,
          url: paymentUrl,
        });
      } catch (error) {
        copyPaymentLink();
      }
    } else {
      copyPaymentLink();
    }
  };

  const deleteMerchantAccount = async () => {
    if (!merchant || !user) {
      console.error('Missing merchant or user data');
      return;
    }

    try {
      console.log('🗑️ Starting merchant account deletion...', {
        merchantId: merchant.id,
        userId: user.id,
        businessName: merchant.business_name
      });

      // First, verify the merchant exists and belongs to the user
      const { data: existingMerchant, error: checkError } = await supabase
        .from('merchant_accounts')
        .select('id, user_id, business_name')
        .eq('id', merchant.id)
        .eq('user_id', user.id)
        .single();

      if (checkError || !existingMerchant) {
        console.error('❌ Merchant not found or access denied:', checkError);
        toast({
          title: "Deletion Failed",
          description: "Merchant account not found or you don't have permission.",
          variant: "destructive",
        });
        return;
      }

      console.log('✅ Merchant verified, proceeding with deletion:', existingMerchant);

      // Delete related payment records
      const { error: paymentsError } = await supabase
        .from('merchant_crypto_payments')
        .delete()
        .eq('merchant_id', merchant.id);

      if (paymentsError) {
        console.warn('⚠️ Warning: Could not delete payment records:', paymentsError);
        // Continue with deletion even if payments can't be deleted
      } else {
        console.log('✅ Payment records deleted successfully');
      }

      // Delete the merchant account
      const { data, error } = await supabase
        .from('merchant_accounts')
        .delete()
        .eq('id', merchant.id)
        .eq('user_id', user.id) // Extra security check
        .select(); // Return deleted rows to confirm deletion

      console.log('🗑️ Deletion result:', { data, error });

      if (error) {
        console.error('❌ Error deleting merchant account:', error);
        toast({
          title: "Deletion Failed",
          description: `Failed to delete merchant account: ${error.message}`,
          variant: "destructive",
        });
        return;
      }

      if (!data || data.length === 0) {
        console.error('❌ No rows were deleted - merchant may not exist or belong to user');
        toast({
          title: "Deletion Failed",
          description: "Merchant account not found or you don't have permission to delete it.",
          variant: "destructive",
        });
        return;
      }

      console.log('✅ Merchant account deleted successfully:', data);

      toast({
        title: "Account Deleted Successfully! 🗑️",
        description: "Your merchant account has been permanently deleted.",
      });

      // Clear any cached data
      localStorage.removeItem('merchantData');

      // Redirect to home page after a short delay
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);

    } catch (error) {
      console.error('❌ Error deleting merchant account:', error);
      toast({
        title: "Deletion Failed",
        description: "An error occurred while deleting your account.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto p-4">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading merchant dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!merchant) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto p-4">
          <Card>
            <CardContent className="p-8 text-center">
              <Store className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">No Merchant Account Found</h2>
              <p className="text-gray-600 mb-4">
                You don't have a merchant account yet. Register to start accepting crypto payments.
              </p>
              <Button onClick={() => window.location.href = '/merchant-register'}>
                Register as Merchant
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-6xl mx-auto p-4 space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">{merchant.business_name}</h1>
              <p className="text-blue-100">{merchant.business_type}</p>
            </div>
            <div className="flex items-center gap-3">
              <Badge className={merchant.is_verified ? "bg-green-500" : "bg-yellow-500"}>
                {merchant.is_verified ? "✅ Verified" : "⏳ Pending"}
              </Badge>

              {/* Settings/Delete Button */}
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                      Delete Merchant Account
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      <div className="space-y-3">
                        <div>Are you sure you want to permanently delete your merchant account?</div>
                        <div className="bg-red-50 p-3 rounded-lg border border-red-200">
                          <div className="text-red-800 font-medium text-sm">This action cannot be undone. This will:</div>
                          <ul className="text-red-700 text-sm mt-2 space-y-1">
                            <li>• Delete your business information</li>
                            <li>• Disable your QR code</li>
                            <li>• Remove all payment history</li>
                            <li>• Stop all future payments</li>
                          </ul>
                        </div>
                        <div className="text-sm text-gray-600">
                          Business: <strong>{merchant.business_name}</strong>
                        </div>
                      </div>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={deleteMerchantAccount}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Account
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </div>

        {/* Main Dashboard Tabs */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="qr-code">QR Code</TabsTrigger>
            <TabsTrigger value="business">Business Info</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <MerchantOverview merchantId={merchant.id} businessName={merchant.business_name} />
          </TabsContent>

          {/* QR Code Tab */}
          <TabsContent value="qr-code" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* QR Code */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                Payment QR Code
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              {qrCodeUrl && (
                <div className="bg-white p-4 rounded-lg border inline-block">
                  <img src={qrCodeUrl} alt="Payment QR Code" className="w-64 h-64" />
                </div>
              )}
              
              <div>
                <h3 className="font-semibold text-lg">{merchant.business_name}</h3>
                <p className="text-sm text-gray-600">Scan to pay with crypto</p>
              </div>

              <div className="flex gap-2 justify-center">
                <Button onClick={downloadQRCode} variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button onClick={shareQRCode} variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
                <Button onClick={copyPaymentLink} variant="outline" size="sm">
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Link
                </Button>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3 mt-4">
                <Button
                  onClick={() => window.location.href = '/merchant-payments'}
                  variant="outline"
                  className="w-full border-green-200 text-green-700 hover:bg-green-50"
                >
                  <History className="h-4 w-4 mr-2" />
                  View Payment History
                </Button>

                <Button
                  onClick={() => window.location.href = '/merchant-api-keys'}
                  variant="outline"
                  className="w-full border-blue-200 text-blue-700 hover:bg-blue-50"
                >
                  <Key className="h-4 w-4 mr-2" />
                  Manage API Keys
                </Button>

                <Button
                  onClick={() => window.location.href = '/merchant-payment-intents'}
                  variant="outline"
                  className="w-full border-purple-200 text-purple-700 hover:bg-purple-50"
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Payment Intents
                </Button>

                <Button
                  onClick={() => window.location.href = '/merchant-payment-links'}
                  variant="outline"
                  className="w-full border-orange-200 text-orange-700 hover:bg-orange-50"
                >
                  <Link className="h-4 w-4 mr-2" />
                  Payment Links
                </Button>
              </div>
            </CardContent>
          </Card>
            </div>
          </TabsContent>

          {/* Business Info Tab */}
          <TabsContent value="business" className="space-y-6">
            <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Store className="h-5 w-5" />
                Business Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{merchant.business_name}</h3>
                <p className="text-gray-600">{merchant.business_type}</p>
              </div>

              {merchant.business_description && (
                <div>
                  <p className="text-sm text-gray-600">{merchant.business_description}</p>
                </div>
              )}

              <div className="space-y-2">
                {merchant.business_address && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span>{merchant.business_address}</span>
                  </div>
                )}
                
                {merchant.business_phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{merchant.business_phone}</span>
                  </div>
                )}
                
                {merchant.business_email && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span>{merchant.business_email}</span>
                  </div>
                )}
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center gap-2 text-sm mb-2">
                  <CreditCard className="h-4 w-4 text-gray-400" />
                  <span className="font-medium">Bank Account</span>
                </div>
                <p className="text-sm">{merchant.bank_name}</p>
                <p className="text-sm">{merchant.account_number} - {merchant.account_name}</p>
              </div>
            </CardContent>
          </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
          <CardHeader>
            <CardTitle>Payment Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Collection Method */}
              <div className="text-center">
                <h4 className="font-medium mb-3">Collection Method</h4>
                <Badge className={`text-lg px-4 py-2 ${merchant.collection_preference === 'crypto' ? 'bg-purple-500 hover:bg-purple-600' : 'bg-blue-500 hover:bg-blue-600'} text-white`}>
                  {merchant.collection_preference === 'crypto' ? '🪙 Direct Crypto Collection' : '💰 Convert to Naira'}
                </Badge>
                {merchant.collection_preference === 'crypto' && merchant.crypto_wallet_address && (
                  <div className="mt-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
                    <p className="text-sm font-medium text-purple-800">Your Wallet Address:</p>
                    <p className="text-xs font-mono text-purple-600 mt-1">
                      {merchant.crypto_wallet_address.slice(0, 12)}...{merchant.crypto_wallet_address.slice(-12)}
                    </p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Accepted Cryptocurrencies</h4>
                  <div className="flex gap-2">
                    {(merchant.accepts_sol ?? true) && (
                      <Badge variant="outline">SOL</Badge>
                    )}
                    {(merchant.accepts_usdc ?? true) && (
                      <Badge variant="outline">USDC</Badge>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Minimum Payment</h4>
                  <p className="text-lg font-semibold">₦{(merchant.min_payment_amount || 0).toLocaleString()}</p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Maximum Payment</h4>
                  <p className="text-lg font-semibold">₦{(merchant.max_payment_amount || 0).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default MerchantDashboard;
