/**
 * QR Code Generator Utility
 * 
 * Generates QR codes for payment URLs and crypto addresses
 */

// Note: In a real implementation, you would use a QR code library like 'qrcode'
// For now, we'll create a simple mock implementation

/**
 * Generate QR code for payment URL
 */
export const generateQRCode = async (data: string): Promise<string> => {
  try {
    // In production, use a real QR code library:
    // const QRCode = require('qrcode');
    // return await QRCode.toDataURL(data, { width: 256, margin: 2 });
    
    // Mock implementation - returns a base64 encoded placeholder
    return generateMockQRCode(data);
  } catch (error) {
    console.error('Error generating QR code:', error);
    return generateMockQRCode(data);
  }
};

/**
 * Generate QR code for crypto payment with amount
 */
export const generateCryptoQRCode = async (
  address: string,
  amount: number,
  cryptocurrency: string
): Promise<string> => {
  try {
    let qrData = '';

    // Generate appropriate URI scheme for each cryptocurrency
    switch (cryptocurrency.toLowerCase()) {
      case 'bitcoin':
      case 'btc':
        qrData = `bitcoin:${address}?amount=${amount}`;
        break;
      case 'ethereum':
      case 'eth':
        qrData = `ethereum:${address}?value=${amount}`;
        break;
      case 'solana':
      case 'sol':
        qrData = `solana:${address}?amount=${amount}`;
        break;
      case 'usdc':
        // USDC on Ethereum
        qrData = `ethereum:${address}?value=${amount}`;
        break;
      default:
        qrData = `${cryptocurrency}:${address}?amount=${amount}`;
    }

    return await generateQRCode(qrData);
  } catch (error) {
    console.error('Error generating crypto QR code:', error);
    return generateMockQRCode(`${cryptocurrency}:${address}?amount=${amount}`);
  }
};

/**
 * Generate QR code for payment link
 */
export const generatePaymentLinkQRCode = async (paymentUrl: string): Promise<string> => {
  return await generateQRCode(paymentUrl);
};

/**
 * Mock QR code generator (placeholder implementation)
 */
const generateMockQRCode = (data: string): string => {
  // Create a simple SVG QR code placeholder
  const size = 256;
  const cellSize = size / 25; // 25x25 grid
  
  // Generate a deterministic pattern based on the data
  const hash = simpleHash(data);
  const pattern = generatePattern(hash, 25);
  
  let svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">`;
  svg += `<rect width="${size}" height="${size}" fill="white"/>`;
  
  // Draw the pattern
  for (let y = 0; y < 25; y++) {
    for (let x = 0; x < 25; x++) {
      if (pattern[y][x]) {
        const rectX = x * cellSize;
        const rectY = y * cellSize;
        svg += `<rect x="${rectX}" y="${rectY}" width="${cellSize}" height="${cellSize}" fill="black"/>`;
      }
    }
  }
  
  svg += '</svg>';
  
  // Convert SVG to base64 data URL
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
};

/**
 * Simple hash function for generating deterministic patterns
 */
const simpleHash = (str: string): number => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
};

/**
 * Generate a pattern for the mock QR code
 */
const generatePattern = (seed: number, size: number): boolean[][] => {
  const pattern: boolean[][] = [];
  let rng = seed;
  
  // Simple linear congruential generator for deterministic randomness
  const next = () => {
    rng = (rng * 1664525 + 1013904223) % Math.pow(2, 32);
    return rng / Math.pow(2, 32);
  };
  
  for (let y = 0; y < size; y++) {
    pattern[y] = [];
    for (let x = 0; x < size; x++) {
      // Add finder patterns (corners)
      if ((x < 7 && y < 7) || (x >= size - 7 && y < 7) || (x < 7 && y >= size - 7)) {
        // Finder pattern
        const inBorder = x === 0 || x === 6 || y === 0 || y === 6;
        const inCenter = x >= 2 && x <= 4 && y >= 2 && y <= 4;
        pattern[y][x] = inBorder || inCenter;
      } else {
        // Random pattern for data
        pattern[y][x] = next() > 0.5;
      }
    }
  }
  
  return pattern;
};

/**
 * Validate QR code data
 */
export const validateQRData = (data: string): boolean => {
  if (!data || data.length === 0) {
    return false;
  }
  
  // Check maximum data length (QR codes have limits)
  if (data.length > 2953) { // Alphanumeric mode limit
    return false;
  }
  
  return true;
};

/**
 * Get QR code options for different use cases
 */
export const getQRCodeOptions = (type: 'payment' | 'url' | 'crypto') => {
  const baseOptions = {
    width: 256,
    height: 256,
    margin: 2,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    }
  };
  
  switch (type) {
    case 'payment':
      return {
        ...baseOptions,
        errorCorrectionLevel: 'M' as const, // Medium error correction
        type: 'image/png' as const
      };
    case 'crypto':
      return {
        ...baseOptions,
        errorCorrectionLevel: 'H' as const, // High error correction for crypto addresses
        type: 'image/png' as const
      };
    case 'url':
      return {
        ...baseOptions,
        errorCorrectionLevel: 'L' as const, // Low error correction for URLs
        type: 'image/png' as const
      };
    default:
      return baseOptions;
  }
};
