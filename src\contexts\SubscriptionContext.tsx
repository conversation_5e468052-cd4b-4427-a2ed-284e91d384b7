
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Subscription, SubscriptionPlan, SubscriptionStatus } from '../types/subscription';
import {
  getAvailableSubscriptions,
  getUserSubscriptions,
  subscribeToService,
  cancelSubscription,
  changeSubscriptionPlan
} from '../services/subscriptionService';
import { toast } from '@/hooks/use-toast';

interface UserSubscriptionData {
  subscription: Subscription;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  nextPayment: Date;
  startDate: Date;
}

interface SubscriptionContextType {
  availableSubscriptions: Subscription[];
  userSubscriptions: UserSubscriptionData[];
  isLoading: boolean;
  error: Error | null;
  refreshSubscriptions: () => Promise<void>;
  subscribe: (subscriptionId: string, planId: string, paymentMethod: string, isRecurring: boolean) => Promise<boolean>;
  cancel: (subscriptionId: string) => Promise<boolean>;
  changePlan: (subscriptionId: string, newPlanId: string) => Promise<boolean>;
  getSubscriptionById: (id: string) => Subscription | undefined;
  getPlanById: (subscriptionId: string, planId: string) => SubscriptionPlan | undefined;
  getMonthlySpend: () => number;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export function useSubscription() {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
}

interface SubscriptionProviderProps {
  children: ReactNode;
}

export function SubscriptionProvider({ children }: SubscriptionProviderProps) {
  const [availableSubscriptions, setAvailableSubscriptions] = useState<Subscription[]>([]);
  const [userSubscriptions, setUserSubscriptions] = useState<UserSubscriptionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // State to track if we've already tried to refresh
  const [hasTriedRefresh, setHasTriedRefresh] = useState(false);

  // Load subscriptions on mount
  useEffect(() => {
    // Only try to refresh once to prevent infinite loops
    if (!hasTriedRefresh) {
      setHasTriedRefresh(true);
      refreshSubscriptions().catch(err => {
        console.error("Failed to initialize subscriptions:", err);
        setError(err instanceof Error ? err : new Error('Failed to initialize subscriptions'));
        setIsLoading(false);
      });
    }
  }, [hasTriedRefresh]);

  // Function to refresh subscription data
  const refreshSubscriptions = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const [fetchedAvailableSubscriptions, fetchedUserSubscriptions] = await Promise.all([
        getAvailableSubscriptions(),
        getUserSubscriptions()
      ]);

      setAvailableSubscriptions(fetchedAvailableSubscriptions);
      setUserSubscriptions(fetchedUserSubscriptions);
      return Promise.resolve();
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch subscription data'));
      toast({
        title: 'Error',
        description: 'Failed to load subscription data. Please try again.',
        variant: 'destructive',
      });
      return Promise.reject(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to subscribe to a service
  const subscribe = async (
    subscriptionId: string,
    planId: string,
    paymentMethod: string,
    isRecurring: boolean
  ): Promise<boolean> => {
    try {
      setIsLoading(true);
      const result = await subscribeToService(subscriptionId, planId, paymentMethod, isRecurring);

      if (result) {
        // Refresh subscriptions to get the updated list
        await refreshSubscriptions();

        toast({
          title: 'Subscription Added',
          description: 'You have successfully subscribed to the service.',
        });

        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to subscribe'));
      toast({
        title: 'Subscription Failed',
        description: 'Failed to subscribe to the service. Please try again.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to cancel a subscription
  const cancel = async (subscriptionId: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const result = await cancelSubscription(subscriptionId);

      if (result) {
        // Update the local state to reflect the cancellation
        setUserSubscriptions(prevSubscriptions =>
          prevSubscriptions.map(sub =>
            sub.subscription.id === subscriptionId
              ? { ...sub, status: SubscriptionStatus.CANCELED }
              : sub
          )
        );

        toast({
          title: 'Subscription Canceled',
          description: 'Your subscription has been canceled successfully.',
        });

        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to cancel subscription'));
      toast({
        title: 'Cancellation Failed',
        description: 'Failed to cancel your subscription. Please try again.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to change subscription plan
  const changePlan = async (subscriptionId: string, newPlanId: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const result = await changeSubscriptionPlan(subscriptionId, newPlanId);

      if (result) {
        // Refresh subscriptions to get the updated plan
        await refreshSubscriptions();

        toast({
          title: 'Plan Changed',
          description: 'Your subscription plan has been updated successfully.',
        });

        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to change plan'));
      toast({
        title: 'Plan Change Failed',
        description: 'Failed to update your subscription plan. Please try again.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get a subscription by ID
  const getSubscriptionById = (id: string): Subscription | undefined => {
    return availableSubscriptions.find(sub => sub.id === id);
  };

  // Helper function to get a plan by ID
  const getPlanById = (subscriptionId: string, planId: string): SubscriptionPlan | undefined => {
    const subscription = getSubscriptionById(subscriptionId);
    return subscription?.plans?.find(plan => plan.id === planId);
  };

  // Calculate total monthly spend on subscriptions
  const getMonthlySpend = (): number => {
    return userSubscriptions.reduce((total, sub) => {
      // Only count active subscriptions
      if (sub.status === SubscriptionStatus.ACTIVE) {
        return total + sub.plan.price;
      }
      return total;
    }, 0);
  };

  const value = {
    availableSubscriptions,
    userSubscriptions,
    isLoading,
    error,
    refreshSubscriptions,
    subscribe,
    cancel,
    changePlan,
    getSubscriptionById,
    getPlanById,
    getMonthlySpend,
  };

  return <SubscriptionContext.Provider value={value}>{children}</SubscriptionContext.Provider>;
}
