
/**
 * Group Card Transactions Component
 */
import React, { useState } from 'react';
import { Plus, ArrowUpRight, ArrowDownRight, Filter, Search, Receipt, DollarSign } from 'lucide-react';
import { useGroupCard } from '@/contexts/GroupCardContext';
import { GroupCardTransactionStatus } from '@/types/groupCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/hooks/use-toast';

export default function GroupCardTransactions() {
  const { 
    transactions, 
    isLoading, 
    createTransaction, 
    approveTransaction, 
    declineTransaction 
  } = useGroupCard();
  
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [amount, setAmount] = useState('');
  const [merchant, setMerchant] = useState('');
  const [category, setCategory] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Handle transaction creation
  const handleCreateTransaction = async () => {
    const parsedAmount = parseFloat(amount);
    if (isNaN(parsedAmount) || parsedAmount <= 0 || !merchant.trim()) return;

    setIsCreating(true);
    try {
      await createTransaction(parsedAmount, merchant, category || 'General');
      
      // Reset form
      setAmount('');
      setMerchant('');
      setCategory('');
      setCreateDialogOpen(false);
      
      toast({
        title: 'Transaction Created',
        description: 'Your transaction has been submitted for approval.',
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Handle transaction approval
  const handleApprove = async (transactionId: string) => {
    try {
      await approveTransaction(transactionId);
      toast({
        title: 'Transaction Approved',
        description: 'The transaction has been approved.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to approve transaction.',
        variant: 'destructive',
      });
    }
  };

  // Handle transaction decline
  const handleDecline = async (transactionId: string) => {
    try {
      await declineTransaction(transactionId);
      toast({
        title: 'Transaction Declined',
        description: 'The transaction has been declined.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to decline transaction.',
        variant: 'destructive',
      });
    }
  };

  // Filter transactions
  const filteredTransactions = transactions.filter(transaction => {
    const matchesFilter = filter === 'all' || transaction.status === filter;
    const matchesSearch = 
      transaction.merchant?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.category?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // Get status color
  const getStatusColor = (status: GroupCardTransactionStatus) => {
    switch (status) {
      case GroupCardTransactionStatus.APPROVED:
        return 'bg-green-500';
      case GroupCardTransactionStatus.DECLINED:
        return 'bg-red-500';
      case GroupCardTransactionStatus.PENDING:
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-40" />
        </div>
        <div className="grid gap-4">
          {[1, 2, 3, 4].map(i => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Transactions</h3>
          <p className="text-sm text-muted-foreground">
            View and manage all group card transactions
          </p>
        </div>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Transaction
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Transaction</DialogTitle>
              <DialogDescription>
                Submit a new transaction for group card approval.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount ($)</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="25.99"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="merchant">Merchant</Label>
                <Input
                  id="merchant"
                  value={merchant}
                  onChange={(e) => setMerchant(e.target.value)}
                  placeholder="Starbucks, Amazon, etc."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category (Optional)</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="food">Food & Dining</SelectItem>
                    <SelectItem value="shopping">Shopping</SelectItem>
                    <SelectItem value="transport">Transportation</SelectItem>
                    <SelectItem value="entertainment">Entertainment</SelectItem>
                    <SelectItem value="utilities">Utilities</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleCreateTransaction}
                disabled={isCreating || !amount || !merchant.trim()}
              >
                {isCreating ? 'Creating...' : 'Create Transaction'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={filter} onValueChange={setFilter}>
          <SelectTrigger className="w-full sm:w-40">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="declined">Declined</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Transactions List */}
      {filteredTransactions.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="pt-6 text-center">
            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted">
              <Receipt className="h-10 w-10 text-muted-foreground" />
            </div>
            <h3 className="mt-4 text-lg font-semibold">No Transactions</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {searchTerm || filter !== 'all' 
                ? 'No transactions match your search criteria.'
                : 'Create your first transaction to get started.'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredTransactions.map((transaction) => (
            <Card key={transaction.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`p-3 rounded-full ${
                      transaction.amount < 0 ? 'bg-red-100' : 'bg-green-100'
                    }`}>
                      {transaction.amount < 0 ? (
                        <ArrowUpRight className={`h-5 w-5 ${
                          transaction.amount < 0 ? 'text-red-600' : 'text-green-600'
                        }`} />
                      ) : (
                        <ArrowDownRight className="h-5 w-5 text-green-600" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">
                          {transaction.merchant || 'Unknown Merchant'}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          {transaction.category || 'Uncategorized'}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {new Date(transaction.createdAt).toLocaleDateString()} at{' '}
                        {new Date(transaction.createdAt).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className={`font-semibold ${
                        transaction.amount < 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {transaction.amount < 0 ? '-' : '+'}${Math.abs(transaction.amount).toFixed(2)}
                      </p>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${getStatusColor(transaction.status)}`} />
                        <span className="text-xs text-muted-foreground capitalize">
                          {transaction.status}
                        </span>
                      </div>
                    </div>
                    {transaction.status === GroupCardTransactionStatus.PENDING && (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleApprove(transaction.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDecline(transaction.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          Decline
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
                {transaction.description && (
                  <p className="mt-3 text-sm text-muted-foreground border-t pt-3">
                    {transaction.description}
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
