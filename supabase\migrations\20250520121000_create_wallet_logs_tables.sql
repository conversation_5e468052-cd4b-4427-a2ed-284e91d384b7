
-- Create table for wallet refresh logs
CREATE TABLE IF NOT EXISTS public.wallet_refresh_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE,
  address TEXT NOT NULL,
  changes J<PERSON><PERSON><PERSON>,
  refreshed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies for wallet_refresh_logs
ALTER TABLE public.wallet_refresh_logs ENABLE ROW LEVEL SECURITY;

-- Get wallet owner's user ID
CREATE OR REPLACE FUNCTION get_wallet_owner_id(wallet_id UUID)
RETURNS UUID AS $$
  SELECT user_id FROM wallets WHERE id = wallet_id;
$$ LANGUAGE SQL SECURITY DEFINER;

-- Policy to allow users to view their own wallet refresh logs
CREATE POLICY "Users can view their own wallet refresh logs"
  ON public.wallet_refresh_logs
  FOR SELECT
  USING (auth.uid() = get_wallet_owner_id(wallet_id));

-- Policy to allow users to insert refresh logs for their own wallets
CREATE POLICY "Users can insert their own wallet refresh logs"
  ON public.wallet_refresh_logs
  FOR INSERT
  WITH CHECK (auth.uid() = get_wallet_owner_id(wallet_id));

-- Create table for user activity logs
CREATE TABLE IF NOT EXISTS public.user_activity_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  activity_type TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies for user_activity_logs
ALTER TABLE public.user_activity_logs ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to view their own activity logs
CREATE POLICY "Users can view their own activity logs"
  ON public.user_activity_logs
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy to allow users to insert their own activity logs
CREATE POLICY "Users can insert their own activity logs"
  ON public.user_activity_logs
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create directory structure in app_assets bucket if it doesn't exist
INSERT INTO storage.objects (bucket_id, name, owner, metadata) 
VALUES ('app_assets', 'config/', auth.uid(), '{"mimetype": "application/x-directory"}')
ON CONFLICT (bucket_id, name) DO NOTHING;
