/**
 * Webhook Service
 * 
 * Handles webhook delivery for payment gateway events
 * Includes retry logic, signature verification, and delivery tracking
 */

import { supabase } from '@/integrations/supabase/client';
import crypto from 'crypto';

export interface WebhookEndpoint {
  id: string;
  merchant_id: string;
  url: string;
  events: string[];
  secret: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WebhookEvent {
  id: string;
  type: string;
  data: {
    object: any;
  };
  created: number;
}

export interface WebhookDelivery {
  id: string;
  webhook_endpoint_id: string;
  payment_intent_id?: string;
  event_type: string;
  payload: any;
  status: 'pending' | 'delivered' | 'failed';
  http_status_code?: number;
  response_body?: string;
  attempt_count: number;
  max_attempts: number;
  created_at: string;
  next_attempt_at: string;
  delivered_at?: string;
}

class WebhookService {
  
  /**
   * Create a webhook endpoint for a merchant
   */
  static async createWebhookEndpoint(
    merchantId: string,
    url: string,
    events: string[] = ['payment_intent.succeeded', 'payment_intent.payment_failed']
  ): Promise<{ success: boolean; webhook_endpoint?: WebhookEndpoint; error?: string }> {
    try {
      // Generate webhook secret
      const secret = crypto.randomBytes(32).toString('hex');

      const { data, error } = await supabase
        .from('merchant_webhook_endpoints')
        .insert({
          merchant_id: merchantId,
          url: url,
          events: events,
          secret: secret,
          is_active: true
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating webhook endpoint:', error);
        return { success: false, error: 'Failed to create webhook endpoint' };
      }

      return { success: true, webhook_endpoint: data };

    } catch (error) {
      console.error('Webhook endpoint creation error:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * List webhook endpoints for a merchant
   */
  static async listWebhookEndpoints(merchantId: string): Promise<{
    success: boolean;
    webhook_endpoints?: WebhookEndpoint[];
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('merchant_webhook_endpoints')
        .select('*')
        .eq('merchant_id', merchantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error listing webhook endpoints:', error);
        return { success: false, error: 'Failed to retrieve webhook endpoints' };
      }

      return { success: true, webhook_endpoints: data };

    } catch (error) {
      console.error('Error listing webhook endpoints:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Update webhook endpoint
   */
  static async updateWebhookEndpoint(
    endpointId: string,
    merchantId: string,
    updates: Partial<Pick<WebhookEndpoint, 'url' | 'events' | 'is_active'>>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('merchant_webhook_endpoints')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', endpointId)
        .eq('merchant_id', merchantId);

      if (error) {
        console.error('Error updating webhook endpoint:', error);
        return { success: false, error: 'Failed to update webhook endpoint' };
      }

      return { success: true };

    } catch (error) {
      console.error('Error updating webhook endpoint:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Delete webhook endpoint
   */
  static async deleteWebhookEndpoint(
    endpointId: string,
    merchantId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('merchant_webhook_endpoints')
        .delete()
        .eq('id', endpointId)
        .eq('merchant_id', merchantId);

      if (error) {
        console.error('Error deleting webhook endpoint:', error);
        return { success: false, error: 'Failed to delete webhook endpoint' };
      }

      return { success: true };

    } catch (error) {
      console.error('Error deleting webhook endpoint:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Send webhook event to all relevant endpoints
   */
  static async sendWebhookEvent(
    merchantId: string,
    eventType: string,
    eventData: any,
    paymentIntentId?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Get all active webhook endpoints for this merchant that listen to this event
      const { data: endpoints, error: endpointsError } = await supabase
        .from('merchant_webhook_endpoints')
        .select('*')
        .eq('merchant_id', merchantId)
        .eq('is_active', true)
        .contains('events', [eventType]);

      if (endpointsError) {
        console.error('Error fetching webhook endpoints:', endpointsError);
        return { success: false, error: 'Failed to fetch webhook endpoints' };
      }

      if (!endpoints || endpoints.length === 0) {
        console.log('No webhook endpoints found for event:', eventType);
        return { success: true };
      }

      // Create webhook event payload
      const webhookEvent: WebhookEvent = {
        id: `evt_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`,
        type: eventType,
        data: {
          object: eventData
        },
        created: Math.floor(Date.now() / 1000)
      };

      // Create delivery records for each endpoint
      const deliveries = endpoints.map(endpoint => ({
        webhook_endpoint_id: endpoint.id,
        payment_intent_id: paymentIntentId,
        event_type: eventType,
        payload: webhookEvent,
        status: 'pending' as const,
        attempt_count: 0,
        max_attempts: 5,
        next_attempt_at: new Date().toISOString()
      }));

      const { error: deliveryError } = await supabase
        .from('webhook_deliveries')
        .insert(deliveries);

      if (deliveryError) {
        console.error('Error creating webhook deliveries:', deliveryError);
        return { success: false, error: 'Failed to create webhook deliveries' };
      }

      // Process deliveries asynchronously
      this.processWebhookDeliveries();

      return { success: true };

    } catch (error) {
      console.error('Error sending webhook event:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Process pending webhook deliveries
   */
  static async processWebhookDeliveries(): Promise<void> {
    try {
      // Get pending deliveries that are ready to be sent
      const { data: deliveries, error } = await supabase
        .from('webhook_deliveries')
        .select(`
          *,
          webhook_endpoint:merchant_webhook_endpoints(*)
        `)
        .eq('status', 'pending')
        .lte('next_attempt_at', new Date().toISOString())
        .lt('attempt_count', 5)
        .limit(50);

      if (error) {
        console.error('Error fetching pending deliveries:', error);
        return;
      }

      if (!deliveries || deliveries.length === 0) {
        return;
      }

      // Process each delivery
      for (const delivery of deliveries) {
        await this.deliverWebhook(delivery);
      }

    } catch (error) {
      console.error('Error processing webhook deliveries:', error);
    }
  }

  /**
   * Deliver a single webhook
   */
  private static async deliverWebhook(delivery: any): Promise<void> {
    try {
      const endpoint = delivery.webhook_endpoint;
      if (!endpoint) {
        console.error('Webhook endpoint not found for delivery:', delivery.id);
        return;
      }

      // Create signature
      const signature = this.createWebhookSignature(
        JSON.stringify(delivery.payload),
        endpoint.secret
      );

      // Send webhook
      const response = await fetch(endpoint.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': signature,
          'User-Agent': 'YourPlatform-Webhooks/1.0'
        },
        body: JSON.stringify(delivery.payload)
      });

      const responseBody = await response.text();
      const isSuccess = response.status >= 200 && response.status < 300;

      // Update delivery record
      const updateData: any = {
        attempt_count: delivery.attempt_count + 1,
        http_status_code: response.status,
        response_body: responseBody.substring(0, 1000), // Limit response body size
      };

      if (isSuccess) {
        updateData.status = 'delivered';
        updateData.delivered_at = new Date().toISOString();
      } else {
        // Calculate next retry time (exponential backoff)
        const nextAttemptDelay = Math.min(
          Math.pow(2, delivery.attempt_count) * 60 * 1000, // Start with 1 minute, double each time
          24 * 60 * 60 * 1000 // Max 24 hours
        );
        
        if (delivery.attempt_count + 1 >= delivery.max_attempts) {
          updateData.status = 'failed';
        } else {
          updateData.next_attempt_at = new Date(Date.now() + nextAttemptDelay).toISOString();
        }
      }

      await supabase
        .from('webhook_deliveries')
        .update(updateData)
        .eq('id', delivery.id);

      console.log(`Webhook delivery ${delivery.id}: ${isSuccess ? 'success' : 'failed'} (${response.status})`);

    } catch (error) {
      console.error('Error delivering webhook:', error);
      
      // Update delivery record with error
      await supabase
        .from('webhook_deliveries')
        .update({
          attempt_count: delivery.attempt_count + 1,
          response_body: `Error: ${error.message}`,
          next_attempt_at: new Date(Date.now() + 5 * 60 * 1000).toISOString() // Retry in 5 minutes
        })
        .eq('id', delivery.id);
    }
  }

  /**
   * Create webhook signature for verification
   */
  private static createWebhookSignature(payload: string, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
  }

  /**
   * Verify webhook signature
   */
  static verifyWebhookSignature(
    payload: string,
    signature: string,
    secret: string
  ): boolean {
    try {
      const expectedSignature = this.createWebhookSignature(payload, secret);
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      console.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  /**
   * Get webhook delivery logs for a merchant
   */
  static async getWebhookDeliveries(
    merchantId: string,
    limit: number = 50
  ): Promise<{ success: boolean; deliveries?: WebhookDelivery[]; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('webhook_deliveries')
        .select(`
          *,
          webhook_endpoint:merchant_webhook_endpoints!inner(merchant_id)
        `)
        .eq('webhook_endpoint.merchant_id', merchantId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching webhook deliveries:', error);
        return { success: false, error: 'Failed to fetch webhook deliveries' };
      }

      return { success: true, deliveries: data };

    } catch (error) {
      console.error('Error fetching webhook deliveries:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Retry a failed webhook delivery
   */
  static async retryWebhookDelivery(
    deliveryId: string,
    merchantId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Reset delivery for retry
      const { error } = await supabase
        .from('webhook_deliveries')
        .update({
          status: 'pending',
          next_attempt_at: new Date().toISOString(),
          response_body: null,
          http_status_code: null
        })
        .eq('id', deliveryId)
        .eq('webhook_endpoint.merchant_id', merchantId);

      if (error) {
        console.error('Error retrying webhook delivery:', error);
        return { success: false, error: 'Failed to retry webhook delivery' };
      }

      // Process the delivery
      this.processWebhookDeliveries();

      return { success: true };

    } catch (error) {
      console.error('Error retrying webhook delivery:', error);
      return { success: false, error: 'Internal server error' };
    }
  }
}

// Start webhook delivery processor
if (typeof window === 'undefined') {
  // Only run on server side
  setInterval(() => {
    WebhookService.processWebhookDeliveries();
  }, 30000); // Process every 30 seconds
}

export { WebhookService };
