/**
 * API Key Management Component
 * 
 * Comprehensive interface for merchants to manage their API keys
 * for the crypto payment gateway integration
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Key,
  Copy,
  Eye,
  EyeOff,
  RefreshCw,
  Trash2,
  Plus,
  Shield,
  Activity,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { MerchantApiKeyService, MerchantApiKey, ApiKeyPair } from '@/services/merchantApiKeyService';

interface ApiKeyManagementProps {
  merchantId: string;
}

const ApiKeyManagement: React.FC<ApiKeyManagementProps> = ({ merchantId }) => {
  const [apiKeys, setApiKeys] = useState<MerchantApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [newKeyPair, setNewKeyPair] = useState<ApiKeyPair | null>(null);
  const [showSecrets, setShowSecrets] = useState<{ [key: string]: boolean }>({});
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadApiKeys();
  }, [merchantId]);

  const loadApiKeys = async () => {
    setLoading(true);
    try {
      const result = await MerchantApiKeyService.listApiKeys(merchantId);
      if (result.success && result.api_keys) {
        setApiKeys(result.api_keys);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load API keys",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading API keys:', error);
      toast({
        title: "Error",
        description: "Failed to load API keys",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createApiKey = async (keyType: 'live' | 'test') => {
    setCreating(true);
    try {
      const result = await MerchantApiKeyService.createApiKey({
        merchant_id: merchantId,
        key_type: keyType,
        permissions: ['read', 'write']
      });

      if (result.success && result.api_key_pair) {
        setNewKeyPair(result.api_key_pair);
        await loadApiKeys();
        toast({
          title: "API Key Created! 🎉",
          description: `Your ${keyType} API key has been generated successfully`,
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create API key",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating API key:', error);
      toast({
        title: "Error",
        description: "Failed to create API key",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const deactivateApiKey = async (keyId: string) => {
    try {
      const result = await MerchantApiKeyService.deactivateApiKey(keyId, merchantId);
      if (result.success) {
        await loadApiKeys();
        toast({
          title: "API Key Deactivated",
          description: "The API key has been deactivated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to deactivate API key",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error deactivating API key:', error);
      toast({
        title: "Error",
        description: "Failed to deactivate API key",
        variant: "destructive",
      });
    }
  };

  const regenerateApiKey = async (keyId: string) => {
    try {
      const result = await MerchantApiKeyService.regenerateApiKey(keyId, merchantId);
      if (result.success && result.api_key_pair) {
        setNewKeyPair(result.api_key_pair);
        await loadApiKeys();
        toast({
          title: "API Key Regenerated! 🔄",
          description: "Your API key has been regenerated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to regenerate API key",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error regenerating API key:', error);
      toast({
        title: "Error",
        description: "Failed to regenerate API key",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied! 📋",
      description: `${label} copied to clipboard`,
    });
  };

  const toggleSecretVisibility = (keyId: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getKeyTypeColor = (keyType: string) => {
    return keyType === 'live' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800';
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">API Keys</h2>
          <p className="text-gray-600">Manage your payment gateway API keys</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create API Key
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New API Key</DialogTitle>
              <DialogDescription>
                Choose the type of API key you want to create. Test keys are for development, 
                live keys are for production.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center gap-2"
                  onClick={() => {
                    createApiKey('test');
                    setShowCreateDialog(false);
                  }}
                  disabled={creating}
                >
                  <Shield className="h-6 w-6 text-blue-600" />
                  <span>Test Key</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center gap-2"
                  onClick={() => {
                    createApiKey('live');
                    setShowCreateDialog(false);
                  }}
                  disabled={creating}
                >
                  <Key className="h-6 w-6 text-green-600" />
                  <span>Live Key</span>
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* New Key Display Dialog */}
      {newKeyPair && (
        <Dialog open={!!newKeyPair} onOpenChange={() => setNewKeyPair(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Your New API Keys
              </DialogTitle>
              <DialogDescription>
                <div className="flex items-center gap-2 text-amber-600">
                  <AlertTriangle className="h-4 w-4" />
                  Save these keys securely. The secret key will not be shown again.
                </div>
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>Publishable Key</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={newKeyPair.publishable_key}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(newKeyPair.publishable_key, 'Publishable key')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div>
                <Label>Secret Key</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={newKeyPair.secret_key}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(newKeyPair.secret_key, 'Secret key')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* API Keys List */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Keys</TabsTrigger>
          <TabsTrigger value="live">Live Keys</TabsTrigger>
          <TabsTrigger value="test">Test Keys</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {apiKeys.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Key className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No API Keys</h3>
                <p className="text-gray-600 text-center mb-4">
                  Create your first API key to start accepting crypto payments
                </p>
                <Button onClick={() => setShowCreateDialog(true)}>
                  Create API Key
                </Button>
              </CardContent>
            </Card>
          ) : (
            apiKeys.map((apiKey) => (
              <ApiKeyCard
                key={apiKey.id}
                apiKey={apiKey}
                showSecret={showSecrets[apiKey.key_id] || false}
                onToggleSecret={() => toggleSecretVisibility(apiKey.key_id)}
                onCopy={copyToClipboard}
                onDeactivate={() => deactivateApiKey(apiKey.key_id)}
                onRegenerate={() => regenerateApiKey(apiKey.key_id)}
                formatDate={formatDate}
                getKeyTypeColor={getKeyTypeColor}
                getStatusColor={getStatusColor}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value="live" className="space-y-4">
          {apiKeys.filter(key => key.key_type === 'live').map((apiKey) => (
            <ApiKeyCard
              key={apiKey.id}
              apiKey={apiKey}
              showSecret={showSecrets[apiKey.key_id] || false}
              onToggleSecret={() => toggleSecretVisibility(apiKey.key_id)}
              onCopy={copyToClipboard}
              onDeactivate={() => deactivateApiKey(apiKey.key_id)}
              onRegenerate={() => regenerateApiKey(apiKey.key_id)}
              formatDate={formatDate}
              getKeyTypeColor={getKeyTypeColor}
              getStatusColor={getStatusColor}
            />
          ))}
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          {apiKeys.filter(key => key.key_type === 'test').map((apiKey) => (
            <ApiKeyCard
              key={apiKey.id}
              apiKey={apiKey}
              showSecret={showSecrets[apiKey.key_id] || false}
              onToggleSecret={() => toggleSecretVisibility(apiKey.key_id)}
              onCopy={copyToClipboard}
              onDeactivate={() => deactivateApiKey(apiKey.key_id)}
              onRegenerate={() => regenerateApiKey(apiKey.key_id)}
              formatDate={formatDate}
              getKeyTypeColor={getKeyTypeColor}
              getStatusColor={getStatusColor}
            />
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Separate component for API Key Card to keep the main component clean
interface ApiKeyCardProps {
  apiKey: MerchantApiKey;
  showSecret: boolean;
  onToggleSecret: () => void;
  onCopy: (text: string, label: string) => void;
  onDeactivate: () => void;
  onRegenerate: () => void;
  formatDate: (date: string) => string;
  getKeyTypeColor: (type: string) => string;
  getStatusColor: (isActive: boolean) => string;
}

const ApiKeyCard: React.FC<ApiKeyCardProps> = ({
  apiKey,
  showSecret,
  onToggleSecret,
  onCopy,
  onDeactivate,
  onRegenerate,
  formatDate,
  getKeyTypeColor,
  getStatusColor
}) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Key className="h-5 w-5 text-gray-600" />
            <div>
              <CardTitle className="text-lg">{apiKey.key_type.toUpperCase()} API Key</CardTitle>
              <CardDescription>
                Created {formatDate(apiKey.created_at)}
                {apiKey.last_used_at && (
                  <span className="ml-2">• Last used {formatDate(apiKey.last_used_at)}</span>
                )}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getKeyTypeColor(apiKey.key_type)}>
              {apiKey.key_type}
            </Badge>
            <Badge className={getStatusColor(apiKey.is_active)}>
              {apiKey.is_active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Publishable Key */}
        <div>
          <Label className="text-sm font-medium">Publishable Key</Label>
          <div className="flex items-center gap-2 mt-1">
            <Input
              value={apiKey.key_id}
              readOnly
              className="font-mono text-sm"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => onCopy(apiKey.key_id, 'Publishable key')}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 pt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onRegenerate}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Regenerate
          </Button>
          
          {apiKey.is_active && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                  Deactivate
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Deactivate API Key</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will immediately deactivate the API key. Any applications using this key will stop working.
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={onDeactivate} className="bg-red-600 hover:bg-red-700">
                    Deactivate
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ApiKeyManagement;
