/**
 * Telegram Bot Setup API
 * Sets up webhook and bot commands
 */

import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  const webhookUrl = process.env.TELEGRAM_WEBHOOK_URL;

  if (!botToken || !webhookUrl) {
    return res.status(500).json({ 
      error: 'Bot token or webhook URL not configured' 
    });
  }

  try {
    // Set webhook
    const webhookResponse = await fetch(`https://api.telegram.org/bot${botToken}/setWebhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message']
      }),
    });

    const webhookResult = await webhookResponse.json();
    
    if (!webhookResult.ok) {
      throw new Error(`Failed to set webhook: ${webhookResult.description}`);
    }

    // Set bot commands
    const commands = [
      { command: 'start', description: 'Start using the bot and link your account' },
      { command: 'balance', description: 'Check your crypto balances' },
      { command: 'deposit', description: 'Get deposit addresses' },
      { command: 'withdraw', description: 'Withdraw crypto to bank' },
      { command: 'rates', description: 'Get current exchange rates' },
      { command: 'pay', description: 'Pay merchant via QR' },
      { command: 'history', description: 'View transaction history' },
      { command: 'help', description: 'Show all commands' }
    ];

    const commandsResponse = await fetch(`https://api.telegram.org/bot${botToken}/setMyCommands`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        commands: commands
      }),
    });

    const commandsResult = await commandsResponse.json();
    
    if (!commandsResult.ok) {
      throw new Error(`Failed to set commands: ${commandsResult.description}`);
    }

    res.status(200).json({ 
      success: true, 
      webhook: webhookResult,
      commands: commandsResult
    });
  } catch (error) {
    console.error('Bot setup error:', error);
    res.status(500).json({ error: error.message });
  }
}
