
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

// Store API keys in memory instead of trying to use storage
const API_KEYS = {
  HELIUS: '4418d794-039b-4530-a9c4-6f8e325faa18'
};

/**
 * Get API key by name
 */
export function getApiKey(name: string): string {
  return API_KEYS[name] || '';
}

/**
 * Initialize storage with required files and folders
 */
export async function initializeStorage() {
  try {
    console.log('Initializing app storage...');

    // Check if storage buckets exist first
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets();

    if (bucketsError) {
      console.error('Error listing buckets:', bucketsError);
      // Don't try to create/access buckets if we can't list them
      return false;
    }

    // Skip storage operations if no buckets exist or user doesn't have permission
    if (!buckets || buckets.length === 0) {
      console.log('No storage buckets available, skipping storage initialization');
      return true;
    }

    // Check if app_assets bucket exists
    const appAssetsBucket = buckets.find(bucket => bucket.name === 'app_assets');
    if (!appAssetsBucket) {
      console.log('app_assets bucket not found, skipping storage initialization');
      return true;
    }

    console.log('Storage initialization complete');
    return true;
  } catch (error) {
    console.error('Storage initialization failed:', error);
    // Don't show toast to user since this is not critical
    return false;
  }
}

/**
 * Run storage initialization when app starts
 */
export function setupAppStorage() {
  // Check if user is authenticated first
  supabase.auth.getUser().then(({ data }) => {
    if (data.user) {
      initializeStorage();
    } else {
      console.log('User not authenticated, deferring storage initialization');

      // Listen for auth state changes to initialize storage when user logs in
      const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
        if (event === 'SIGNED_IN' && session) {
          console.log('User signed in, initializing storage');
          initializeStorage();
        }
      });

      // Return cleanup function
      return () => {
        authListener.subscription.unsubscribe();
      };
    }
  });
}
