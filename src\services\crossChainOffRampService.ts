/**
 * Cross-Chain Off-Ramp Service
 * 
 * Handles off-ramping from multiple blockchain networks to fiat
 */

import { 
  SupportedChain, 
  CrossChainWallet, 
  TokenBalance,
  CHAIN_CONFIGS 
} from '@/types/crossChain';
import { CrossChainWalletService } from './crossChainWalletService';
import { CrossChainBridgeService } from './crossChainBridgeService';
import { exchangeRateService } from './exchangeRateService';

export interface CrossChainOffRampRequest {
  userId: string;
  fromChain: SupportedChain;
  fromToken: string;
  amount: string;
  targetCurrency: string; // NGN, USD, etc.
  bankAccount: {
    bankName: string;
    accountNumber: string;
    accountName: string;
  };
  bridgeToOptimalChain?: boolean; // Auto-bridge to chain with best rates
}

export interface CrossChainOffRampQuote {
  fromChain: SupportedChain;
  fromToken: string;
  fromAmount: string;
  targetCurrency: string;
  targetAmount: number;
  exchangeRate: number;
  fees: {
    networkFee: string;
    bridgeFee?: string;
    exchangeFee: string;
    totalFeeUSD: number;
  };
  estimatedTime: number; // minutes
  route: OffRampStep[];
  optimalChain?: SupportedChain; // If bridging is recommended
}

export interface OffRampStep {
  action: 'bridge' | 'convert' | 'withdraw';
  fromChain?: SupportedChain;
  toChain?: SupportedChain;
  fromToken: string;
  toToken: string;
  fromAmount: string;
  toAmount: string;
  estimatedTime: number;
  fees: string;
}

export class CrossChainOffRampService {
  /**
   * Get off-ramp quotes from multiple chains
   */
  static async getOffRampQuotes(
    userId: string,
    targetCurrency: string = 'NGN'
  ): Promise<CrossChainOffRampQuote[]> {
    try {
      const quotes: CrossChainOffRampQuote[] = [];
      const userWallets = await CrossChainWalletService.getUserWallets(userId);

      for (const wallet of userWallets) {
        for (const balance of wallet.balances) {
          if (parseFloat(balance.balance) > 0) {
            const quote = await this.getQuoteForToken(
              wallet.chain,
              balance.tokenSymbol,
              balance.balance,
              targetCurrency
            );
            
            if (quote) {
              quotes.push(quote);
            }
          }
        }
      }

      // Sort by best rate (highest output amount)
      return quotes.sort((a, b) => b.targetAmount - a.targetAmount);
    } catch (error) {
      console.error('Error getting cross-chain off-ramp quotes:', error);
      return [];
    }
  }

  /**
   * Get quote for specific token on specific chain
   */
  static async getQuoteForToken(
    fromChain: SupportedChain,
    fromToken: string,
    amount: string,
    targetCurrency: string
  ): Promise<CrossChainOffRampQuote | null> {
    try {
      // Get direct exchange rate for this chain/token
      const directRate = await exchangeRateService.getExchangeRate(fromToken, targetCurrency);
      
      if (!directRate) {
        console.warn(`No direct rate found for ${fromToken} to ${targetCurrency}`);
        return null;
      }

      const directAmount = parseFloat(amount) * directRate.rate;
      const directFees = this.calculateDirectFees(fromChain, parseFloat(amount));

      const quote: CrossChainOffRampQuote = {
        fromChain,
        fromToken,
        fromAmount: amount,
        targetCurrency,
        targetAmount: directAmount - directFees.totalFeeUSD,
        exchangeRate: directRate.rate,
        fees: directFees,
        estimatedTime: this.getEstimatedTime(fromChain),
        route: [{
          action: 'convert',
          fromToken,
          toToken: targetCurrency,
          fromAmount: amount,
          toAmount: directAmount.toString(),
          estimatedTime: this.getEstimatedTime(fromChain),
          fees: directFees.exchangeFee
        }]
      };

      // Check if bridging to another chain would be more optimal
      const optimalQuote = await this.checkOptimalChainRoute(
        fromChain,
        fromToken,
        amount,
        targetCurrency
      );

      if (optimalQuote && optimalQuote.targetAmount > quote.targetAmount) {
        return optimalQuote;
      }

      return quote;
    } catch (error) {
      console.error(`Error getting quote for ${fromToken} on ${fromChain}:`, error);
      return null;
    }
  }

  /**
   * Check if bridging to another chain would provide better rates
   */
  private static async checkOptimalChainRoute(
    fromChain: SupportedChain,
    fromToken: string,
    amount: string,
    targetCurrency: string
  ): Promise<CrossChainOffRampQuote | null> {
    try {
      const supportedChains = CrossChainWalletService.getSupportedChains();
      let bestQuote: CrossChainOffRampQuote | null = null;

      for (const targetChain of supportedChains) {
        if (targetChain === fromChain) continue;

        // Check if we can bridge this token to the target chain
        const bridgeQuotes = await CrossChainBridgeService.getBridgeQuotes(
          fromChain,
          targetChain,
          fromToken,
          fromToken, // Same token on different chain
          amount
        );

        if (bridgeQuotes.length === 0) continue;

        const bestBridgeQuote = bridgeQuotes[0];
        
        // Get off-ramp rate on target chain
        const targetChainRate = await exchangeRateService.getExchangeRate(
          fromToken,
          targetCurrency
        );

        if (!targetChainRate) continue;

        const bridgedAmount = parseFloat(bestBridgeQuote.toAmount);
        const finalAmount = bridgedAmount * targetChainRate.rate;
        const totalFees = bestBridgeQuote.fees.totalFeeUSD + 
                         this.calculateDirectFees(targetChain, bridgedAmount).totalFeeUSD;

        const netAmount = finalAmount - totalFees;

        const quote: CrossChainOffRampQuote = {
          fromChain,
          fromToken,
          fromAmount: amount,
          targetCurrency,
          targetAmount: netAmount,
          exchangeRate: targetChainRate.rate,
          fees: {
            networkFee: bestBridgeQuote.fees.networkFee,
            bridgeFee: bestBridgeQuote.fees.bridgeFee,
            exchangeFee: this.calculateDirectFees(targetChain, bridgedAmount).exchangeFee,
            totalFeeUSD: totalFees
          },
          estimatedTime: bestBridgeQuote.estimatedTime + this.getEstimatedTime(targetChain),
          route: [
            {
              action: 'bridge',
              fromChain,
              toChain: targetChain,
              fromToken,
              toToken: fromToken,
              fromAmount: amount,
              toAmount: bestBridgeQuote.toAmount,
              estimatedTime: bestBridgeQuote.estimatedTime,
              fees: bestBridgeQuote.fees.bridgeFee
            },
            {
              action: 'convert',
              fromToken,
              toToken: targetCurrency,
              fromAmount: bestBridgeQuote.toAmount,
              toAmount: finalAmount.toString(),
              estimatedTime: this.getEstimatedTime(targetChain),
              fees: this.calculateDirectFees(targetChain, bridgedAmount).exchangeFee
            }
          ],
          optimalChain: targetChain
        };

        if (!bestQuote || netAmount > bestQuote.targetAmount) {
          bestQuote = quote;
        }
      }

      return bestQuote;
    } catch (error) {
      console.error('Error checking optimal chain route:', error);
      return null;
    }
  }

  /**
   * Execute cross-chain off-ramp
   */
  static async executeOffRamp(request: CrossChainOffRampRequest): Promise<{
    success: boolean;
    transactionId?: string;
    error?: string;
  }> {
    try {
      // Get the best quote for this request
      const quote = await this.getQuoteForToken(
        request.fromChain,
        request.fromToken,
        request.amount,
        request.targetCurrency
      );

      if (!quote) {
        return { success: false, error: 'No quote available' };
      }

      // If optimal chain is different and bridging is enabled
      if (quote.optimalChain && request.bridgeToOptimalChain) {
        return this.executeOptimalChainOffRamp(request, quote);
      } else {
        return this.executeDirectOffRamp(request, quote);
      }
    } catch (error) {
      console.error('Error executing cross-chain off-ramp:', error);
      return { success: false, error: 'Execution failed' };
    }
  }

  /**
   * Execute direct off-ramp (no bridging)
   */
  private static async executeDirectOffRamp(
    request: CrossChainOffRampRequest,
    quote: CrossChainOffRampQuote
  ): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    try {
      // Import the existing off-ramp service
      const { offRampProcessingService } = await import('./offRampProcessingService');

      // Convert cross-chain request to standard off-ramp request
      const offRampRequest = {
        userId: request.userId,
        cryptoAmount: parseFloat(request.amount),
        cryptoSymbol: request.fromToken,
        chain: request.fromChain,
        bankAccount: {
          bankName: request.bankAccount.bankName,
          bankCode: this.getBankCode(request.bankAccount.bankName),
          accountNumber: request.bankAccount.accountNumber,
          accountName: request.bankAccount.accountName,
        }
      };

      // Execute the off-ramp using existing service
      const result = await offRampProcessingService.processOffRamp(offRampRequest);

      if (result.success) {
        // Record the cross-chain off-ramp transaction
        await this.recordCrossChainOffRamp(request, quote, result.transactionId);

        return {
          success: true,
          transactionId: result.transactionId
        };
      } else {
        return {
          success: false,
          error: result.message
        };
      }
    } catch (error) {
      console.error('Direct off-ramp execution failed:', error);
      return {
        success: false,
        error: 'Failed to execute off-ramp transaction'
      };
    }
  }

  /**
   * Execute optimal chain off-ramp (with bridging)
   */
  private static async executeOptimalChainOffRamp(
    request: CrossChainOffRampRequest,
    quote: CrossChainOffRampQuote
  ): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    try {
      if (!quote.optimalChain) {
        return { success: false, error: 'No optimal chain specified' };
      }

      // Step 1: Execute bridge to optimal chain
      const { CrossChainBridgeService } = await import('./crossChainBridgeService');

      // Get bridge quote
      const bridgeQuotes = await CrossChainBridgeService.getBridgeQuotes(
        request.fromChain,
        quote.optimalChain,
        request.fromToken,
        request.fromToken, // Same token on destination
        request.amount
      );

      if (bridgeQuotes.length === 0) {
        return { success: false, error: 'No bridge routes available' };
      }

      const bestBridgeQuote = bridgeQuotes[0];

      // Execute bridge transaction
      const bridgeResult = await CrossChainBridgeService.executeBridge(
        request.userId,
        bestBridgeQuote,
        'user-from-address', // Would get from wallet service
        'user-to-address'    // Would get from wallet service
      );

      if (!bridgeResult) {
        return { success: false, error: 'Bridge execution failed' };
      }

      // Step 2: Wait for bridge completion (in production, this would be event-driven)
      console.log('🌉 Bridge initiated, waiting for completion...');

      // Step 3: Execute off-ramp on optimal chain
      const optimalChainRequest = {
        ...request,
        fromChain: quote.optimalChain,
        amount: bestBridgeQuote.toAmount
      };

      const offRampResult = await this.executeDirectOffRamp(optimalChainRequest, quote);

      if (offRampResult.success) {
        return {
          success: true,
          transactionId: `bridge-${bridgeResult.id}-offramp-${offRampResult.transactionId}`
        };
      } else {
        return {
          success: false,
          error: `Bridge succeeded but off-ramp failed: ${offRampResult.error}`
        };
      }
    } catch (error) {
      console.error('Optimal chain off-ramp execution failed:', error);
      return {
        success: false,
        error: 'Failed to execute optimal chain off-ramp'
      };
    }
  }

  /**
   * Calculate fees for direct off-ramp
   */
  private static calculateDirectFees(chain: SupportedChain, amount: number): {
    networkFee: string;
    exchangeFee: string;
    totalFeeUSD: number;
  } {
    const chainConfig = CHAIN_CONFIGS[chain];
    
    // Base network fees by chain
    const networkFees: Record<SupportedChain, number> = {
      [SupportedChain.SOLANA]: 0.001,
      [SupportedChain.ETHEREUM]: 0.01,
      [SupportedChain.POLYGON]: 0.001,
      [SupportedChain.BSC]: 0.001,
      [SupportedChain.ARBITRUM]: 0.001,
      [SupportedChain.AVALANCHE]: 0.001,
      [SupportedChain.BASE]: 0.001
    };

    const networkFee = networkFees[chain] || 0.001;
    const exchangeFeePercent = 0.005; // 0.5%
    const exchangeFee = amount * exchangeFeePercent;
    
    return {
      networkFee: networkFee.toString(),
      exchangeFee: exchangeFee.toString(),
      totalFeeUSD: networkFee + exchangeFee
    };
  }

  /**
   * Get estimated time for chain operations
   */
  private static getEstimatedTime(chain: SupportedChain): number {
    const times: Record<SupportedChain, number> = {
      [SupportedChain.SOLANA]: 2,
      [SupportedChain.ETHEREUM]: 15,
      [SupportedChain.POLYGON]: 5,
      [SupportedChain.BSC]: 3,
      [SupportedChain.ARBITRUM]: 10,
      [SupportedChain.AVALANCHE]: 3,
      [SupportedChain.BASE]: 5
    };

    return times[chain] || 10;
  }

  /**
   * Get bank code from bank name (helper method)
   */
  private static getBankCode(bankName: string): string {
    // Common Nigerian bank codes
    const bankCodes: Record<string, string> = {
      'Access Bank': '044',
      'Zenith Bank': '057',
      'GTBank': '058',
      'First Bank': '011',
      'UBA': '033',
      'Fidelity Bank': '070',
      'FCMB': '214',
      'Sterling Bank': '232',
      'Union Bank': '032',
      'Wema Bank': '035'
    };

    return bankCodes[bankName] || '044'; // Default to Access Bank
  }

  /**
   * Record cross-chain off-ramp transaction
   */
  private static async recordCrossChainOffRamp(
    request: CrossChainOffRampRequest,
    quote: CrossChainOffRampQuote,
    transactionId?: string
  ): Promise<void> {
    try {
      const { supabase } = await import('@/lib/supabase');

      await supabase
        .from('cross_chain_off_ramp_transactions')
        .insert({
          user_id: request.userId,
          from_chain: request.fromChain,
          from_token: request.fromToken,
          from_amount: parseFloat(request.amount),
          target_currency: request.targetCurrency,
          target_amount: quote.targetAmount,
          exchange_rate: quote.exchangeRate,
          bank_name: request.bankAccount.bankName,
          account_number: request.bankAccount.accountNumber,
          account_name: request.bankAccount.accountName,
          status: 'processing',
          fees: quote.fees,
          route: quote.route,
          estimated_time: quote.estimatedTime,
          reference: transactionId
        });
    } catch (error) {
      console.error('Failed to record cross-chain off-ramp:', error);
    }
  }
}
