/**
 * Feature Configuration
 * 
 * Controls which features are enabled in the application
 */

export const FEATURE_FLAGS = {
  // Off-ramp real transactions
  // Set to true once Paystack business registration is complete
  REAL_TRANSACTIONS_ENABLED: true,
  
  // Virtual card features
  VIRTUAL_CARDS_ENABLED: true,
  
  // Group cards
  GROUP_CARDS_ENABLED: true,
  
  // Recurring payments
  RECURRING_PAYMENTS_ENABLED: true,
  
  // Admin features
  ADMIN_DASHBOARD_ENABLED: true,
  
  // Development features
  DEBUG_MODE: import.meta.env.DEV || false,
} as const;

/**
 * Payment Provider Configuration
 */
export const PAYMENT_CONFIG = {
  // Paystack configuration
  PAYSTACK: {
    // Set to true when business registration is complete
    LIVE_MODE_ENABLED: true,
    
    // Test keys (safe to commit)
    TEST_PUBLIC_KEY: 'pk_test_your_test_key_here',
    
    // Live keys (should be in environment variables)
    LIVE_PUBLIC_KEY: import.meta.env.VITE_PAYSTACK_LIVE_PUBLIC_KEY || '',
  },
  
  // Flutterwave configuration
  FLUTTERWAVE: {
    LIVE_MODE_ENABLED: false,
    TEST_PUBLIC_KEY: 'FLWPUBK_TEST-your_test_key_here',
    LIVE_PUBLIC_KEY: import.meta.env.VITE_FLUTTERWAVE_LIVE_PUBLIC_KEY || '',
  },
} as const;

/**
 * Business Registration Checklist
 * 
 * Complete these steps to enable real transactions:
 * 
 * 1. ✅ Register business with Paystack
 * 2. ✅ Complete KYC/business verification
 * 3. ✅ Get live API keys
 * 4. ✅ Test transfers in Paystack dashboard
 * 5. ✅ Update REAL_TRANSACTIONS_ENABLED to true
 * 6. ✅ Update PAYSTACK.LIVE_MODE_ENABLED to true
 * 7. ✅ Add live API keys to environment variables
 */

export const getPaymentConfig = () => {
  const isLive = PAYMENT_CONFIG.PAYSTACK.LIVE_MODE_ENABLED && FEATURE_FLAGS.REAL_TRANSACTIONS_ENABLED;
  
  return {
    paystack: {
      publicKey: isLive 
        ? PAYMENT_CONFIG.PAYSTACK.LIVE_PUBLIC_KEY 
        : PAYMENT_CONFIG.PAYSTACK.TEST_PUBLIC_KEY,
      isLive,
    },
    flutterwave: {
      publicKey: isLive 
        ? PAYMENT_CONFIG.FLUTTERWAVE.LIVE_PUBLIC_KEY 
        : PAYMENT_CONFIG.FLUTTERWAVE.TEST_PUBLIC_KEY,
      isLive,
    },
  };
};
