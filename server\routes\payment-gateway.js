/**
 * Payment Gateway API Routes
 * 
 * RESTful API for businesses to integrate crypto payments
 * Similar to Stripe API but for cryptocurrency
 */

const express = require('express');
const router = express.Router();
const { supabase } = require('../utils/supabaseClient');
const crypto = require('crypto');

// Middleware to authenticate API keys
const authenticateAPIKey = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: {
          type: 'authentication_error',
          message: 'Invalid API key provided'
        }
      });
    }

    const apiKey = authHeader.substring(7); // Remove 'Bearer '
    
    // Extract key ID (everything before the last underscore)
    const keyId = apiKey.substring(0, apiKey.lastIndexOf('_'));
    
    // Verify API key
    const { data: keyData, error } = await supabase
      .from('merchant_api_keys')
      .select(`
        *,
        merchant:merchant_accounts(*)
      `)
      .eq('key_id', keyId)
      .eq('is_active', true)
      .single();

    if (error || !keyData) {
      return res.status(401).json({
        error: {
          type: 'authentication_error',
          message: 'Invalid API key provided'
        }
      });
    }

    // Verify the full key (in production, hash and compare)
    // For now, simple comparison
    if (keyData.key_secret !== crypto.createHash('sha256').update(apiKey).digest('hex')) {
      return res.status(401).json({
        error: {
          type: 'authentication_error',
          message: 'Invalid API key provided'
        }
      });
    }

    // Update last used timestamp
    await supabase
      .from('merchant_api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', keyData.id);

    req.merchant = keyData.merchant;
    req.apiKey = keyData;
    next();

  } catch (error) {
    console.error('API key authentication error:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
};

/**
 * Create Payment Intent
 * POST /api/v1/payment_intents
 */
router.post('/payment_intents', authenticateAPIKey, async (req, res) => {
  try {
    const {
      amount,
      currency = 'USD',
      accepted_cryptocurrencies = ['SOL', 'USDC'],
      settlement_currency = 'NGN',
      metadata = {},
      description,
      success_url,
      cancel_url,
      customer_email
    } = req.body;

    // Validation
    if (!amount || amount <= 0) {
      return res.status(400).json({
        error: {
          type: 'invalid_request_error',
          message: 'Amount must be a positive number'
        }
      });
    }

    // Generate unique payment intent ID
    const paymentIntentId = `pi_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
    const clientSecret = `${paymentIntentId}_secret_${crypto.randomBytes(16).toString('hex')}`;
    
    // Generate payment URL
    const baseUrl = process.env.BASE_URL || 'https://yourplatform.com';
    const paymentUrl = `${baseUrl}/pay/${paymentIntentId}`;

    // Insert payment intent
    const { data, error } = await supabase
      .from('payment_intents')
      .insert({
        id: paymentIntentId,
        merchant_id: req.merchant.id,
        client_secret: clientSecret,
        amount: amount,
        currency: currency,
        settlement_currency: settlement_currency,
        accepted_cryptocurrencies: accepted_cryptocurrencies,
        success_url: success_url,
        cancel_url: cancel_url,
        payment_url: paymentUrl,
        metadata: metadata,
        description: description,
        status: 'requires_payment_method'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating payment intent:', error);
      return res.status(500).json({
        error: {
          type: 'api_error',
          message: 'Failed to create payment intent'
        }
      });
    }

    // Generate QR code
    const QRCode = require('qrcode');
    const qrCode = await QRCode.toDataURL(paymentUrl);

    res.json({
      id: data.id,
      object: 'payment_intent',
      client_secret: data.client_secret,
      amount: data.amount,
      currency: data.currency,
      status: data.status,
      payment_url: data.payment_url,
      qr_code: qrCode,
      accepted_cryptocurrencies: data.accepted_cryptocurrencies,
      settlement_currency: data.settlement_currency,
      metadata: data.metadata,
      created: Math.floor(new Date(data.created_at).getTime() / 1000)
    });

  } catch (error) {
    console.error('Payment intent creation error:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
});

/**
 * Retrieve Payment Intent
 * GET /api/v1/payment_intents/:id
 */
router.get('/payment_intents/:id', authenticateAPIKey, async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('payment_intents')
      .select('*')
      .eq('id', id)
      .eq('merchant_id', req.merchant.id)
      .single();

    if (error || !data) {
      return res.status(404).json({
        error: {
          type: 'invalid_request_error',
          message: 'Payment intent not found'
        }
      });
    }

    res.json({
      id: data.id,
      object: 'payment_intent',
      amount: data.amount,
      currency: data.currency,
      status: data.status,
      payment_url: data.payment_url,
      accepted_cryptocurrencies: data.accepted_cryptocurrencies,
      settlement_currency: data.settlement_currency,
      metadata: data.metadata,
      created: Math.floor(new Date(data.created_at).getTime() / 1000),
      succeeded_at: data.succeeded_at ? Math.floor(new Date(data.succeeded_at).getTime() / 1000) : null
    });

  } catch (error) {
    console.error('Error retrieving payment intent:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
});

/**
 * List Payment Intents
 * GET /api/v1/payment_intents
 */
router.get('/payment_intents', authenticateAPIKey, async (req, res) => {
  try {
    const { limit = 10, starting_after, ending_before } = req.query;

    let query = supabase
      .from('payment_intents')
      .select('*')
      .eq('merchant_id', req.merchant.id)
      .order('created_at', { ascending: false })
      .limit(parseInt(limit));

    if (starting_after) {
      query = query.gt('created_at', starting_after);
    }

    if (ending_before) {
      query = query.lt('created_at', ending_before);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error listing payment intents:', error);
      return res.status(500).json({
        error: {
          type: 'api_error',
          message: 'Failed to retrieve payment intents'
        }
      });
    }

    const formattedData = data.map(item => ({
      id: item.id,
      object: 'payment_intent',
      amount: item.amount,
      currency: item.currency,
      status: item.status,
      metadata: item.metadata,
      created: Math.floor(new Date(item.created_at).getTime() / 1000)
    }));

    res.json({
      object: 'list',
      data: formattedData,
      has_more: data.length === parseInt(limit)
    });

  } catch (error) {
    console.error('Error listing payment intents:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
});

/**
 * Create Payment Link
 * POST /api/v1/payment_links
 */
router.post('/payment_links', authenticateAPIKey, async (req, res) => {
  try {
    const {
      title,
      description,
      amount,
      currency = 'USD',
      is_amount_fixed = true,
      min_amount,
      max_amount,
      accepted_cryptocurrencies = ['SOL', 'USDC'],
      expires_at,
      max_uses,
      metadata = {}
    } = req.body;

    if (!title) {
      return res.status(400).json({
        error: {
          type: 'invalid_request_error',
          message: 'Title is required'
        }
      });
    }

    // Generate unique payment link ID and URL slug
    const paymentLinkId = `plink_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
    const urlSlug = crypto.randomBytes(16).toString('hex');

    const { data, error } = await supabase
      .from('payment_links')
      .insert({
        id: paymentLinkId,
        merchant_id: req.merchant.id,
        url_slug: urlSlug,
        title: title,
        description: description,
        amount: amount,
        currency: currency,
        is_amount_fixed: is_amount_fixed,
        min_amount: min_amount,
        max_amount: max_amount,
        accepted_cryptocurrencies: accepted_cryptocurrencies,
        expires_at: expires_at,
        max_uses: max_uses,
        metadata: metadata
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating payment link:', error);
      return res.status(500).json({
        error: {
          type: 'api_error',
          message: 'Failed to create payment link'
        }
      });
    }

    const baseUrl = process.env.BASE_URL || 'https://yourplatform.com';
    const paymentUrl = `${baseUrl}/link/${urlSlug}`;

    res.json({
      id: data.id,
      object: 'payment_link',
      url: paymentUrl,
      title: data.title,
      description: data.description,
      amount: data.amount,
      currency: data.currency,
      is_amount_fixed: data.is_amount_fixed,
      accepted_cryptocurrencies: data.accepted_cryptocurrencies,
      active: data.is_active,
      created: Math.floor(new Date(data.created_at).getTime() / 1000)
    });

  } catch (error) {
    console.error('Payment link creation error:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
});

/**
 * Create Webhook Endpoint
 * POST /api/v1/webhook_endpoints
 */
router.post('/webhook_endpoints', authenticateAPIKey, async (req, res) => {
  try {
    const { url, events = ['payment_intent.succeeded', 'payment_intent.payment_failed'] } = req.body;

    if (!url) {
      return res.status(400).json({
        error: {
          type: 'invalid_request_error',
          message: 'URL is required'
        }
      });
    }

    // Generate webhook secret
    const crypto = require('crypto');
    const secret = crypto.randomBytes(32).toString('hex');

    const { data, error } = await supabase
      .from('merchant_webhook_endpoints')
      .insert({
        merchant_id: req.merchant.id,
        url: url,
        events: events,
        secret: secret,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating webhook endpoint:', error);
      return res.status(500).json({
        error: {
          type: 'api_error',
          message: 'Failed to create webhook endpoint'
        }
      });
    }

    res.json({
      id: data.id,
      object: 'webhook_endpoint',
      url: data.url,
      events: data.events,
      secret: data.secret,
      active: data.is_active,
      created: Math.floor(new Date(data.created_at).getTime() / 1000)
    });

  } catch (error) {
    console.error('Webhook endpoint creation error:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
});

/**
 * List Webhook Endpoints
 * GET /api/v1/webhook_endpoints
 */
router.get('/webhook_endpoints', authenticateAPIKey, async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('merchant_webhook_endpoints')
      .select('*')
      .eq('merchant_id', req.merchant.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error listing webhook endpoints:', error);
      return res.status(500).json({
        error: {
          type: 'api_error',
          message: 'Failed to retrieve webhook endpoints'
        }
      });
    }

    const formattedData = data.map(item => ({
      id: item.id,
      object: 'webhook_endpoint',
      url: item.url,
      events: item.events,
      active: item.is_active,
      created: Math.floor(new Date(item.created_at).getTime() / 1000)
    }));

    res.json({
      object: 'list',
      data: formattedData,
      has_more: false
    });

  } catch (error) {
    console.error('Error listing webhook endpoints:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
});

/**
 * Delete Webhook Endpoint
 * DELETE /api/v1/webhook_endpoints/:id
 */
router.delete('/webhook_endpoints/:id', authenticateAPIKey, async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('merchant_webhook_endpoints')
      .delete()
      .eq('id', id)
      .eq('merchant_id', req.merchant.id);

    if (error) {
      console.error('Error deleting webhook endpoint:', error);
      return res.status(500).json({
        error: {
          type: 'api_error',
          message: 'Failed to delete webhook endpoint'
        }
      });
    }

    res.json({ deleted: true });

  } catch (error) {
    console.error('Error deleting webhook endpoint:', error);
    res.status(500).json({
      error: {
        type: 'api_error',
        message: 'Internal server error'
      }
    });
  }
});

module.exports = router;
