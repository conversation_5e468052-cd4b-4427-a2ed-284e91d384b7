
-- Create a storage bucket for user avatars
INSERT INTO storage.buckets (id, name, public, file_size_limit)
VALUES ('avatars', 'avatars', TRUE, 5242880); -- 5MB limit

-- Create RLS policy for the bucket
BEGIN;
  -- Policy for public access to avatars (read-only)
  CREATE POLICY "Public avatars are accessible to everyone" ON storage.objects
    FOR SELECT
    USING (bucket_id = 'avatars');

  -- Policy for authenticated users to upload their own avatars
  CREATE POLICY "Users can upload their own avatar" ON storage.objects
    FOR INSERT
    WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

  -- Policy for users to update their own avatars
  CREATE POLICY "Users can update their own avatar" ON storage.objects
    FOR UPDATE
    USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

  -- Policy for users to delete their own avatars
  CREATE POLICY "Users can delete their own avatar" ON storage.objects
    FOR DELETE
    USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
COMMIT;
