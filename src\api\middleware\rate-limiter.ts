/**
 * Rate Limiter Middleware
 * 
 * Implements rate limiting for API endpoints
 */

import { Request, Response, NextFunction } from 'express';

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const store: RateLimitStore = {};

interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  max: number; // Maximum requests per window
  message?: string;
  standardHeaders?: boolean; // Include rate limit headers
}

const defaultOptions: RateLimitOptions = {
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: 'Too many requests, please try again later',
  standardHeaders: true
};

export const createRateLimiter = (options: Partial<RateLimitOptions> = {}) => {
  const opts = { ...defaultOptions, ...options };

  return (req: Request, res: Response, next: NextFunction) => {
    const key = getClientKey(req);
    const now = Date.now();
    const windowStart = now - opts.windowMs;

    // Clean up old entries
    if (store[key] && store[key].resetTime < windowStart) {
      delete store[key];
    }

    // Initialize or update counter
    if (!store[key]) {
      store[key] = {
        count: 1,
        resetTime: now + opts.windowMs
      };
    } else {
      store[key].count++;
    }

    const current = store[key];
    const remaining = Math.max(0, opts.max - current.count);
    const resetTime = Math.ceil(current.resetTime / 1000);

    // Add rate limit headers
    if (opts.standardHeaders) {
      res.set({
        'X-RateLimit-Limit': opts.max.toString(),
        'X-RateLimit-Remaining': remaining.toString(),
        'X-RateLimit-Reset': resetTime.toString()
      });
    }

    // Check if limit exceeded
    if (current.count > opts.max) {
      res.set('Retry-After', Math.ceil(opts.windowMs / 1000).toString());
      
      return res.status(429).json({
        error: {
          type: 'rate_limit_exceeded',
          message: opts.message,
          limit: opts.max,
          remaining: 0,
          reset: resetTime
        }
      });
    }

    next();
  };
};

const getClientKey = (req: Request): string => {
  // Use API key if available, otherwise fall back to IP
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const apiKey = authHeader.substring(7);
    return `api_key:${apiKey}`;
  }
  
  // Get IP address (handle proxies)
  const ip = req.ip || 
             req.connection.remoteAddress || 
             req.socket.remoteAddress ||
             (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
             'unknown';
  
  return `ip:${ip}`;
};

// Default rate limiter for general API usage
export const rateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 100 // 100 requests per minute
});

// Stricter rate limiter for payment creation
export const paymentRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 20, // 20 payment creations per minute
  message: 'Too many payment requests, please slow down'
});

// Very strict rate limiter for webhook endpoints
export const webhookRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 1000, // 1000 webhook calls per minute (high for legitimate traffic)
  message: 'Webhook rate limit exceeded'
});
