/**
 * Rate Health Monitor Component
 * Shows the status of exchange rate APIs and data freshness
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { reliableExchangeRateService } from '@/services/reliableExchangeRateService';
import { 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw, 
  Clock,
  TrendingUp,
  Wifi,
  WifiOff
} from 'lucide-react';

interface RateInfo {
  symbol: 'USDC' | 'SOL';
  rate: number;
  ageMinutes: number;
  isStale: boolean;
}

const RateHealthMonitor: React.FC = () => {
  const [rates, setRates] = useState<RateInfo[]>([]);
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadRateInfo();
    const interval = setInterval(loadRateInfo, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadRateInfo = async () => {
    try {
      const [usdcInfo, solInfo] = await Promise.all([
        reliableExchangeRateService.getRateWithAge('USDC'),
        reliableExchangeRateService.getRateWithAge('SOL')
      ]);

      setRates([
        { symbol: 'USDC', ...usdcInfo },
        { symbol: 'SOL', ...solInfo }
      ]);

      setHealthStatus(reliableExchangeRateService.getHealthStatus());
    } catch (error) {
      console.error('Error loading rate info:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await reliableExchangeRateService.updateRates();
      await loadRateInfo();
    } catch (error) {
      console.error('Error refreshing rates:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusColor = (isStale: boolean) => {
    return isStale ? 'bg-yellow-500' : 'bg-green-500';
  };

  const getStatusIcon = (isStale: boolean) => {
    return isStale ? <AlertTriangle className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />;
  };

  const formatAge = (minutes: number) => {
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m ago`;
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Exchange Rate Monitor
          </CardTitle>
          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Health */}
        {healthStatus && (
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {healthStatus.hasCachedRates ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="font-medium">
                {healthStatus.hasCachedRates ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            <Badge variant={healthStatus.isStale ? "secondary" : "default"}>
              {healthStatus.isStale ? 'Stale Data' : 'Fresh Data'}
            </Badge>
          </div>
        )}

        {/* Rate Details */}
        <div className="space-y-3">
          {rates.map((rateInfo) => (
            <div key={rateInfo.symbol} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${getStatusColor(rateInfo.isStale)}`}></div>
                <div>
                  <div className="font-medium">{rateInfo.symbol}/NGN</div>
                  <div className="text-sm text-gray-600 flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {formatAge(rateInfo.ageMinutes)}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-semibold">
                  ₦{rateInfo.rate.toLocaleString()}
                </div>
                <Badge 
                  variant={rateInfo.isStale ? "secondary" : "default"}
                  className="text-xs"
                >
                  {rateInfo.isStale ? 'Stale' : 'Fresh'}
                </Badge>
              </div>
            </div>
          ))}
        </div>

        {/* Status Messages */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Rates update automatically every 5 minutes</p>
          <p>• Multiple APIs ensure reliability</p>
          <p>• Fallback rates used if all APIs fail</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default RateHealthMonitor;
