import React, { useState } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Wallet, DollarSign, Settings, CreditCard, User, Home, RefreshCw, ArrowUpDown, ArrowLeftRight, Users, Bell, Menu, X, Shield, Banknote, Globe, Store, QrCode, MessageCircle, Mic, Sparkles, Building2 } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useWallet } from "@/contexts/WalletContext";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from "@/contexts/NotificationContext";
import PWAInstall from "@/components/PWAInstall";
import CryptoLogo from "@/components/ui/CryptoLogo";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { wallets, isLoading, refreshWallets } = useWallet();
  const { unreadCount = 0 } = useNotifications();
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshWallets();
    setIsRefreshing(false);
  };

  // Main navigation items for bottom navbar (7 items)
  const bottomNavItems = [
    { to: "/", icon: Home, label: "Home" },
    { to: "/wallets", icon: Wallet, label: "Wallets" },
    { to: "/swap", icon: ArrowLeftRight, label: "Swap" },
    { to: "/services", icon: Settings, label: "Services" },
    { to: "/virtual-card", icon: CreditCard, label: "Card" },
    { to: "/off-ramp", icon: Banknote, label: "Off-Ramp" },
    { to: "/group-cards", icon: Users, label: "Groups" }
  ];

  // Header navigation items (3 items)
  const headerNavItems = [
    { to: "/profile", icon: User, label: "Profile" },
    { to: "/notifications", icon: Bell, label: "Notifications", badge: unreadCount > 0 ? unreadCount : undefined },
    { to: "/settings", icon: Settings, label: "Settings" }
  ];

  return (
    <div className="min-h-screen bg-background flex flex-col w-full overflow-x-hidden">
      {/* Sidebar for desktop */}
      <aside className="hidden md:flex w-64 flex-col fixed inset-y-0 z-20">
        <div className="flex flex-col flex-grow bg-card border-r border-border">
          <div className="flex items-center h-24 px-4 border-b border-border">
            <div className="flex items-center justify-center w-full">
              <img
                src="/solana.png"
                alt="solpay"
                className="h-20 w-auto max-w-[220px] object-contain"
                loading="eager"
              />
            </div>
          </div>
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <Home size={18} />
              <span>Dashboard</span>
            </NavLink>
            <NavLink
              to="/wallets"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <Wallet size={18} />
              <span>Wallets</span>
            </NavLink>
            <NavLink
              to="/off-ramp"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <Banknote size={18} />
              <span>Off-Ramp</span>
            </NavLink>
            <NavLink
              to="/services"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <DollarSign size={18} />
              <span>Services</span>
            </NavLink>
            <NavLink
              to="/virtual-card"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <CreditCard size={18} />
              <span>Virtual Card</span>
            </NavLink>
            <NavLink
              to="/group-cards"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <CreditCard size={18} />
              <span>Group Cards</span>
            </NavLink>
            <NavLink
              to="/settings"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <Settings size={18} />
              <span>Settings</span>
            </NavLink>
            <NavLink
              to="/profile"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <User size={18} />
              <span>Profile</span>
            </NavLink>

            {/* Notifications Link */}
            <NavLink
              to="/notifications"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <Bell size={18} />
              <span>Notifications</span>
              {unreadCount > 0 && (
                <Badge className="ml-2 h-5 w-5 flex items-center justify-center p-0 bg-red-500 text-white text-xs">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </Badge>
              )}
            </NavLink>

            {/* Payment Gateway Dashboard */}
            <NavLink
              to="/payment-gateway-dashboard"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <Building2 size={18} />
              <span>Payment Gateway</span>
            </NavLink>

            {/* Wallet Configuration */}
            <NavLink
              to="/payment-gateway-wallets"
              className={({ isActive }) =>
                `flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                }`
              }
            >
              <Wallet size={18} />
              <span>Wallet Setup</span>
            </NavLink>

          </nav>

          <div className="p-4 border-t border-border">
            <Card className="p-4">
              <div className="flex flex-col space-y-2">
                <div className="flex justify-between items-center">
                  <div className="text-xs text-muted-foreground">
                    Total Balance
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                  >
                    <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
                    <span className="sr-only">Refresh</span>
                  </Button>
                </div>

                {isLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-full" />
                  </div>
                ) : wallets.length > 0 ? (
                  <>
                    {wallets.map(wallet => (
                      <div key={wallet.id} className="font-medium">
                        <div className="text-sm font-semibold">{wallet.name}</div>
                        {wallet.tokens.map(token => (
                          <div key={token.type} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <CryptoLogo symbol={token.type} size="sm" />
                              <span className="text-sm">{token.balance} {token.type}</span>
                            </div>
                            <span className="text-xs text-muted-foreground ml-2">
                              ≈ ${token.dollarValue.toFixed(2)}
                            </span>
                          </div>
                        ))}
                        <div className="text-xs text-muted-foreground mt-1">
                          Total: ${wallet.totalDollarValue.toFixed(2)}
                        </div>
                      </div>
                    ))}
                  </>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    No wallets found
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </aside>

      <div className="flex-1 md:pl-64 w-full">
        {/* Mobile header with top navigation - extends to very top covering status bar */}
        <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-border">
          {/* Full header container that covers status bar area completely */}
          <div
            className="w-full bg-white"
            style={{
              paddingTop: 'max(env(safe-area-inset-top), 20px)',
              minHeight: 'calc(max(env(safe-area-inset-top), 20px) + 4.5rem)'
            }}
          >
            {/* Header content with proper spacing from status bar */}
            <div className="flex justify-between items-center w-full px-4 py-3">
              {/* Logo */}
              <div className="flex items-center">
                <img
                  src="/solana.png"
                  alt="solpay"
                  className="h-16 w-auto max-w-[280px] object-contain"
                  loading="eager"
                />
              </div>

              {/* Header Navigation */}
              <div className="flex items-center gap-2">
                {headerNavItems.map((item) => (
                  <NavLink
                    key={item.to}
                    to={item.to}
                    className={({ isActive }) =>
                      `relative p-2 rounded-lg transition-colors ${
                        isActive
                          ? "text-primary bg-primary/10"
                          : "text-muted-foreground hover:text-foreground"
                      }`
                    }
                  >
                    <item.icon size={18} />
                    {item.badge && (
                      <div className="notification-badge">
                        <span>{item.badge > 9 ? '9+' : item.badge}</span>
                      </div>
                    )}
                  </NavLink>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <main className="mobile-container py-4 max-w-full md:py-6 overflow-x-hidden" style={{
          marginTop: 'calc(max(env(safe-area-inset-top), 20px) + 5rem)',
          marginBottom: 'calc(5rem + env(safe-area-inset-bottom))',
          paddingBottom: '1rem'
        }}>
          <div className="w-full max-w-6xl mx-auto md:mt-0">
            {children}
          </div>
        </main>
      </div>

      {/* Mobile Bottom Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-card border-t border-border z-40" style={{
        paddingTop: '0.75rem',
        paddingBottom: 'calc(max(env(safe-area-inset-bottom), 0.5rem) + 0.75rem)'
      }}>
        <div className="grid grid-cols-7 gap-1 px-1">
          {bottomNavItems.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `flex items-center justify-center p-3 rounded-lg transition-colors relative ${
                  isActive
                    ? "text-primary bg-primary/10"
                    : "text-muted-foreground hover:text-foreground"
                }`
              }
            >
              <item.icon size={20} className="" />
            </NavLink>
          ))}
        </div>
      </nav>

      {/* PWA Install Prompt */}
      <PWAInstall />
    </div>
  );
};

export default Layout;
