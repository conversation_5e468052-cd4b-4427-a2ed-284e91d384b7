import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Wallet,
  Plus,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  ArrowUpDown,
  ExternalLink,
  Copy,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { CrossChainWalletService } from '@/services/crossChainWalletService';
import { 
  SupportedChain, 
  CrossChainWallet, 
  TokenBalance,
  CHAIN_CONFIGS 
} from '@/types/crossChain';
import { toast } from '@/hooks/use-toast';

interface CrossChainWalletOverviewProps {
  onCreateWallet?: (chain: SupportedChain) => void;
  onBridgeAssets?: () => void;
}

export const CrossChainWalletOverview: React.FC<CrossChainWalletOverviewProps> = ({
  onCreateWallet,
  onBridgeAssets
}) => {
  const { user } = useAuth();
  const [wallets, setWallets] = useState<CrossChainWallet[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [totalPortfolioValue, setTotalPortfolioValue] = useState(0);
  const [aggregatedBalances, setAggregatedBalances] = useState<Record<string, TokenBalance>>({});
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadWallets();
    }
  }, [user]);

  const loadWallets = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const userWallets = await CrossChainWalletService.getUserWallets(user.id);
      setWallets(userWallets);

      // Calculate total portfolio value
      const totalValue = await CrossChainWalletService.getTotalPortfolioValue(user.id);
      setTotalPortfolioValue(totalValue);

      // Get aggregated balances
      const aggregated = await CrossChainWalletService.getAggregatedBalances(user.id);
      setAggregatedBalances(aggregated);
    } catch (error) {
      console.error('Error loading wallets:', error);
      toast({
        title: "Error",
        description: "Failed to load wallets. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshBalances = async () => {
    if (!user) return;

    try {
      setRefreshing(true);
      await CrossChainWalletService.updateAllWalletBalances(user.id);
      await loadWallets(); // Reload to get updated balances
      
      toast({
        title: "Balances Updated! 🔄",
        description: "All wallet balances have been refreshed.",
      });
    } catch (error) {
      console.error('Error refreshing balances:', error);
      toast({
        title: "Error",
        description: "Failed to refresh balances. Please try again.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleCreateWallet = (chain: SupportedChain) => {
    if (onCreateWallet) {
      onCreateWallet(chain);
    }
  };

  const getChainIcon = (chain: SupportedChain) => {
    const config = CHAIN_CONFIGS[chain];
    return config?.logo || '/crypto-logos/default.svg';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatTokenAmount = (amount: string, decimals: number = 6) => {
    const num = parseFloat(amount);
    if (num === 0) return '0';
    if (num < 0.000001) return '< 0.000001';
    return num.toFixed(decimals);
  };

  const copyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address);
      setCopiedAddress(address);
      toast({
        title: "Address Copied! 📋",
        description: "Wallet address copied to clipboard",
      });
      setTimeout(() => setCopiedAddress(null), 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy address to clipboard",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading wallets...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Wallet className="h-5 w-5 text-blue-500" />
            <h2 className="text-lg font-semibold">Cross-Chain Portfolio</h2>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshBalances}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Updating...' : 'Refresh Balances'}
            </Button>
            {onBridgeAssets && (
              <Button
                variant="outline"
                size="sm"
                onClick={onBridgeAssets}
              >
                <ArrowUpDown className="h-4 w-4 mr-2" />
                Bridge Assets
              </Button>
            )}
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Total Portfolio Value</p>
            <p className="text-2xl font-bold">{formatCurrency(totalPortfolioValue)}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Active Chains</p>
            <p className="text-2xl font-bold">{wallets.length}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Total Assets</p>
            <p className="text-2xl font-bold">{Object.keys(aggregatedBalances).length}</p>
          </div>
        </div>
      </Card>

      {/* Supported Tokens Notice */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex items-start gap-3">
          <div className="bg-blue-100 rounded-full p-2">
            <Wallet className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <h3 className="font-medium text-blue-900 mb-1">Deposit Information</h3>
            <p className="text-sm text-blue-700">
              • <strong>Solana:</strong> You can deposit both SOL and USDC<br/>
              • <strong>Other chains:</strong> Only USDC deposits are supported<br/>
              • <strong>Wallet addresses:</strong> Use the addresses shown below for deposits
            </p>
          </div>
        </div>
      </Card>

      {/* Aggregated Token Balances */}
      {Object.keys(aggregatedBalances).length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Token Balances (All Chains)</h3>
          <div className="grid gap-3">
            {Object.entries(aggregatedBalances).map(([symbol, balance]) => (
              <div key={symbol} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <img 
                    src={`/crypto-logos/${symbol.toLowerCase()}.svg`} 
                    alt={symbol}
                    className="w-8 h-8"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/crypto-logos/default.svg';
                    }}
                  />
                  <div>
                    <p className="font-medium">{symbol}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatTokenAmount(balance.balance)} {symbol}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatCurrency(balance.balanceUSD)}</p>
                  <div className="flex items-center gap-1 text-sm">
                    {Math.random() > 0.5 ? (
                      <>
                        <TrendingUp className="h-3 w-3 text-green-500" />
                        <span className="text-green-500">+2.4%</span>
                      </>
                    ) : (
                      <>
                        <TrendingDown className="h-3 w-3 text-red-500" />
                        <span className="text-red-500">-1.2%</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Individual Chain Wallets */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Chain Wallets</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Show chain selection modal
              const availableChains = CrossChainWalletService.getSupportedChains()
                .filter(chain => !wallets.some(w => w.chain === chain));
              
              if (availableChains.length > 0) {
                handleCreateWallet(availableChains[0]);
              }
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Chain
          </Button>
        </div>

        <div className="grid gap-4">
          {wallets.map((wallet) => {
            const chainConfig = CHAIN_CONFIGS[wallet.chain];
            const totalValue = wallet.balances.reduce((sum, balance) => sum + balance.balanceUSD, 0);
            
            return (
              <div key={wallet.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <img 
                      src={getChainIcon(wallet.chain)} 
                      alt={chainConfig.name}
                      className="w-8 h-8"
                    />
                    <div>
                      <p className="font-medium">{chainConfig.name}</p>
                      <div className="flex items-center gap-2">
                        <p className="text-sm text-muted-foreground font-mono">
                          {wallet.address.slice(0, 8)}...{wallet.address.slice(-6)}
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => copyAddress(wallet.address)}
                        >
                          {copiedAddress === wallet.address ? (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                      <p className="text-xs text-blue-600 font-medium">
                        Deposit Address {wallet.chain === SupportedChain.SOLANA ? '(SOL & USDC)' : '(USDC only)'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(totalValue)}</p>
                    <Badge variant="outline" className="text-xs">
                      {wallet.balances.length} assets
                    </Badge>
                  </div>
                </div>

                {wallet.balances.length > 0 && (
                  <div className="space-y-2">
                    <Separator />
                    {wallet.balances.map((balance, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span>{balance.tokenSymbol}</span>
                        <div className="text-right">
                          <p>{formatTokenAmount(balance.balance)} {balance.tokenSymbol}</p>
                          <p className="text-muted-foreground">{formatCurrency(balance.balanceUSD)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex justify-end mt-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const explorerUrl = `${chainConfig.explorerUrl}/address/${wallet.address}`;
                      window.open(explorerUrl, '_blank');
                    }}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    View on Explorer
                  </Button>
                </div>
              </div>
            );
          })}
        </div>

        {wallets.length === 0 && (
          <div className="text-center py-8">
            <Wallet className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">No wallets created yet</p>
            <Button onClick={() => handleCreateWallet(SupportedChain.SOLANA)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Wallet
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};
