
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>solpay</title>
    <meta name="description" content="Get virtual cards for international payments and subscriptions. Secure BVN verification for African users." />
    <meta name="author" content="solpay" />

    <!-- Favicon - Use sola.png with larger sizes for better visibility -->
    <link rel="icon" type="image/png" sizes="64x64" href="/icon-192.png?v=7" />
    <link rel="icon" type="image/png" sizes="48x48" href="/icon-192.png?v=7" />
    <link rel="icon" type="image/png" sizes="32x32" href="/sola.png?v=7" />
    <link rel="icon" type="image/png" sizes="16x16" href="/sola.png?v=7" />
    <link rel="icon" type="image/png" href="/icon-192.png?v=7" />
    <link rel="shortcut icon" type="image/png" href="/icon-192.png?v=7" />
    <link rel="icon" href="/icon-192.png?v=7" />
    <link rel="apple-touch-icon" href="/icon-512.png?v=7" />

    <!-- Apple Touch Icons - Larger sizes for better visibility -->
    <link rel="apple-touch-icon" sizes="180x180" href="/icon-512.png?v=7" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icon-192.png?v=7" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icon-192.png?v=7" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icon-192.png?v=7" />
    <link rel="apple-touch-icon" sizes="114x114" href="/icon-192.png?v=7" />
    <link rel="apple-touch-icon" sizes="76x76" href="/sola.png?v=7" />
    <link rel="apple-touch-icon" sizes="72x72" href="/sola.png?v=7" />
    <link rel="apple-touch-icon" sizes="60x60" href="/sola.png?v=7" />
    <link rel="apple-touch-icon" sizes="57x57" href="/sola.png?v=7" />
    <link rel="apple-touch-icon" href="/icon-512.png?v=7" />

    <!-- PWA / Home Screen -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="solpay" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="solpay" />
    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta name="msapplication-TileImage" content="/sola.png?v=6" />

    <!-- Additional PWA meta tags for better appearance -->
    <meta name="theme-color" content="#ffffff" />
    <meta name="msapplication-navbutton-color" content="#ffffff" />

    <!-- iOS Safari specific - use default to blend with white header -->
    <meta name="format-detection" content="telephone=no" />

    <!-- Wallet browser compatibility -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />

    <!-- Security headers for wallet browsers -->
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />

    <!-- Android Chrome specific -->
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#ffffff" />

    <link rel="manifest" href="/manifest.json" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="solpay - Virtual Cards for Global Payments" />
    <meta property="og:description" content="Get virtual cards for international payments and subscriptions. Secure BVN verification for African users." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/solana.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:url" content="https://solpay.app" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@solpay_app" />
    <meta name="twitter:title" content="solpay - Virtual Cards for Global Payments" />
    <meta name="twitter:description" content="Get virtual cards for international payments and subscriptions. Secure BVN verification for African users." />
    <meta name="twitter:image" content="/solana.png" />
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
