
/**
 * Group Card Detail Page
 */
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, CreditCard, Users, Shield, MessageSquare, Receipt, AlertTriangle, Lock, Unlock } from 'lucide-react';
import { useGroupCard } from '@/contexts/GroupCardContext';
import { GroupCardStatus } from '@/types/groupCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import GroupCardOverview from '@/components/group-cards/GroupCardOverview';
import GroupCardMembers from '@/components/group-cards/GroupCardMembers';
import GroupCardRules from '@/components/group-cards/GroupCardRules';
import GroupCardChat from '@/components/group-cards/GroupCardChat';
import GroupCardTransactions from '@/components/group-cards/GroupCardTransactions';
import Layout from '@/components/Layout';

export default function GroupCardDetailPage() {
  const navigate = useNavigate();
  const { id } = useParams();
  const {
    selectedCard,
    isLoading,
    selectCard,
    emergencyLockCard,
    emergencyUnlockCard,
    deleteCard
  } = useGroupCard();
  const [activeTab, setActiveTab] = useState('overview');
  const [isEmergencyLocking, setIsEmergencyLocking] = useState(false);
  const [isEmergencyUnlocking, setIsEmergencyUnlocking] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Load card data when the page loads
  useEffect(() => {
    if (id) {
      try {
        // Check if id is a valid UUID
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (uuidRegex.test(id)) {
          selectCard(id);
        } else {
          console.error('Invalid card ID format');
          navigate('/group-cards');
        }
      } catch (error) {
        console.error('Error selecting card:', error);
        navigate('/group-cards');
      }
    }
  }, [id]); // Remove selectCard and navigate from dependencies to prevent infinite loop

  // Handle emergency lock
  const handleEmergencyLock = async () => {
    if (!selectedCard) return;

    setIsEmergencyLocking(true);
    try {
      await emergencyLockCard(selectedCard.id);
    } finally {
      setIsEmergencyLocking(false);
    }
  };

  // Handle emergency unlock
  const handleEmergencyUnlock = async () => {
    if (!selectedCard) return;

    setIsEmergencyUnlocking(true);
    try {
      await emergencyUnlockCard(selectedCard.id);
    } finally {
      setIsEmergencyUnlocking(false);
    }
  };

  // Handle card deletion
  const handleDeleteCard = async () => {
    if (!selectedCard) return;

    setIsDeleting(true);
    try {
      const success = await deleteCard(selectedCard.id);
      if (success) {
        navigate('/group-cards');
      }
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  // Handle back button
  const handleBack = () => {
    navigate('/group-cards');
  };

  // Render loading state
  if (isLoading || !selectedCard) {
    return (
      <Layout>
        <div className="container py-6 space-y-4">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Skeleton className="h-8 w-48" />
          </div>
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-6 space-y-6">
        {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-2">
            <span className="text-2xl">{selectedCard.emoji || '💳'}</span>
            <h1 className="text-2xl font-bold">{selectedCard.name}</h1>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {selectedCard.isEmergencyLocked ? (
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={handleEmergencyUnlock}
              disabled={isEmergencyUnlocking}
            >
              <Unlock className="h-4 w-4" />
              {isEmergencyUnlocking ? 'Unlocking...' : 'Emergency Unlock'}
            </Button>
          ) : (
            <Button
              variant="outline"
              className="flex items-center gap-2 text-destructive hover:text-destructive"
              onClick={handleEmergencyLock}
              disabled={isEmergencyLocking}
            >
              <Lock className="h-4 w-4" />
              {isEmergencyLocking ? 'Locking...' : 'Emergency Lock'}
            </Button>
          )}
          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="destructive">Delete Card</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Group Card</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this group card? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteCard}
                  disabled={isDeleting}
                >
                  {isDeleting ? 'Deleting...' : 'Delete Card'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Emergency Lock Alert */}
      {selectedCard.isEmergencyLocked && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Emergency Locked</AlertTitle>
          <AlertDescription>
            This card has been emergency locked. All transactions are blocked until the card is unlocked.
          </AlertDescription>
        </Alert>
      )}

      {/* Frozen Alert */}
      {selectedCard.status === GroupCardStatus.FROZEN && !selectedCard.isEmergencyLocked && (
        <Alert>
          <Lock className="h-4 w-4" />
          <AlertTitle>Card Frozen</AlertTitle>
          <AlertDescription>
            This card is currently frozen. Transactions are temporarily disabled.
          </AlertDescription>
        </Alert>
      )}

      {/* Card Info */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Card Details</CardTitle>
          <CardDescription>
            {selectedCard.description || 'No description provided'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Balance</p>
              <p className="text-2xl font-semibold">${selectedCard.balance.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Card Number</p>
              <p className="font-mono">
                {selectedCard.cardNumber ?
                  `${selectedCard.cardNumber.substring(0, 4)} **** **** ${selectedCard.cardNumber.substring(12)}` :
                  'Not available'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Status</p>
              <div className="flex items-center">
                {selectedCard.isEmergencyLocked ? (
                  <span className="text-destructive font-medium">Emergency Locked</span>
                ) : selectedCard.status === GroupCardStatus.FROZEN ? (
                  <span className="text-amber-500 font-medium">Frozen</span>
                ) : (
                  <span className="text-green-500 font-medium">Active</span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="members" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Members</span>
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            <span className="hidden sm:inline">Transactions</span>
          </TabsTrigger>
          <TabsTrigger value="rules" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Rules</span>
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span className="hidden sm:inline">Chat</span>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <GroupCardOverview />
        </TabsContent>
        <TabsContent value="members">
          <GroupCardMembers />
        </TabsContent>
        <TabsContent value="transactions">
          <GroupCardTransactions />
        </TabsContent>
        <TabsContent value="rules">
          <GroupCardRules />
        </TabsContent>
        <TabsContent value="chat">
          <GroupCardChat />
        </TabsContent>
      </Tabs>
      </div>
    </Layout>
  );
}
