/**
 * Cross-Chain Wallet Service
 * 
 * Manages wallets across multiple blockchain networks
 */

import { supabase } from '@/lib/supabase';
import { 
  SupportedChain, 
  CrossChainWallet, 
  TokenBalance, 
  CHAIN_CONFIGS 
} from '@/types/crossChain';

export class CrossChainWalletService {
  /**
   * Get all wallets for a user across all chains
   */
  static async getUserWallets(userId: string): Promise<CrossChainWallet[]> {
    try {
      const { data, error } = await supabase
        .from('cross_chain_wallets')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user wallets:', error);
      return [];
    }
  }

  /**
   * Get wallet for specific chain
   */
  static async getWalletForChain(userId: string, chain: SupportedChain): Promise<CrossChainWallet | null> {
    try {
      const { data, error } = await supabase
        .from('cross_chain_wallets')
        .select('*')
        .eq('user_id', userId)
        .eq('chain', chain)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error fetching ${chain} wallet:`, error);
      return null;
    }
  }

  /**
   * Create wallet for a specific chain
   */
  static async createWallet(userId: string, chain: SupportedChain): Promise<CrossChainWallet | null> {
    try {
      // Generate wallet based on chain type
      const walletData = await this.generateWalletForChain(chain);
      
      const { data, error } = await supabase
        .from('cross_chain_wallets')
        .insert({
          user_id: userId,
          chain,
          address: walletData.address,
          public_key: walletData.publicKey,
          encrypted_private_key: walletData.encryptedPrivateKey,
          is_active: true,
          balances: []
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error creating ${chain} wallet:`, error);
      return null;
    }
  }

  /**
   * Update wallet balances
   */
  static async updateWalletBalances(walletId: string, balances: TokenBalance[]): Promise<void> {
    try {
      const { error } = await supabase
        .from('cross_chain_wallets')
        .update({ 
          balances,
          updated_at: new Date().toISOString()
        })
        .eq('id', walletId);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating wallet balances:', error);
    }
  }

  /**
   * Get total portfolio value across all chains
   */
  static async getTotalPortfolioValue(userId: string): Promise<number> {
    try {
      const wallets = await this.getUserWallets(userId);
      let totalValue = 0;

      for (const wallet of wallets) {
        for (const balance of wallet.balances) {
          totalValue += balance.balanceUSD;
        }
      }

      return totalValue;
    } catch (error) {
      console.error('Error calculating portfolio value:', error);
      return 0;
    }
  }

  /**
   * Get aggregated balances across all chains
   */
  static async getAggregatedBalances(userId: string): Promise<Record<string, TokenBalance>> {
    try {
      const wallets = await this.getUserWallets(userId);
      const aggregated: Record<string, TokenBalance> = {};

      for (const wallet of wallets) {
        for (const balance of wallet.balances) {
          const key = balance.tokenSymbol;
          
          if (aggregated[key]) {
            // Aggregate balances for same token across chains
            const currentBalance = parseFloat(aggregated[key].balance);
            const newBalance = parseFloat(balance.balance);
            
            aggregated[key] = {
              ...aggregated[key],
              balance: (currentBalance + newBalance).toString(),
              balanceUSD: aggregated[key].balanceUSD + balance.balanceUSD,
              lastUpdated: new Date().toISOString()
            };
          } else {
            aggregated[key] = { ...balance };
          }
        }
      }

      return aggregated;
    } catch (error) {
      console.error('Error aggregating balances:', error);
      return {};
    }
  }

  /**
   * Generate wallet for specific chain
   */
  private static async generateWalletForChain(chain: SupportedChain): Promise<{
    address: string;
    publicKey?: string;
    encryptedPrivateKey: string;
  }> {
    switch (chain) {
      case SupportedChain.SOLANA:
        return this.generateSolanaWallet();
      
      case SupportedChain.ETHEREUM:
      case SupportedChain.POLYGON:
      case SupportedChain.BSC:
      case SupportedChain.ARBITRUM:
      case SupportedChain.AVALANCHE:
      case SupportedChain.BASE:
        return this.generateEVMWallet();
      
      default:
        throw new Error(`Unsupported chain: ${chain}`);
    }
  }

  /**
   * Generate Solana wallet
   */
  private static async generateSolanaWallet(): Promise<{
    address: string;
    publicKey: string;
    encryptedPrivateKey: string;
  }> {
    try {
      // Use @solana/web3.js to generate wallet
      const { Keypair } = await import('@solana/web3.js');
      const keypair = Keypair.generate();

      // Encrypt private key
      const encryptedPrivateKey = await this.encryptPrivateKey(
        Array.from(keypair.secretKey).join(',') // Convert Uint8Array to string
      );

      return {
        address: keypair.publicKey.toString(),
        publicKey: keypair.publicKey.toString(),
        encryptedPrivateKey
      };
    } catch (error) {
      console.error('Error generating Solana wallet:', error);
      throw new Error('Failed to generate Solana wallet');
    }
  }

  /**
   * Generate EVM wallet (Ethereum, Polygon, etc.)
   */
  private static async generateEVMWallet(): Promise<{
    address: string;
    encryptedPrivateKey: string;
  }> {
    try {
      // Use ethers.js to generate wallet
      const { ethers } = await import('ethers');
      const wallet = ethers.Wallet.createRandom();

      // Encrypt private key
      const encryptedPrivateKey = await this.encryptPrivateKey(wallet.privateKey);

      return {
        address: wallet.address,
        encryptedPrivateKey
      };
    } catch (error) {
      console.error('Error generating EVM wallet:', error);
      throw new Error('Failed to generate EVM wallet');
    }
  }

  /**
   * Encrypt private key using AES-256-GCM
   */
  private static async encryptPrivateKey(privateKey: string, userPassword?: string): Promise<string> {
    try {
      // Use Web Crypto API for encryption
      const encoder = new TextEncoder();
      const data = encoder.encode(privateKey);

      // Generate a random key (in production, derive from user password)
      const key = await crypto.subtle.generateKey(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );

      // Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(12));

      // Encrypt the private key
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        key,
        data
      );

      // Export the key for storage (in production, use key derivation)
      const exportedKey = await crypto.subtle.exportKey('raw', key);

      // Combine key, iv, and encrypted data
      const combined = new Uint8Array(exportedKey.byteLength + iv.length + encrypted.byteLength);
      combined.set(new Uint8Array(exportedKey), 0);
      combined.set(iv, exportedKey.byteLength);
      combined.set(new Uint8Array(encrypted), exportedKey.byteLength + iv.length);

      // Return base64 encoded result
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.error('Encryption error:', error);
      // Fallback to base64 (NOT SECURE)
      return Buffer.from(privateKey).toString('base64');
    }
  }

  /**
   * Decrypt private key using AES-256-GCM
   */
  private static async decryptPrivateKey(encryptedKey: string, userPassword?: string): Promise<string> {
    try {
      // Decode base64
      const combined = new Uint8Array(
        atob(encryptedKey).split('').map(char => char.charCodeAt(0))
      );

      // Extract components
      const keyData = combined.slice(0, 32); // 256 bits = 32 bytes
      const iv = combined.slice(32, 44); // 12 bytes for GCM
      const encrypted = combined.slice(44);

      // Import the key
      const key = await crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );

      // Decrypt
      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        key,
        encrypted
      );

      // Convert back to string
      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      console.error('Decryption error:', error);
      // Fallback to base64 decode (NOT SECURE)
      return Buffer.from(encryptedKey, 'base64').toString();
    }
  }

  /**
   * Get supported chains
   */
  static getSupportedChains(): SupportedChain[] {
    return Object.values(SupportedChain);
  }

  /**
   * Get chain configuration
   */
  static getChainConfig(chain: SupportedChain) {
    return CHAIN_CONFIGS[chain];
  }

  /**
   * Check if chain is supported
   */
  static isChainSupported(chain: string): chain is SupportedChain {
    return Object.values(SupportedChain).includes(chain as SupportedChain);
  }

  /**
   * Fetch balances for a specific wallet
   */
  static async fetchWalletBalances(wallet: CrossChainWallet): Promise<TokenBalance[]> {
    try {
      switch (wallet.chain) {
        case SupportedChain.SOLANA:
          return await this.fetchSolanaBalances(wallet.address);

        case SupportedChain.ETHEREUM:
        case SupportedChain.POLYGON:
        case SupportedChain.BSC:
        case SupportedChain.ARBITRUM:
        case SupportedChain.AVALANCHE:
        case SupportedChain.BASE:
          return await this.fetchEVMBalances(wallet.address, wallet.chain);

        default:
          console.warn(`Balance fetching not implemented for ${wallet.chain}`);
          return [];
      }
    } catch (error) {
      console.error(`Error fetching balances for ${wallet.chain}:`, error);
      return [];
    }
  }

  /**
   * Fetch Solana balances
   */
  private static async fetchSolanaBalances(address: string): Promise<TokenBalance[]> {
    try {
      const { Connection, PublicKey } = await import('@solana/web3.js');
      // Use free public RPC endpoint to avoid 403 errors
      const connection = new Connection('https://api.mainnet-beta.solana.com');
      const publicKey = new PublicKey(address);

      const balances: TokenBalance[] = [];

      // Get SOL balance
      const solBalance = await connection.getBalance(publicKey);
      const solBalanceFormatted = (solBalance / 1e9).toString();

      // Get SOL price from CoinGecko
      const solPrice = await this.getTokenPrice('solana');

      balances.push({
        tokenSymbol: 'SOL',
        tokenAddress: 'So11111111111111111111111111111111111111112',
        balance: solBalanceFormatted,
        balanceUSD: parseFloat(solBalanceFormatted) * solPrice,
        lastUpdated: new Date().toISOString()
      });

      // Get SPL token balances
      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(publicKey, {
        programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
      });

      for (const tokenAccount of tokenAccounts.value) {
        const tokenInfo = tokenAccount.account.data.parsed.info;
        const mint = tokenInfo.mint;
        const balance = tokenInfo.tokenAmount.uiAmount;

        if (balance > 0) {
          // Check if it's USDC
          if (mint === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') {
            const usdcPrice = await this.getTokenPrice('usd-coin'); // Real USDC market price
            balances.push({
              tokenSymbol: 'USDC',
              tokenAddress: mint,
              balance: balance.toString(),
              balanceUSD: balance * usdcPrice,
              lastUpdated: new Date().toISOString()
            });
          }
        }
      }

      return balances;
    } catch (error) {
      console.error('Error fetching Solana balances:', error);
      return [];
    }
  }

  /**
   * Fetch EVM chain balances - FIXED for proper USDC detection on Base
   */
  private static async fetchEVMBalances(address: string, chain: SupportedChain): Promise<TokenBalance[]> {
    try {
      console.log(`🔍 [${chain.toUpperCase()}] Fetching balances for address: ${address}`);
      
      const balances: TokenBalance[] = [];
      const chainConfig = CHAIN_CONFIGS[chain];

      if (!chainConfig) {
        console.error(`❌ [${chain.toUpperCase()}] No config found for chain`);
        return [];
      }

      // Use the most reliable RPC URLs for each chain with fallbacks
      let rpcUrl = chainConfig.rpcUrl;
      let fallbackRPCs: string[] = [];

      if (chain === SupportedChain.ETHEREUM) {
        fallbackRPCs = [
          'https://ethereum-rpc.publicnode.com',
          'https://eth.llamarpc.com',
          'https://rpc.ankr.com/eth',
          'https://ethereum.blockpi.network/v1/rpc/public'
        ];
        rpcUrl = fallbackRPCs[0];
      } else if (chain === SupportedChain.POLYGON) {
        fallbackRPCs = [
          'https://polygon-bor-rpc.publicnode.com',
          'https://polygon.llamarpc.com',
          'https://rpc.ankr.com/polygon',
          'https://polygon-rpc.com'
        ];
        rpcUrl = fallbackRPCs[0];
      } else if (chain === SupportedChain.BSC) {
        fallbackRPCs = [
          'https://bsc-rpc.publicnode.com',
          'https://rpc.ankr.com/bsc',
          'https://bsc-dataseed.binance.org',
          'https://bsc-dataseed1.defibit.io'
        ];
        rpcUrl = fallbackRPCs[0];
      } else if (chain === SupportedChain.ARBITRUM) {
        fallbackRPCs = [
          'https://arbitrum-one-rpc.publicnode.com',
          'https://rpc.ankr.com/arbitrum',
          'https://arb1.arbitrum.io/rpc'
        ];
        rpcUrl = fallbackRPCs[0];
      } else if (chain === SupportedChain.BASE) {
        fallbackRPCs = [
          'https://mainnet.base.org',
          'https://base.blockpi.network/v1/rpc/public',
          'https://base-mainnet.public.blastapi.io'
        ];
        rpcUrl = fallbackRPCs[0];
      }

      console.log(`🌐 [${chain.toUpperCase()}] Using RPC: ${rpcUrl}`);

      // Enhanced RPC call function with retry logic
      const makeRPCCall = async (method: string, params: any[], retryCount = 0): Promise<any> => {
        try {
          console.log(`📞 [${chain.toUpperCase()}] RPC Call: ${method}`, params);
          
          const response = await fetch(rpcUrl, {
            method: 'POST',
            headers: { 
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: JSON.stringify({
              jsonrpc: '2.0',
              method,
              params,
              id: Math.floor(Math.random() * 1000000)
            })
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          const data = await response.json();
          console.log(`📞 [${chain.toUpperCase()}] RPC Response for ${method}:`, data);
          
          if (data.error) {
            console.error(`❌ [${chain.toUpperCase()}] RPC Error:`, data.error);
            throw new Error(`RPC Error: ${data.error.message || 'Unknown error'}`);
          }
          
          return data.result;
        } catch (error) {
          console.error(`❌ [${chain.toUpperCase()}] RPC call failed for ${method}:`, error);
          
          // Retry logic with different RPC endpoints
          if (retryCount < fallbackRPCs.length - 1) {
            rpcUrl = fallbackRPCs[retryCount + 1];
            console.log(`🔄 [${chain.toUpperCase()}] Retrying with RPC: ${rpcUrl}`);
            return makeRPCCall(method, params, retryCount + 1);
          }
          
          throw error;
        }
      };

      // Check native token balance
      try {
        console.log(`🔍 [${chain.toUpperCase()}] Checking native token balance...`);
        const nativeBalance = await makeRPCCall('eth_getBalance', [address, 'latest']);
        
        if (nativeBalance && nativeBalance !== '0x0') {
          const balance = parseInt(nativeBalance, 16) / Math.pow(10, chainConfig.nativeCurrency.decimals);
          console.log(`✅ [${chain.toUpperCase()}] Native balance: ${balance} ${chainConfig.nativeCurrency.symbol}`);
          
          if (balance > 0) {
            let tokenPrice = 0;
            try {
              switch (chain) {
                case SupportedChain.ETHEREUM:
                case SupportedChain.BASE:
                case SupportedChain.ARBITRUM:
                  tokenPrice = await this.getTokenPrice('ethereum');
                  break;
                case SupportedChain.POLYGON:
                  tokenPrice = await this.getTokenPrice('matic-network');
                  break;
                case SupportedChain.BSC:
                  tokenPrice = await this.getTokenPrice('binancecoin');
                  break;
                case SupportedChain.AVALANCHE:
                  tokenPrice = await this.getTokenPrice('avalanche-2');
                  break;
              }
            } catch (priceError) {
              console.warn(`⚠️ [${chain.toUpperCase()}] Failed to get native token price:`, priceError);
            }

            balances.push({
              tokenSymbol: chainConfig.nativeCurrency.symbol,
              tokenAddress: '******************************************',
              balance: balance.toString(),
              balanceUSD: balance * tokenPrice,
              lastUpdated: new Date().toISOString()
            });
          }
        }
      } catch (error) {
        console.error(`❌ [${chain.toUpperCase()}] Error checking native balance:`, error);
      }

      // Check ERC-20 token balances with enhanced detection
      const supportedTokens = chainConfig.supportedTokens || [];
      console.log(`🔍 [${chain.toUpperCase()}] Checking ${supportedTokens.length} ERC-20 tokens...`);

      for (const token of supportedTokens) {
        if (token.address === '******************************************') {
          continue; // Skip native token
        }

        try {
          console.log(`🪙 [${chain.toUpperCase()}] Checking ${token.symbol} at ${token.address}...`);
          
          // Prepare the balanceOf call with proper formatting
          const functionSelector = '0x70a08231'; // balanceOf(address) function signature

          // Ensure address is properly formatted (remove 0x, pad to 64 chars)
          let cleanAddress = address.toLowerCase();
          if (cleanAddress.startsWith('0x')) {
            cleanAddress = cleanAddress.slice(2);
          }

          // Validate address length
          if (cleanAddress.length !== 40) {
            console.warn(`❌ [${chain.toUpperCase()}] Invalid address length for ${token.symbol}: ${cleanAddress.length} chars`);
            continue;
          }

          const addressParam = cleanAddress.padStart(64, '0');
          const callData = functionSelector + addressParam;

          console.log(`📞 [${chain.toUpperCase()}] ${token.symbol} balanceOf call data: ${callData}`);

          // Make the contract call
          const result = await makeRPCCall('eth_call', [
            {
              to: token.address,
              data: callData
            },
            'latest'
          ]);

          console.log(`📊 [${chain.toUpperCase()}] ${token.symbol} raw result: ${result}`);

          // Parse the result
          if (result && result !== '0x' && result !== '0x0' && result !== '0x00') {
            const rawBalance = BigInt(result);
            const balance = Number(rawBalance) / Math.pow(10, token.decimals);
            
            console.log(`💰 [${chain.toUpperCase()}] ${token.symbol} balance found: ${balance} (raw: ${rawBalance.toString()}, decimals: ${token.decimals})`);
            
            if (balance > 0) {
              // Get token price - simplified for USDC-only chains
              let tokenPrice = 0;
              try {
                if (token.symbol === 'USDC') {
                  tokenPrice = 1.00; // USDC is always $1
                  console.log(`💲 [${chain.toUpperCase()}] USDC price: $${tokenPrice}`);
                } else {
                  // For non-Solana chains, we only support USDC, so this shouldn't happen
                  console.warn(`⚠️ [${chain.toUpperCase()}] Unexpected token: ${token.symbol}`);
                  tokenPrice = 0;
                }
              } catch (priceError) {
                console.warn(`⚠️ [${chain.toUpperCase()}] Failed to get price for ${token.symbol}:`, priceError);
              }

              const tokenBalance = {
                tokenSymbol: token.symbol,
                tokenAddress: token.address,
                balance: balance.toString(),
                balanceUSD: balance * tokenPrice,
                lastUpdated: new Date().toISOString()
              };

              console.log(`✅ [${chain.toUpperCase()}] Adding ${token.symbol} balance:`, tokenBalance);
              balances.push(tokenBalance);
            }
          } else {
            console.log(`❌ [${chain.toUpperCase()}] No ${token.symbol} balance (result: ${result})`);
          }
        } catch (error) {
          console.error(`❌ [${chain.toUpperCase()}] Error checking ${token.symbol}:`, error);
        }

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      console.log(`🎯 [${chain.toUpperCase()}] Total balances found: ${balances.length}`, balances);
      return balances;
    } catch (error) {
      console.error(`❌ [${chain.toUpperCase()}] Error fetching balances:`, error);
      return [];
    }
  }

  /**
   * Get token price with multiple fallback methods
   */
  private static async getTokenPrice(coingeckoId: string): Promise<number> {
    // Since we only support USDC on non-Solana chains, simplify pricing
    if (coingeckoId === 'usd-coin') {
      return 1.00; // USDC is always $1
    }

    if (coingeckoId === 'solana') {
      // Only fetch SOL price since it's the only non-stablecoin we support
      const fallbackPrices: Record<string, number> = {
        'solana': 180.50
      };

      try {
        // Method 1: Try our CORS-free API endpoint
        try {
          const response = await fetch(`/api/crypto-prices?ids=${coingeckoId}`);
          if (response.ok) {
            const data = await response.json();
            const price = data[coingeckoId]?.usd;
            if (price > 0) {
              console.log(`💲 Got ${coingeckoId} price from API: $${price}`);
              return price;
            }
          }
        } catch (apiError) {
          console.warn('API endpoint unavailable:', apiError);
        }

        // Method 2: Try existing exchange rate service
        try {
          const { exchangeRateService } = await import('./exchangeRateService');
          const rate = await exchangeRateService.getCryptoToUSDRate('SOL');
          if (rate > 0) {
            console.log(`💲 Got SOL price from exchange service: $${rate}`);
            return rate;
          }
        } catch (exchangeError) {
          console.warn('Exchange rate service unavailable:', exchangeError);
        }

      } catch (error) {
        console.warn('All price services unavailable, using fallback');
      }

      // Method 3: Use fallback price for SOL
      const fallbackPrice = fallbackPrices[coingeckoId] || 180.50;
      console.log(`💲 Using fallback price for ${coingeckoId}: $${fallbackPrice}`);
      return fallbackPrice;
    }

    // For any other token, return 0 (shouldn't happen with our simplified setup)
    console.warn(`💲 Unsupported token: ${coingeckoId}`);
    return 0;
  }

  /**
   * Update all wallet balances for a user
   */
  static async updateAllWalletBalances(userId: string): Promise<void> {
    try {
      const wallets = await this.getUserWallets(userId);

      for (const wallet of wallets) {
        const balances = await this.fetchWalletBalances(wallet);
        await this.updateWalletBalances(wallet.id, balances);
      }
    } catch (error) {
      console.error('Error updating all wallet balances:', error);
    }
  }
}
