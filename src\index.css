
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 272 100% 64%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 189 100% 50%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 272 100% 64%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 272 100% 64%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 189 100% 50%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 272 100% 64%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  html {
    font-size: 16px;
    scroll-behavior: smooth;
    /* Support for PWA full-screen */
    height: 100%;
    width: 100%;
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
    min-height: 100vh;
    /* Support for PWA full-screen */
    min-height: 100dvh; /* Dynamic viewport height */
    position: relative;
  }

  /* PWA specific styles to ensure clean white background */
  @media (display-mode: standalone) {
    html, body {
      background-color: #ffffff !important;
    }

    #root {
      background-color: #ffffff !important;
    }

    /* Ensure PWA header covers everything */
    body {
      padding-top: 0 !important;
      margin-top: 0 !important;
    }
  }

  /* iOS PWA specific styles */
  @media (display-mode: standalone) and (-webkit-touch-callout: none) {
    html, body {
      background-color: #ffffff !important;
    }
  }

  #root {
    width: 100%;
    min-height: 100vh;
    overflow-x: hidden;
  }
}

@layer components {
  .wallet-address {
    font-family: ui-monospace, SFMono-Regular, "SF Mono", monospace;
    font-size: 0.875rem;
    background-color: hsl(var(--muted));
    padding: 0.75rem;
    border-radius: 0.375rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    max-width: calc(100vw - 5rem);
  }

  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: '';
    position: absolute;
    inset: 0;
    transform: translateX(-100%);
    animation: shimmer 2s infinite linear;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  }

  .subscription-card {
    border-radius: 0.5rem;
    border: 1px solid hsl(var(--border));
    background-color: hsl(var(--card));
    padding: 0.75rem 1rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    transition: all 0.15s ease-in-out;
    width: 100%;
  }

  .subscription-card:hover {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .gradient-text {
    background: linear-gradient(to right, #9945FF, #00C2FF);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .crypto-badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    background-color: rgba(148, 69, 255, 0.1);
    padding: 0.125rem 0.625rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: hsl(var(--primary));
  }

  /* Mobile-first responsive containers */
  .mobile-container {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
    margin-left: auto;
    margin-right: auto;
    max-width: 100vw;
  }

  .mobile-card {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  .mobile-grid {
    display: grid;
    gap: 1rem;
    width: 100%;
  }

  .mobile-flex {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }
}

/* Virtual Card Flip Animation */
.perspective-card {
  height: 14rem;
  width: 100%;
  max-width: 24rem;
  margin-left: auto;
  margin-right: auto;
  perspective: 1000px;
}

@media (min-width: 640px) {
  .perspective-card {
    height: 16rem;
  }
}

@media (min-width: 768px) {
  .perspective-card {
    height: 18rem;
  }
}

.card-flip {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.5s;
  transform-style: preserve-3d;
}

.card-flip.is-flipped {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.card-back {
  transform: rotateY(180deg);
}

/* Scrollbar styles */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/* Ensure sidebar has max height and scrolls */
aside nav {
  max-height: calc(100vh - 64px - 150px);
  overflow-y: auto;
}

/* Enhanced mobile optimizations for better touch experience */
@media (max-width: 768px) {
  html {
    font-size: 16px; /* Increased from 14px */
  }

  html, body {
    overflow-x: hidden;
    position: relative;
    width: 100%;
    max-width: 100vw;
  }

  * {
    max-width: 100vw;
    box-sizing: border-box;
  }

  .container {
    padding-left: 1rem; /* Increased from 0.75rem */
    padding-right: 1rem;
    width: 100%;
    max-width: 100% !important;
    margin: 0 auto;
  }

  /* Enhanced card responsiveness with better spacing */
  .subscription-card,
  .card,
  .mobile-card {
    max-width: 100%;
    overflow: hidden;
    width: 100% !important;
    min-width: 0;
    padding: 1rem; /* Increased padding */
  }

  /* Better table handling */
  .overflow-auto,
  .overflow-x-auto {
    margin-left: -1rem; /* Adjusted for new padding */
    margin-right: -1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: calc(100vw - 2rem);
  }

  /* Improved text handling with larger sizes */
  h1, h2, h3, h4, h5, h6, p, span, div {
    word-break: break-words;
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Better spacing for mobile */
  .space-y-4 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 1rem; /* Increased from 0.75rem */
  }

  .space-y-6 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 1.5rem; /* Increased from 1rem */
  }

  /* Enhanced main content area */
  main {
    width: 100% !important;
    max-width: 100vw !important;
    padding-left: 1rem !important; /* Increased from 0.75rem */
    padding-right: 1rem !important;
    padding-bottom: 6rem !important; /* Increased from 5rem */
    overflow-x: hidden;
    box-sizing: border-box;
  }

  /* Better mobile navigation with larger touch targets */
  nav.fixed {
    padding-bottom: env(safe-area-inset-bottom, 0.75rem); /* Increased padding */
    width: 100vw;
    left: 0;
    right: 0;
    padding-top: 1rem; /* Added top padding */
  }

  /* PWA Mobile Header - Clean white header that covers status bar */
  @media (max-width: 768px) {
    /* Ensure body doesn't scroll behind header */
    body {
      padding-top: 0;
      margin-top: 0;
      background-color: #ffffff;
    }

    /* Make sure main content starts below header with proper spacing */
    main.mobile-container {
      margin-top: calc(max(env(safe-area-inset-top), 20px) + 5rem) !important;
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }
  }

  /* PWA specific header adjustments */
  @media (display-mode: standalone) and (max-width: 768px) {
    /* Ensure header background extends to very top in PWA mode */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: max(env(safe-area-inset-top), 20px);
      background-color: #ffffff;
      z-index: 60;
    }
  }

  /* Notification badge positioning */
  .notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #ef4444;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    border: 2px solid hsl(var(--card));
  }

  /* Enhanced dropdown handling */
  .select-content,
  .dropdown-content {
    max-width: 90vw;
    left: 50% !important;
    transform: translateX(-50%);
  }

  /* Better table responsiveness */
  table {
    width: 100%;
    table-layout: auto;
    min-width: 0;
  }

  th, td {
    font-size: 1rem; /* Increased from 0.875rem */
    padding: 0.75rem; /* Increased from 0.5rem */
    min-width: 0;
    word-break: break-word;
  }

  /* Enhanced form elements */
  input, select, textarea {
    width: 100%;
    max-width: 100%;
    padding: 0.75rem; /* Increased padding */
    font-size: 1rem; /* Increased font size */
  }

  /* Better button sizing with larger touch targets */
  .btn, button {
    font-size: 1rem; /* Increased from 0.875rem */
    min-height: 48px; /* Increased from 44px for better touch */
    padding: 0.75rem 1rem; /* Increased padding */
  }

  /* Enhanced grid layouts */
  .grid {
    gap: 1rem; /* Increased from 0.75rem */
  }

  /* Better flex layouts */
  .flex-row {
    flex-direction: column;
  }

  @media (min-width: 640px) {
    .flex-row {
      flex-direction: row;
    }
  }

  /* Enhanced modal and dialog sizing */
  .dialog-content,
  .modal-content {
    margin-left: 1rem; /* Increased from 0.75rem */
    margin-right: 1rem;
    max-width: calc(100vw - 2rem); /* Adjusted for new margins */
  }

  /* Better card layouts with increased spacing */
  .card-content {
    padding: 1rem; /* Increased from 0.75rem */
  }

  .card-header {
    padding: 1rem; /* Increased from 0.75rem */
    padding-bottom: 0.75rem; /* Increased from 0.5rem */
  }

  .card-footer {
    padding: 1rem; /* Increased from 0.75rem */
    padding-top: 0.75rem; /* Increased from 0.5rem */
  }

  /* Enhanced utility classes for mobile */
  .mobile-hidden {
    display: none;
  }

  .mobile-full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }

  /* Better aspect ratios for mobile */
  .aspect-video {
    aspect-ratio: 1 / 1;
  }

  @media (min-width: 640px) {
    .aspect-video {
      aspect-ratio: 16 / 9;
    }
  }

  /* Enhanced text sizing for better readability */
  .text-lg {
    font-size: 1.125rem; /* Increased from 1rem */
  }

  .text-xl {
    font-size: 1.25rem; /* Increased from 1.125rem */
  }

  .text-2xl {
    font-size: 1.5rem; /* Increased from 1.25rem */
  }

  .text-3xl {
    font-size: 1.875rem; /* Increased from 1.5rem */
  }

  /* Better padding and margins */
  .p-6 {
    padding: 1.25rem; /* Increased from 1rem */
  }

  .p-8 {
    padding: 1.5rem; /* Increased from 1rem */
  }

  .py-6 {
    padding-top: 1.25rem; /* Increased from 1rem */
    padding-bottom: 1.25rem;
  }

  .py-8 {
    padding-top: 1.5rem; /* Increased from 1rem */
    padding-bottom: 1.5rem;
  }

  .px-6 {
    padding-left: 1.25rem; /* Increased from 1rem */
    padding-right: 1.25rem;
  }

  .px-8 {
    padding-left: 1.5rem; /* Increased from 1rem */
    padding-right: 1.5rem;
  }

  .m-6 {
    margin: 1.25rem; /* Increased from 1rem */
  }

  .m-8 {
    margin: 1.5rem; /* Increased from 1rem */
  }
}

/* Optimized small mobile devices with better spacing */
@media (max-width: 480px) {
  html {
    font-size: 15px; /* Increased from 13px */
  }

  .container {
    padding-left: 0.75rem; /* Increased from 0.5rem */
    padding-right: 0.75rem;
  }

  main {
    padding-left: 0.75rem !important; /* Increased from 0.5rem */
    padding-right: 0.75rem !important;
  }

  .card-content,
  .card-header,
  .card-footer {
    padding: 0.75rem; /* Increased from 0.5rem */
  }

  th, td {
    font-size: 0.875rem; /* Increased from 0.75rem */
    padding: 0.5rem; /* Increased from 0.25rem */
  }

  .text-sm {
    font-size: 0.875rem; /* Increased from 0.75rem */
  }

  .text-base {
    font-size: 1rem; /* Increased from 0.875rem */
  }
}

/* Enhanced tablet responsiveness */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding-left: 1.5rem; /* Increased from 1rem */
    padding-right: 1.5rem;
    max-width: 100%;
  }

  main {
    padding-left: 1.5rem !important; /* Increased from 1rem */
    padding-right: 1.5rem !important;
  }
}

/* Keyframes for animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Logo optimization for crisp rendering */
img[alt="solpay"] {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimizeQuality;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  filter: contrast(1.1) brightness(1.05);
  min-height: 48px !important;
  min-width: 120px !important;
}

/* Force larger logo sizes on mobile */
@media (max-width: 768px) {
  img[alt="solpay"] {
    min-height: 40px !important;
    min-width: 100px !important;
  }
}

/* Ensure logo containers don't constrain size */
.logo-container {
  min-height: 60px !important;
  min-width: 150px !important;
}

/* PWA specific styles */
@media (display-mode: standalone) {
  body {
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  /* Hide scrollbars in standalone mode */
  ::-webkit-scrollbar {
    display: none;
  }
}

/* Safe area support for mobile devices */
@supports (padding: max(0px)) {
  .pt-safe-top {
    padding-top: max(env(safe-area-inset-top), 1rem);
  }

  .pb-safe-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 0.75rem);
  }
}

/* Mobile navigation styles */
@media (max-width: 768px) {
  /* Ensure main content doesn't overlap with navigation */
  main {
    padding-bottom: calc(6.5rem + env(safe-area-inset-bottom)) !important;
  }

  /* Mobile header - no floating, integrated with status bar */
  .mobile-header {
    background-color: hsl(var(--card));
    border-bottom: 1px solid hsl(var(--border));
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 30;
    padding-top: max(env(safe-area-inset-top), 0.5rem);
    padding-bottom: 0.75rem;
  }

  /* Bottom navigation with proper spacing */
  .mobile-bottom-nav {
    height: calc(6.5rem + env(safe-area-inset-bottom));
    padding-top: 1rem;
    padding-bottom: calc(max(env(safe-area-inset-bottom), 0.75rem) + 1rem);
  }

  /* Prevent content from showing behind header */
  body {
    background-color: hsl(var(--card));
  }
}

/* Enhanced mobile navigation */
.mobile-nav-item {
  transition: all 0.2s ease-in-out;
  border-radius: 0.5rem;
  min-height: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.mobile-nav-item:active {
  transform: scale(0.95);
  background-color: hsl(var(--primary) / 0.1);
}

/* Improve touch targets for mobile navigation */
@media (max-width: 768px) {
  .mobile-nav-item {
    min-height: 4rem;
    padding: 0.75rem 0.5rem;
  }

  .mobile-nav-item span {
    font-size: 0.75rem;
    line-height: 1;
    margin-top: 0.25rem;
  }
}

/* Notification badge positioning */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  height: 16px;
  width: 16px;
  background-color: #ef4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: white;
  line-height: 1;
  border: 1px solid hsl(var(--card));
}

/* Minimal Mobile Camera Fixes */
@media (max-width: 768px) {
  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px !important;
  }

  /* Basic video element fixes */
  video {
    -webkit-playsinline: true;
    playsinline: true;
  }
}


