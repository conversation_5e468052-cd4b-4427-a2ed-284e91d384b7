
import React, { useEffect, useState } from "react";
import { useCard } from "@/contexts/CardContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield, Plus, RefreshCw, AlertCircle, MapPin } from "lucide-react";
import { CardStatus } from "@/types/card";
import Layout from "@/components/Layout";
import NoCardState from "@/components/card/NoCardState";
import { toast } from "@/hooks/use-toast";
import CardDetailView from "@/components/card/CardDetailView";

export default function SimpleVirtualCard() {
  const { card, refreshCard, isLoading, freezeCard, unfreezeCard, resetCardBalance } = useCard();
  const [hasTriedRefresh, setHasTriedRefresh] = useState(false);
  const [localLoading, setLocalLoading] = useState(false);
  
  // Use refreshCard only once when component mounts
  useEffect(() => {
    // Only try to refresh once to prevent infinite loops
    if (!hasTriedRefresh) {
      setHasTriedRefresh(true);
      setLocalLoading(true);
      
      // Force refresh to ensure we get the latest state from Supabase
      refreshCard()
        .catch(err => {
          console.error("Error refreshing card:", err);
        })
        .finally(() => {
          setLocalLoading(false);
        });
    }
  }, [hasTriedRefresh, refreshCard]);

  // Handle freezing the card
  const handleFreezeCard = async () => {
    if (!card) return;
    setLocalLoading(true);
    try {
      await freezeCard();
    } finally {
      setLocalLoading(false);
    }
  };

  // Handle unfreezing the card
  const handleUnfreezeCard = async () => {
    if (!card) return;
    setLocalLoading(true);
    try {
      await unfreezeCard();
    } finally {
      setLocalLoading(false);
    }
  };
  
  // Handle resetting the card balance
  const handleResetBalance = async () => {
    if (!card) return;
    setLocalLoading(true);
    try {
      const success = await resetCardBalance(card.id);
      if (success) {
        toast({
          title: "Balance Reset",
          description: "Your card balance has been reset to zero."
        });
      }
    } catch (err) {
      console.error("Error resetting balance:", err);
      toast({
        title: "Reset Failed",
        description: "Failed to reset the card balance.",
        variant: "destructive"
      });
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <Layout>
      <div className="mobile-grid gap-4 md:gap-6 w-full">
        <div className="flex justify-between items-center">
          <h1 className="text-lg md:text-xl font-semibold">Virtual Card (Simple View)</h1>
        </div>
      
        {isLoading || localLoading ? (
          <div className="flex justify-center items-center h-48 md:h-64">
            <div className="animate-spin rounded-full h-8 w-8 md:h-12 md:w-12 border-b-2 border-primary"></div>
          </div>
        ) : card ? (
          <div className="mobile-grid gap-4 md:gap-6">
            <Card className="mobile-card overflow-hidden">
              <div className={`bg-gradient-to-r ${card.status === CardStatus.FROZEN ? "from-slate-700 to-slate-800" : "from-indigo-500 to-purple-600"} p-4 md:p-6 text-white`}>
                <div className="flex justify-between items-start">
                  <div className="min-w-0 flex-1">
                    <p className="text-xs md:text-sm opacity-80">Virtual Card</p>
                    <h2 className="text-lg md:text-xl font-bold truncate">{card.name}</h2>
                  </div>
                  <div className="uppercase font-bold text-sm md:text-base ml-2">
                    {card.provider}
                  </div>
                </div>

                <div className="mt-4 md:mt-6">
                  <p className="text-xs md:text-sm opacity-80 mb-1">Card Number</p>
                  <p className="font-mono text-base md:text-xl tracking-wider">
                    {card.maskedNumber}
                  </p>
                </div>

                <div className="flex justify-between mt-3 md:mt-4 gap-4">
                  <div className="min-w-0 flex-1">
                    <p className="text-xs opacity-80 mb-1">Valid Thru</p>
                    <p className="font-mono text-sm">{card.expiry}</p>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-xs opacity-80 mb-1">CVV</p>
                    <p className="font-mono text-sm">***</p>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-xs opacity-80 mb-1">Status</p>
                    <p className="font-medium uppercase text-sm truncate">
                      {card.status}
                    </p>
                  </div>
                </div>
              </div>

              <CardContent className="p-4 md:p-6">
                <div className="mobile-flex md:flex-row justify-between gap-4">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-base md:text-lg font-semibold mb-3">Card Details</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground text-sm">Name:</span>
                        <span className="font-medium text-sm truncate ml-2">{card.name}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground text-sm">Balance:</span>
                        <span className="font-medium text-sm">${card.balance.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground text-sm">Spent This Month:</span>
                        <span className="font-medium text-sm">${card.spentThisMonth.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground text-sm">Created:</span>
                        <span className="font-medium text-sm">{card.createdAt.toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    {/* Billing address section */}
                    {card.billingAddress && (
                      <div className="mt-4">
                        <h3 className="text-base md:text-lg font-semibold flex items-center gap-1 mb-2">
                          <MapPin className="h-4 w-4" /> 
                          Billing Address
                        </h3>
                        <div className="space-y-1 text-sm">
                          {card.billingAddress.street && (
                            <p className="break-words">{card.billingAddress.street}</p>
                          )}
                          {card.billingAddress.city && card.billingAddress.state && (
                            <p className="break-words">
                              {card.billingAddress.city}, {card.billingAddress.state} {card.billingAddress.zipCode || card.billingAddress.postalCode}
                            </p>
                          )}
                          {card.billingAddress.country && (
                            <p className="break-words">{card.billingAddress.country}</p>
                          )}
                        </div>
                      </div>
                    )}
                    
                    <div className="flex flex-wrap gap-2 mt-4">
                      {card.status === CardStatus.ACTIVE ? (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={handleFreezeCard}
                          className="flex items-center gap-1"
                        >
                          <Shield className="h-4 w-4" />
                          <span className="hidden sm:inline">Freeze Card</span>
                          <span className="sm:hidden">Freeze</span>
                        </Button>
                      ) : (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={handleUnfreezeCard}
                          className="flex items-center gap-1"
                        >
                          <Shield className="h-4 w-4" />
                          <span className="hidden sm:inline">Unfreeze Card</span>
                          <span className="sm:hidden">Unfreeze</span>
                        </Button>
                      )}
                      
                      {card.balance > 0 && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={handleResetBalance}
                          className="flex items-center gap-1"
                        >
                          <AlertCircle className="h-4 w-4" />
                          <span className="hidden sm:inline">Reset Balance</span>
                          <span className="sm:hidden">Reset</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Card Detail View with full card information */}
            {card && (
              <div className="mobile-card">
                <CardDetailView card={card} />
              </div>
            )}
          </div>
        ) : (
          <div className="mobile-card">
            <NoCardState />
          </div>
        )}
      </div>
    </Layout>
  );
}
