/**
 * Error Handler Middleware
 * 
 * Centralized error handling for API routes
 */

import { Request, Response, NextFunction } from 'express';

interface ApiError extends Error {
  statusCode?: number;
  type?: string;
  code?: string;
}

export const errorHandler = (
  error: ApiError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('API Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    headers: req.headers
  });

  // Default error response
  let statusCode = error.statusCode || 500;
  let errorType = error.type || 'internal_error';
  let message = error.message || 'An unexpected error occurred';

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    errorType = 'validation_error';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    errorType = 'invalid_request';
    message = 'Invalid ID format';
  } else if (error.code === '23505') { // PostgreSQL unique violation
    statusCode = 409;
    errorType = 'duplicate_resource';
    message = 'Resource already exists';
  } else if (error.code === '23503') { // PostgreSQL foreign key violation
    statusCode = 400;
    errorType = 'invalid_reference';
    message = 'Referenced resource does not exist';
  }

  // Don't expose internal errors in production
  if (statusCode === 500 && process.env.NODE_ENV === 'production') {
    message = 'Internal server error';
  }

  res.status(statusCode).json({
    error: {
      type: errorType,
      message: message,
      ...(process.env.NODE_ENV === 'development' && {
        stack: error.stack,
        details: error
      })
    }
  });
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export const createError = (statusCode: number, type: string, message: string): ApiError => {
  const error = new Error(message) as ApiError;
  error.statusCode = statusCode;
  error.type = type;
  return error;
};
