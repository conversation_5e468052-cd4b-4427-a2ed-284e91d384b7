
/**
 * Exchange Rate Service
 * 
 * Fetches and manages exchange rates between cryptocurrencies and fiat currencies
 */

import { CryptoType } from '@/types/wallet';
import { reliableExchangeRateService } from './reliableExchangeRateService';

export interface ExchangeRate {
  fromCurrency: CryptoType | string;
  toCurrency: string;
  rate: number;
  lastUpdated: Date;
}

export interface RateLock {
  id: string;
  fromCurrency: CryptoType;
  toCurrency: string;
  lockedRate: number;
  expiresAt: Date;
}

export function formatRate(rate: number): string {
  return rate.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

class ExchangeRateService {
  private rates: { [key: string]: ExchangeRate } = {};
  private subscribers: { [key: string]: ((rate: ExchangeRate) => void)[] } = {};
  private updateInterval: number = 30000; // Update every 30 seconds
  private rateLocks: { [key: string]: RateLock } = {};

  constructor() {
    this.init();
  }

  private async init() {
    await this.fetchRealTimeRates();
    setInterval(() => this.fetchRealTimeRates(), this.updateInterval);
    // Clean up expired rate locks every 5 minutes
    setInterval(() => this.cleanupExpiredLocks(), 5 * 60 * 1000);
  }

  async getExchangeRate(fromCurrency: CryptoType | string, toCurrency: string): Promise<ExchangeRate | null> {
    const key = `${fromCurrency}-${toCurrency}`;
    return this.rates[key] || null;
  }

  /**
   * Get exchange rate synchronously (for rate locking)
   */
  getExchangeRateSync(fromCurrency: CryptoType | string, toCurrency: string): ExchangeRate | null {
    const key = `${fromCurrency}-${toCurrency}`;
    return this.rates[key] || null;
  }

  subscribeToRateUpdates(
    fromCurrency: CryptoType | string,
    toCurrency: string,
    callback: (rate: ExchangeRate) => void
  ): () => void {
    const key = `${fromCurrency}-${toCurrency}`;
    if (!this.subscribers[key]) {
      this.subscribers[key] = [];
    }
    this.subscribers[key].push(callback);

    // Return unsubscribe function
    return () => {
      this.subscribers[key] = this.subscribers[key].filter(sub => sub !== callback);
      if (this.subscribers[key].length === 0) {
        delete this.subscribers[key];
      }
    };
  }

  private notifySubscribers(rate: ExchangeRate): void {
    const key = `${rate.fromCurrency}-${rate.toCurrency}`;
    if (this.subscribers[key]) {
      this.subscribers[key].forEach(callback => callback(rate));
    }
  }

  private async fetchRealTimeRates(retryCount: number = 0): Promise<void> {
    try {
      console.log('🔄 Fetching real-time exchange rates...');

      // Step 1: Use reliable USD to NGN rate
      const usdToNgnRate = 1540; // Use stable rate to avoid API issues
      console.log('💱 Using USD to NGN rate:', usdToNgnRate);

      // Step 2: Get crypto prices using reliable service
      const reliableRates = await reliableExchangeRateService.getCurrentRates();
      console.log('📊 Reliable service response:', reliableRates);

      // Convert to CoinGecko-like format for compatibility
      const data = {
        'solana': { usd: reliableRates.SOL / usdToNgnRate },
        'usd-coin': { usd: reliableRates.USDC / usdToNgnRate }
      };

      // Map CoinGecko data to our exchange rates
      const coinMapping = {
        'solana': 'SOL',
        'usd-coin': 'USDC',
        'bitcoin': 'BTC',
        'ethereum': 'ETH',
        'matic-network': 'MATIC',
        'binancecoin': 'BNB',
        'avalanche-2': 'AVAX',
        'arbitrum': 'ARB'
      };

      // Step 3: Set USDC rates - Use REAL market price (not fixed $1.00)
      let usdcPrice = 1.00; // Default fallback
      if (data['usd-coin'] && data['usd-coin'].usd) {
        usdcPrice = data['usd-coin'].usd; // Real USDC market price
      }

      const usdcToNgnRate = usdcPrice * usdToNgnRate; // Real USDC price × NGN rate

      const usdcUsdRate: ExchangeRate = {
        fromCurrency: 'USDC' as CryptoType,
        toCurrency: 'USD',
        rate: usdcPrice, // Real market price for USDC
        lastUpdated: new Date(),
      };
      this.rates['USDC-USD'] = usdcUsdRate;
      this.notifySubscribers(usdcUsdRate);

      const usdcNgnRate: ExchangeRate = {
        fromCurrency: 'USDC' as CryptoType,
        toCurrency: 'NGN',
        rate: usdcToNgnRate,
        lastUpdated: new Date(),
      };
      this.rates['USDC-NGN'] = usdcNgnRate;
      this.notifySubscribers(usdcNgnRate);

      console.log(`💰 USDC: $${usdcPrice} (₦${usdcToNgnRate.toLocaleString()})`);

      // Step 4: Set other crypto rates with proper USDC conversion flow
      for (const [coinId, symbol] of Object.entries(coinMapping)) {
        if (symbol === 'USDC') continue; // Already handled above

        if (data[coinId] && data[coinId].usd) {
          // Use real market prices for ALL tokens (including stablecoins)
          const usdPrice = data[coinId].usd;

          // USD rate (for reference)
          const usdExchangeRate: ExchangeRate = {
            fromCurrency: symbol as CryptoType,
            toCurrency: 'USD',
            rate: usdPrice,
            lastUpdated: new Date(),
          };
          this.rates[`${symbol}-USD`] = usdExchangeRate;
          this.notifySubscribers(usdExchangeRate);

          // USDC rate (how much USDC you get for 1 unit of this token)
          // Since USDC = $1, 1 SOL ($149) = 149 USDC
          const usdcExchangeRate: ExchangeRate = {
            fromCurrency: symbol as CryptoType,
            toCurrency: 'USDC',
            rate: usdPrice, // 1 SOL = $149 = 149 USDC
            lastUpdated: new Date(),
          };
          this.rates[`${symbol}-USDC`] = usdcExchangeRate;
          this.notifySubscribers(usdcExchangeRate);

          // NGN rate (Token → USD → USDC → NGN)
          // Example: 1 SOL = $149 = 149 USDC = 149 × NGN_rate
          const ngnPrice = usdPrice * usdcToNgnRate;
          const ngnExchangeRate: ExchangeRate = {
            fromCurrency: symbol as CryptoType,
            toCurrency: 'NGN',
            rate: ngnPrice,
            lastUpdated: new Date(),
          };
          this.rates[`${symbol}-NGN`] = ngnExchangeRate;
          this.notifySubscribers(ngnExchangeRate);

          console.log(`💰 ${symbol}: $${usdPrice} → ${usdPrice} USDC → ₦${ngnPrice.toLocaleString()}`);
        }
      }

      console.log('✅ Real-time exchange rates updated successfully');
    } catch (error) {
      console.error('❌ Error fetching real-time rates:', error);

      // Retry logic - try up to 3 times with increasing delays
      if (retryCount < 3) {
        const delay = (retryCount + 1) * 5000; // 5s, 10s, 15s
        console.log(`🔄 Retrying in ${delay/1000} seconds... (attempt ${retryCount + 1}/3)`);
        setTimeout(() => this.fetchRealTimeRates(retryCount + 1), delay);
      } else {
        console.error('❌ Failed to fetch rates after 3 attempts. Will try again in next cycle.');
      }
    }
  }

  private async fetchFallbackRates(): Promise<void> {
    console.log('🔄 Using fallback exchange rates...');

    // Current fallback rates (updated December 2024) - 0.5% better than market
    const marketUsdToNgn = 1540; // Current market rate
    const currentUsdToNgn = marketUsdToNgn * 1.005; // 0.5% better for customers

    const fallbackRates = {
      // USDC rates - Base currency, always $1
      'USDC-NGN': currentUsdToNgn,
      'USDC-USD': 1.00,

      // Solana rates - Current price ~$150.93 → 150.93 USDC → NGN
      'SOL-NGN': 150.93 * currentUsdToNgn,  // 1 SOL = $150.93 = 150.93 USDC = 150.93 × NGN_rate
      'SOL-USD': 150.93,
      'SOL-USDC': 150.93,  // 1 SOL = 150.93 USDC

      // Bitcoin rates - Current price ~$100,000 → 100,000 USDC → NGN
      'BTC-NGN': 100000 * currentUsdToNgn,
      'BTC-USD': 100000,
      'BTC-USDC': 100000,  // 1 BTC = 100,000 USDC

      // Ethereum rates - Current price ~$3,900 → 3,900 USDC → NGN
      'ETH-NGN': 3900 * currentUsdToNgn,
      'ETH-USD': 3900,
      'ETH-USDC': 3900,  // 1 ETH = 3,900 USDC

      // Polygon MATIC rates - Current price ~$0.50 → 0.50 USDC → NGN
      'MATIC-NGN': 0.50 * currentUsdToNgn,
      'MATIC-USD': 0.50,
      'MATIC-USDC': 0.50,  // 1 MATIC = 0.50 USDC

      // BNB rates - Current price ~$700 → 700 USDC → NGN
      'BNB-NGN': 700 * currentUsdToNgn,
      'BNB-USD': 700,
      'BNB-USDC': 700,  // 1 BNB = 700 USDC

      // Avalanche AVAX rates - Current price ~$45 → 45 USDC → NGN
      'AVAX-NGN': 45 * currentUsdToNgn,
      'AVAX-USD': 45,
      'AVAX-USDC': 45,  // 1 AVAX = 45 USDC

      // Arbitrum ARB rates - Current price ~$0.90 → 0.90 USDC → NGN
      'ARB-NGN': 0.90 * currentUsdToNgn,
      'ARB-USD': 0.90,
      'ARB-USDC': 0.90  // 1 ARB = 0.90 USDC
    };

    for (const key in fallbackRates) {
      const [fromCurrency, toCurrency] = key.split('-');
      const rate = fallbackRates[key];
      const exchangeRate: ExchangeRate = {
        fromCurrency: fromCurrency as CryptoType,
        toCurrency,
        rate,
        lastUpdated: new Date(),
      };
      this.rates[key] = exchangeRate;
      this.notifySubscribers(exchangeRate);
    }

    console.log(`💰 Fallback rates loaded with USDC base: ₦${currentUsdToNgn} per USDC`);
  }

  /**
   * Lock a rate for a specific duration
   */
  lockRate(fromCurrency: CryptoType, toCurrency: string, durationMinutes: number = 5): RateLock | null {
    const currentRate = this.getExchangeRateSync(fromCurrency, toCurrency);
    if (!currentRate) return null;

    const lockId = `${fromCurrency}-${toCurrency}-${Date.now()}`;
    const rateLock: RateLock = {
      id: lockId,
      fromCurrency,
      toCurrency,
      lockedRate: currentRate.rate,
      expiresAt: new Date(Date.now() + durationMinutes * 60 * 1000)
    };

    this.rateLocks[lockId] = rateLock;
    return rateLock;
  }

  /**
   * Get a locked rate by ID
   */
  getLockedRate(lockId: string): RateLock | null {
    const lock = this.rateLocks[lockId];
    if (!lock || lock.expiresAt < new Date()) {
      delete this.rateLocks[lockId];
      return null;
    }
    return lock;
  }

  /**
   * Use a locked rate (mark it as consumed)
   */
  useLockedRate(lockId: string): boolean {
    const lock = this.rateLocks[lockId];
    if (!lock || lock.expiresAt < new Date()) {
      delete this.rateLocks[lockId];
      return false;
    }
    delete this.rateLocks[lockId];
    return true;
  }

  /**
   * Clean up expired rate locks
   */
  private cleanupExpiredLocks(): void {
    const now = new Date();
    for (const [lockId, lock] of Object.entries(this.rateLocks)) {
      if (lock.expiresAt < now) {
        delete this.rateLocks[lockId];
      }
    }
  }
}

// Export as singleton
export const exchangeRateService = new ExchangeRateService();
