import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface PaymentData {
  id: string;
  amount: number;
  currency: string;
  business_name: string;
  title: string;
}

export default function SimplePaymentPage() {
  const { paymentId } = useParams<{ paymentId: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // Mock payment data - this works without any database calls
  const paymentData: PaymentData = {
    id: paymentId || '',
    amount: 10.00,
    currency: 'USD',
    business_name: 'Fezola Business',
    title: 'Payment gig'
  };

  const handlePayment = async () => {
    setLoading(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setLoading(false);
      toast({
        title: "Payment Successful!",
        description: "Your payment has been processed successfully.",
      });
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-black/20 backdrop-blur-sm border-purple-500/20">
        <CardHeader className="text-center">
          <CardTitle className="text-white text-2xl">
            {paymentData.title}
          </CardTitle>
          <p className="text-gray-300">
            {paymentData.business_name}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <div className="text-4xl font-bold text-white">
              ${paymentData.amount.toFixed(2)}
            </div>
            <div className="text-gray-400">
              {paymentData.currency}
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 bg-purple-500/20 rounded-lg border border-purple-500/30">
              <div className="flex items-center justify-between">
                <span className="text-white">Solana (SOL)</span>
                <span className="text-purple-300">◎</span>
              </div>
              <div className="text-sm text-gray-400 mt-1">
                Fast & Low Fees
              </div>
            </div>

            <Button 
              onClick={handlePayment}
              disabled={loading}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3"
            >
              {loading ? 'Processing...' : 'Pay with Solana'}
            </Button>
          </div>

          <div className="text-center text-sm text-gray-400">
            Payment ID: {paymentData.id}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
