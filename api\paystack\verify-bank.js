/**
 * Paystack Bank Verification API
 * Verifies Nigerian bank account details
 */

export default async function handler(req, res) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { accountNumber, bankCode } = req.body;

  if (!accountNumber || !bankCode) {
    return res.status(400).json({ 
      success: false,
      error: 'Account number and bank code are required' 
    });
  }

  const paystackSecretKey = process.env.PAYSTACK_SECRET_KEY;
  
  if (!paystackSecretKey) {
    return res.status(500).json({ 
      success: false,
      error: 'Paystack not configured' 
    });
  }

  try {
    // Step 1: Resolve account name
    const resolveResponse = await fetch(
      `https://api.paystack.co/bank/resolve?account_number=${accountNumber}&bank_code=${bankCode}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${paystackSecretKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const resolveData = await resolveResponse.json();

    if (!resolveData.status) {
      return res.status(400).json({
        success: false,
        error: resolveData.message || 'Could not resolve account details'
      });
    }

    const accountName = resolveData.data.account_name;

    // Step 2: Create transfer recipient (for future payouts)
    const recipientResponse = await fetch('https://api.paystack.co/transferrecipient', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paystackSecretKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'nuban',
        name: accountName,
        account_number: accountNumber,
        bank_code: bankCode,
        currency: 'NGN'
      }),
    });

    const recipientData = await recipientResponse.json();

    if (!recipientData.status) {
      console.error('Failed to create recipient:', recipientData);
      // Still return success for account verification, just without recipient code
    }

    return res.status(200).json({
      success: true,
      data: {
        account_name: accountName,
        account_number: accountNumber,
        bank_code: bankCode,
        recipient_code: recipientData.status ? recipientData.data.recipient_code : null,
        verified: true
      }
    });

  } catch (error) {
    console.error('Bank verification error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to verify bank account'
    });
  }
}
