/**
 * Payment Link Page
 * 
 * Customer-facing page for payment links
 * Accessible via /link/:url_slug
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import {
  CreditCard,
  Wallet,
  QrCode,
  Copy,
  ExternalLink,
  CheckCircle,
  Clock,
  Shield,
  Zap,
  ArrowRight,
  AlertCircle,
  DollarSign,
  Minus,
  Plus
} from 'lucide-react';

interface PaymentLink {
  id: string;
  title: string;
  description?: string;
  amount?: number;
  currency: string;
  is_amount_fixed: boolean;
  min_amount?: number;
  max_amount?: number;
  accepted_cryptocurrencies: string[];
  is_active: boolean;
  expires_at?: string;
  max_uses?: number;
  current_uses: number;
  merchant: {
    business_name: string;
    business_logo?: string;
  };
}

const PaymentLinkPage: React.FC = () => {
  const { url_slug } = useParams<{ url_slug: string }>();
  const navigate = useNavigate();
  const [paymentLink, setPaymentLink] = useState<PaymentLink | null>(null);
  const [loading, setLoading] = useState(true);
  const [customAmount, setCustomAmount] = useState<number>(0);
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');
  const [paymentStep, setPaymentStep] = useState<'amount' | 'crypto' | 'processing'>('amount');
  const { toast } = useToast();

  useEffect(() => {
    if (url_slug) {
      loadPaymentLink();
    }
  }, [url_slug]);

  const loadPaymentLink = async () => {
    if (!url_slug) return;

    setLoading(true);
    try {
      // This would call an API to get the payment link
      // For now, we'll simulate with mock data
      const mockPaymentLink: PaymentLink = {
        id: 'plink_1234567890',
        title: 'Premium Subscription',
        description: 'Get access to all premium features with our monthly subscription plan. Includes unlimited access, priority support, and exclusive content.',
        amount: 29.99,
        currency: 'USD',
        is_amount_fixed: true,
        accepted_cryptocurrencies: ['SOL', 'USDC', 'ETH'],
        is_active: true,
        max_uses: 100,
        current_uses: 23,
        merchant: {
          business_name: 'TechCorp Solutions',
          business_logo: undefined
        }
      };

      // Check if link is active and not expired
      if (!mockPaymentLink.is_active) {
        toast({
          title: "Payment Link Inactive",
          description: "This payment link is no longer active.",
          variant: "destructive",
        });
        navigate('/');
        return;
      }

      // Check usage limits
      if (mockPaymentLink.max_uses && mockPaymentLink.current_uses >= mockPaymentLink.max_uses) {
        toast({
          title: "Payment Link Expired",
          description: "This payment link has reached its usage limit.",
          variant: "destructive",
        });
        navigate('/');
        return;
      }

      setPaymentLink(mockPaymentLink);
      
      // Set initial amount for variable amount links
      if (!mockPaymentLink.is_amount_fixed && mockPaymentLink.min_amount) {
        setCustomAmount(mockPaymentLink.min_amount);
      }
    } catch (error) {
      console.error('Error loading payment link:', error);
      toast({
        title: "Error",
        description: "Failed to load payment link",
        variant: "destructive",
      });
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const handleAmountChange = (value: number) => {
    if (!paymentLink) return;
    
    const min = paymentLink.min_amount || 0;
    const max = paymentLink.max_amount || Infinity;
    
    if (value < min) {
      setCustomAmount(min);
    } else if (value > max) {
      setCustomAmount(max);
    } else {
      setCustomAmount(value);
    }
  };

  const handleCryptoSelection = (crypto: string) => {
    setSelectedCrypto(crypto);
    setPaymentStep('processing');
    
    // Create payment intent and redirect to payment page
    createPaymentIntent(crypto);
  };

  const createPaymentIntent = async (crypto: string) => {
    if (!paymentLink) return;

    try {
      const amount = paymentLink.is_amount_fixed ? paymentLink.amount! : customAmount;
      
      // This would call the payment gateway API to create a payment intent
      // For now, we'll simulate the creation and redirect
      const paymentIntentId = `pi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      toast({
        title: "Redirecting to Payment",
        description: "Creating your payment request...",
      });

      // Simulate API delay
      setTimeout(() => {
        navigate(`/pay/${paymentIntentId}`);
      }, 2000);

    } catch (error) {
      console.error('Error creating payment intent:', error);
      toast({
        title: "Error",
        description: "Failed to create payment request",
        variant: "destructive",
      });
      setPaymentStep('crypto');
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getCryptoIcon = (crypto: string) => {
    const icons = {
      'SOL': '◎',
      'USDC': '💵',
      'ETH': 'Ξ',
      'BTC': '₿'
    };
    return icons[crypto as keyof typeof icons] || '🪙';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!paymentLink) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Payment Link Not Found</h2>
            <p className="text-gray-600">
              The payment link you're looking for doesn't exist or has expired.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              {paymentLink.merchant.business_logo ? (
                <img 
                  src={paymentLink.merchant.business_logo} 
                  alt={paymentLink.merchant.business_name}
                  className="w-12 h-12 rounded-full"
                />
              ) : (
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {paymentLink.merchant.business_name.charAt(0)}
                </div>
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{paymentLink.merchant.business_name}</h1>
                <p className="text-gray-600">Secure crypto payment</p>
              </div>
            </div>
          </div>

          {/* Payment Card */}
          <Card className="shadow-xl">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
              <CardTitle className="text-2xl">{paymentLink.title}</CardTitle>
              {paymentLink.description && (
                <CardDescription className="text-blue-100">
                  {paymentLink.description}
                </CardDescription>
              )}
            </CardHeader>

            <CardContent className="p-6">
              {paymentStep === 'amount' && (
                <div className="space-y-6">
                  {/* Amount Section */}
                  <div className="text-center">
                    {paymentLink.is_amount_fixed ? (
                      <div>
                        <div className="text-4xl font-bold text-gray-900 mb-2">
                          {formatAmount(paymentLink.amount!, paymentLink.currency)}
                        </div>
                        <p className="text-gray-600">Fixed amount</p>
                      </div>
                    ) : (
                      <div>
                        <Label className="text-lg font-semibold">Enter Amount</Label>
                        <div className="flex items-center justify-center gap-4 mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAmountChange(customAmount - 1)}
                            disabled={customAmount <= (paymentLink.min_amount || 0)}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          
                          <div className="text-center">
                            <Input
                              type="number"
                              value={customAmount}
                              onChange={(e) => handleAmountChange(parseFloat(e.target.value) || 0)}
                              className="text-2xl font-bold text-center w-32"
                              min={paymentLink.min_amount}
                              max={paymentLink.max_amount}
                            />
                            <p className="text-sm text-gray-500 mt-1">
                              {paymentLink.min_amount && paymentLink.max_amount && (
                                `${formatAmount(paymentLink.min_amount, paymentLink.currency)} - ${formatAmount(paymentLink.max_amount, paymentLink.currency)}`
                              )}
                            </p>
                          </div>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAmountChange(customAmount + 1)}
                            disabled={customAmount >= (paymentLink.max_amount || Infinity)}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Continue Button */}
                  <Button
                    onClick={() => setPaymentStep('crypto')}
                    className="w-full"
                    disabled={!paymentLink.is_amount_fixed && customAmount <= 0}
                  >
                    Continue to Payment
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>

                  {/* Security Info */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Shield className="h-4 w-4" />
                      <span>Secured by blockchain technology</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                      <Zap className="h-4 w-4" />
                      <span>Fast and low-cost transactions</span>
                    </div>
                  </div>
                </div>
              )}

              {paymentStep === 'crypto' && (
                <div className="space-y-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setPaymentStep('amount')}
                    >
                      ← Back
                    </Button>
                    <div className="text-center flex-1">
                      <h3 className="text-lg font-semibold">Choose Payment Method</h3>
                      <p className="text-sm text-gray-600">
                        Amount: {formatAmount(paymentLink.is_amount_fixed ? paymentLink.amount! : customAmount, paymentLink.currency)}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {paymentLink.accepted_cryptocurrencies.map((crypto) => (
                      <Button
                        key={crypto}
                        variant="outline"
                        className="h-20 flex flex-col items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                        onClick={() => handleCryptoSelection(crypto)}
                      >
                        <span className="text-2xl">{getCryptoIcon(crypto)}</span>
                        <span className="font-semibold">{crypto}</span>
                      </Button>
                    ))}
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-2">Why choose crypto?</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Lower transaction fees</li>
                      <li>• Instant global payments</li>
                      <li>• Enhanced security and privacy</li>
                      <li>• No chargebacks or reversals</li>
                    </ul>
                  </div>
                </div>
              )}

              {paymentStep === 'processing' && (
                <div className="text-center space-y-6">
                  <div className="flex justify-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                      <Clock className="h-8 w-8 text-blue-600 animate-spin" />
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Creating Payment Request</h3>
                    <p className="text-gray-600">
                      Please wait while we prepare your crypto payment...
                    </p>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="text-sm text-blue-800">
                      <div><strong>Amount:</strong> {formatAmount(paymentLink.is_amount_fixed ? paymentLink.amount! : customAmount, paymentLink.currency)}</div>
                      <div><strong>Payment Method:</strong> {selectedCrypto}</div>
                      <div><strong>Merchant:</strong> {paymentLink.merchant.business_name}</div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-gray-500">
            <p>Powered by Your Crypto Payment Platform</p>
            <p>Secure • Fast • Global</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentLinkPage;
