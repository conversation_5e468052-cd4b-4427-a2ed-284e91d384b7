
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const onfidoApiToken = Deno.env.get('ONFIDO_API_TOKEN')!;
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create authenticated Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Verify authentication
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { action } = await req.json();

    if (action === 'create_applicant') {
      // Create an Onfido applicant
      const response = await fetch('https://api.onfido.com/v3/applicants', {
        method: 'POST',
        headers: {
          'Authorization': `Token token=${onfidoApiToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          first_name: user.user_metadata.first_name || '',
          last_name: user.user_metadata.last_name || '',
          email: user.email
        })
      });

      if (!response.ok) {
        throw new Error(`Onfido API error: ${response.status}`);
      }

      const applicantData = await response.json();

      // Create a workflow run (SDK token)
      const workflowResponse = await fetch('https://api.onfido.com/v3/workflow_runs', {
        method: 'POST',
        headers: {
          'Authorization': `Token token=${onfidoApiToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          workflow_id: Deno.env.get('ONFIDO_WORKFLOW_ID')!,
          applicant_id: applicantData.id
        })
      });

      if (!workflowResponse.ok) {
        throw new Error(`Onfido workflow API error: ${workflowResponse.status}`);
      }

      const workflowData = await workflowResponse.json();

      // Get an SDK token
      const tokenResponse = await fetch('https://api.onfido.com/v3/sdk_token', {
        method: 'POST',
        headers: {
          'Authorization': `Token token=${onfidoApiToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          applicant_id: applicantData.id,
          referrer: '*://*/*' // Allow embedding on any domain (for development)
        })
      });

      if (!tokenResponse.ok) {
        throw new Error(`Onfido token API error: ${tokenResponse.status}`);
      }

      const tokenData = await tokenResponse.json();

      // Update user profile with applicant ID for future reference
      await supabase
        .from('profiles')
        .update({
          kyc_applicant_id: applicantData.id,
          kyc_status: 'pending'
        })
        .eq('id', user.id);

      return new Response(
        JSON.stringify({
          sdkToken: tokenData.token,
          workflowRunId: workflowData.id
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    } else if (action === 'check_status') {
      // Get the applicant ID from the profiles table
      const { data: profile } = await supabase
        .from('profiles')
        .select('kyc_applicant_id, kyc_status')
        .eq('id', user.id)
        .single();

      if (!profile?.kyc_applicant_id) {
        return new Response(
          JSON.stringify({ status: 'not_started' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Check the status with Onfido
      const response = await fetch(`https://api.onfido.com/v3/applicants/${profile.kyc_applicant_id}/checks`, {
        method: 'GET',
        headers: {
          'Authorization': `Token token=${onfidoApiToken}`
        }
      });

      if (!response.ok) {
        throw new Error(`Onfido API error: ${response.status}`);
      }

      const checksData = await response.json();
      
      // Get the most recent check
      const latestCheck = checksData.checks && checksData.checks.length > 0 
        ? checksData.checks.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]
        : null;
      
      let status = profile.kyc_status || 'pending';
      
      if (latestCheck) {
        if (latestCheck.status === 'complete' && latestCheck.result === 'clear') {
          status = 'verified';
        } else if (latestCheck.status === 'complete' && latestCheck.result !== 'clear') {
          status = 'rejected';
        }
        
        // Update the status in the database if it's changed
        if (status !== profile.kyc_status) {
          await supabase
            .from('profiles')
            .update({ kyc_status: status })
            .eq('id', user.id);
        }
      }

      return new Response(
        JSON.stringify({ status }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ error: 'Invalid action' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error processing request:', error);
    
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
