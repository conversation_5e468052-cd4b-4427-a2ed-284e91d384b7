import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ingAddress } from '@/types/card';

// BridgeCard Service using your existing server proxy
export class BridgeCardService {
  private baseUrl: string;

  constructor() {
    // Use your existing server proxy instead of direct API calls
    this.baseUrl = '/api/bridgecard-test';
    console.log(`🔧 BridgeCard Service: Using server proxy at ${this.baseUrl}`);
  }

  /**
   * Check if BridgeCard service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/connection`);
      const result = await response.json();
      return result.success;
    } catch (error) {
      console.warn('⚠️ BridgeCard not available:', error);
      return false;
    }
  }

  /**
   * Register a user as a cardholder using your form data
   */
  async registerCardholder(userData: {
    fullName: string;
    dateOfBirth: string;
    email: string;
    phoneNumber?: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  }): Promise<string> {
    try {
      console.log('🧪 Registering cardholder with BridgeCard:', userData.fullName);

      const response = await fetch(`${this.baseUrl}/cardholder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const result = await response.json();

      if (result.success && result.data?.cardholder_id) {
        console.log('✅ Cardholder registered successfully:', result.data.cardholder_id);
        return result.data.cardholder_id;
      } else {
        // Check if cardholder already exists
        if (result.error?.message?.includes('already exists')) {
          console.log('ℹ️ Cardholder already exists, extracting ID...');
          if (result.error?.data?.cardholder_id) {
            return result.error.data.cardholder_id;
          }
        }
        throw new Error(result.message || 'Failed to register cardholder');
      }
    } catch (error) {
      console.error('❌ Cardholder registration failed:', error);
      throw error;
    }
  }

  /**
   * Get cardholder details and verification status
   */
  async getCardholderDetails(cardholderID: string): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/cardholder/get_cardholder?cardholder_id=${cardholderID}`,
        {
          method: 'GET',
          headers: {
            'token': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const result = await response.json();

      if (result.status === 'success') {
        return result.data;
      } else {
        throw new Error(result.message || 'Failed to get cardholder details');
      }
    } catch (error) {
      console.error('Error getting cardholder details:', error);
      throw error;
    }
  }

  /**
   * Create a virtual card for a cardholder
   */
  async createVirtualCard(
    cardholderID: string,
    cardName: string,
    provider: CardProvider = CardProvider.MASTERCARD
  ): Promise<VirtualCard> {
    try {
      console.log('🧪 Creating virtual card for cardholder:', cardholderID);

      const response = await fetch(`${this.baseUrl}/card`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cardholder_id: cardholderID,
          card_name: cardName,
          card_provider: provider,
        }),
      });

      const result = await response.json();

      if (result.success && result.data?.card_id) {
        console.log('✅ Virtual card created successfully:', result.data.card_id);

        // Get the card details
        const cardDetails = await this.getCardDetails(result.data.card_id);
        return cardDetails;
      } else {
        throw new Error(result.message || 'Failed to create virtual card');
      }
    } catch (error) {
      console.error('❌ Virtual card creation failed:', error);
      throw error;
    }
  }

  /**
   * Get card details using secure token method
   */
  async getCardDetails(cardID: string): Promise<VirtualCard> {
    try {
      console.log('🧪 Getting card details for:', cardID);

      // Step 1: Generate secure token
      const tokenResponse = await fetch(`${this.baseUrl}/generate-card-token/${cardID}`);
      const tokenResult = await tokenResponse.json();

      if (!tokenResult.success || !tokenResult.data?.token) {
        throw new Error('Failed to generate card token');
      }

      // Step 2: Get decrypted card details
      const detailsResponse = await fetch(`${this.baseUrl}/get-decrypted-details/${tokenResult.data.token}`);
      const detailsResult = await detailsResponse.json();

      if (!detailsResult.success || !detailsResult.data) {
        throw new Error('Failed to get card details');
      }

      const cardData = detailsResult.data;

      // Convert BridgeCard response to VirtualCard format
      const virtualCard: VirtualCard = {
        id: cardData.card_id,
        name: cardData.cardholder_name || 'Virtual Card',
        number: cardData.card_number,
        maskedNumber: `•••• •••• •••• ${cardData.card_number.slice(-4)}`,
        expiry: `${cardData.expiry_month}/${cardData.expiry_year}`,
        cvv: cardData.cvv,
        provider: CardProvider.MASTERCARD, // BridgeCard uses Mastercard
        status: cardData.status === 'Active' ? CardStatus.ACTIVE : CardStatus.FROZEN,
        balance: parseInt(cardData.balance) / 100, // Convert from cents to dollars
        spentThisMonth: 0,
        createdAt: new Date(),
        billingAddress: cardData.billing_address ? {
          street: cardData.billing_address.billing_address1,
          city: cardData.billing_address.billing_city,
          state: cardData.billing_address.state || cardData.billing_address.billing_state,
          zipCode: cardData.billing_address.billing_zip_code,
          country: cardData.billing_address.billing_country,
        } : undefined,
      };

      console.log('✅ Card details retrieved successfully');
      return virtualCard;
    } catch (error) {
      console.error('❌ Failed to get card details:', error);
      throw error;
    }
  }

  /**
   * Fund a virtual card
   */
  async fundCard(cardID: string, amount: string, currency: string = 'USD'): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/cards/fund_card_asynchronously`, {
        method: 'PATCH',
        headers: {
          'token': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          card_id: cardID,
          amount: amount, // In cents
          currency: currency
        })
      });

      const result = await response.json();
      return result.status === 'success';
    } catch (error) {
      console.error('Error funding card:', error);
      return false;
    }
  }

  /**
   * Freeze/Unfreeze card
   */
  async toggleCardStatus(cardID: string, action: 'freeze' | 'unfreeze'): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/cards/${action}_card`, {
        method: 'PATCH',
        headers: {
          'token': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          card_id: cardID
        })
      });

      const result = await response.json();
      return result.status === 'success';
    } catch (error) {
      console.error(`Error ${action}ing card:`, error);
      return false;
    }
  }

  /**
   * Delete a card
   */
  async deleteCard(cardID: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/cards/delete_card`, {
        method: 'DELETE',
        headers: {
          'token': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          card_id: cardID
        })
      });

      const result = await response.json();
      return result.status === 'success';
    } catch (error) {
      console.error('Error deleting card:', error);
      return false;
    }
  }

  /**
   * Complete BridgeCard flow: Register user + Create card
   */
  async createBridgeCardForUser(
    userData: {
      fullName: string;
      dateOfBirth: string;
      email: string;
      phoneNumber?: string;
      address: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    },
    cardName: string,
    provider: CardProvider = CardProvider.MASTERCARD
  ): Promise<VirtualCard> {
    try {
      console.log('🚀 Starting complete BridgeCard flow for:', userData.fullName);

      // Step 1: Register cardholder (or get existing ID)
      const cardholderID = await this.registerCardholder(userData);

      // Step 2: Create virtual card
      const virtualCard = await this.createVirtualCard(cardholderID, cardName, provider);

      console.log('🎉 BridgeCard flow completed successfully!');
      return virtualCard;
    } catch (error) {
      console.error('❌ BridgeCard flow failed:', error);
      throw error;
    }
  }

  /**
   * Fund a BridgeCard virtual card (alternative method)
   */
  async fundCardAlternative(cardID: string, amount: number): Promise<boolean> {
    try {
      console.log('🧪 Funding card:', cardID, 'with amount:', amount);

      const response = await fetch(`${this.baseUrl}/fund-card`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          card_id: cardID,
          amount: (amount * 100).toString(), // Convert to cents
          currency: 'USD',
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Card funded successfully');
        return true;
      } else {
        console.error('❌ Card funding failed:', result.message);
        return false;
      }
    } catch (error) {
      console.error('❌ Card funding error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const bridgeCardService = new BridgeCardService();
