
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, DollarSign, ExternalLink } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { TransactionType } from "@/types/wallet";

interface Transaction {
  id: string;
  type: TransactionType;
  amount: number;
  cryptoType: string;
  date: Date;
  service?: string;
}

interface TransactionListProps {
  transactions: Transaction[];
  showViewAll?: boolean;
}

const TransactionList = ({ transactions, showViewAll = true }: TransactionListProps) => {
  const navigate = useNavigate();

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Transactions</CardTitle>
        {showViewAll && (
          <Button variant="ghost" size="sm" onClick={() => navigate('/transactions')}>
            View All
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.length === 0 ? (
            <p className="text-center text-muted-foreground py-4">
              No transactions yet
            </p>
          ) : (
            transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between py-2 border-b border-border last:border-0 hover:bg-muted/50 rounded-md px-2 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                    transaction.type === TransactionType.DEPOSIT
                      ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                      : 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
                  }`}>
                    {transaction.type === TransactionType.DEPOSIT ? (
                      <ArrowUpDown className="h-4 w-4" />
                    ) : (
                      <DollarSign className="h-4 w-4" />
                    )}
                  </div>
                  <div className="flex flex-col">
                    <div className="font-medium">
                      {transaction.type === TransactionType.DEPOSIT
                        ? "Deposit"
                        : transaction.type === TransactionType.PAYMENT
                          ? `Payment to ${transaction.service || 'Service'}`
                          : transaction.type === TransactionType.WITHDRAWAL
                            ? "Withdrawal"
                            : "Transfer"}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {transaction.date.toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className={`font-medium ${
                      transaction.type === TransactionType.DEPOSIT
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {transaction.type === TransactionType.DEPOSIT ? "+" : "-"}
                    {transaction.amount} {transaction.cryptoType}
                  </div>
                  <Button variant="ghost" size="icon" className="h-6 w-6">
                    <ExternalLink className="h-3 w-3" />
                    <span className="sr-only">View details</span>
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionList;
