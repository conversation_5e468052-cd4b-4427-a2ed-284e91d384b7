/**
 * Payment Intents API Routes
 * 
 * Handles payment intent creation, retrieval, and management
 */

import express from 'express';
import { createClient } from '@supabase/supabase-js';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/error-handler';
import { paymentRateLimiter } from '../middleware/rate-limiter';
import { validatePaymentIntent, validatePaymentIntentUpdate } from '../validators/payment-intent';
import { generatePaymentIntentId, generateClientSecret } from '../utils/id-generator';
import { calculateCryptoAmount, getCurrentExchangeRates } from '../utils/crypto-calculator';
import { generateQRCode } from '../utils/qr-generator';

const router = express.Router();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Create a new payment intent
router.post('/', paymentRateLimiter, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validationResult = validatePaymentIntent(req.body);
  if (!validationResult.success) {
    throw createError(400, 'validation_error', validationResult.error);
  }

  const {
    amount,
    currency = 'USD',
    accepted_cryptocurrencies = ['SOL', 'USDC'],
    settlement_currency = 'NGN',
    description,
    customer_email,
    customer_name,
    success_url,
    cancel_url,
    metadata = {}
  } = req.body;

  // Generate unique IDs
  const paymentIntentId = generatePaymentIntentId();
  const clientSecret = generateClientSecret(paymentIntentId);
  const paymentUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/pay/${paymentIntentId}`;

  // Get current exchange rates for crypto amounts
  const exchangeRates = await getCurrentExchangeRates(accepted_cryptocurrencies);

  // Generate QR code for the payment URL
  const qrCode = await generateQRCode(paymentUrl);

  try {
    // Create payment intent in database
    const { data: paymentIntent, error } = await supabase
      .from('payment_intents')
      .insert({
        id: paymentIntentId,
        payment_gateway_merchant_id: req.merchant!.id,
        amount: amount,
        currency: currency,
        status: 'requires_payment_method',
        accepted_cryptocurrencies: accepted_cryptocurrencies,
        settlement_currency: settlement_currency,
        description: description,
        customer_email: customer_email,
        customer_name: customer_name,
        success_url: success_url,
        cancel_url: cancel_url,
        payment_url: paymentUrl,
        qr_code: qrCode,
        metadata: {
          ...metadata,
          exchange_rates: exchangeRates,
          created_via: 'api'
        },
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      })
      .select()
      .single();

    if (error) {
      console.error('Database error creating payment intent:', error);
      throw createError(500, 'database_error', 'Failed to create payment intent');
    }

    // Return payment intent (exclude sensitive data)
    res.status(201).json({
      id: paymentIntent.id,
      object: 'payment_intent',
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      accepted_cryptocurrencies: paymentIntent.accepted_cryptocurrencies,
      settlement_currency: paymentIntent.settlement_currency,
      description: paymentIntent.description,
      customer_email: paymentIntent.customer_email,
      customer_name: paymentIntent.customer_name,
      payment_url: paymentIntent.payment_url,
      success_url: paymentIntent.success_url,
      cancel_url: paymentIntent.cancel_url,
      metadata: paymentIntent.metadata,
      created_at: paymentIntent.created_at,
      expires_at: paymentIntent.expires_at,
      client_secret: clientSecret
    });

  } catch (dbError) {
    console.error('Error creating payment intent:', dbError);
    throw createError(500, 'internal_error', 'Failed to create payment intent');
  }
}));

// Retrieve a payment intent
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const { data: paymentIntent, error } = await supabase
    .from('payment_intents')
    .select('*')
    .eq('id', id)
    .eq('payment_gateway_merchant_id', req.merchant!.id)
    .single();

  if (error || !paymentIntent) {
    throw createError(404, 'resource_not_found', 'Payment intent not found');
  }

  res.json({
    id: paymentIntent.id,
    object: 'payment_intent',
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    status: paymentIntent.status,
    accepted_cryptocurrencies: paymentIntent.accepted_cryptocurrencies,
    settlement_currency: paymentIntent.settlement_currency,
    description: paymentIntent.description,
    customer_email: paymentIntent.customer_email,
    customer_name: paymentIntent.customer_name,
    payment_url: paymentIntent.payment_url,
    success_url: paymentIntent.success_url,
    cancel_url: paymentIntent.cancel_url,
    metadata: paymentIntent.metadata,
    created_at: paymentIntent.created_at,
    updated_at: paymentIntent.updated_at,
    expires_at: paymentIntent.expires_at
  });
}));

// List payment intents
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const {
    limit = 10,
    starting_after,
    ending_before,
    status,
    created_gte,
    created_lte
  } = req.query;

  let query = supabase
    .from('payment_intents')
    .select('*')
    .eq('payment_gateway_merchant_id', req.merchant!.id)
    .order('created_at', { ascending: false })
    .limit(Math.min(Number(limit), 100)); // Max 100 items

  // Add filters
  if (status) {
    query = query.eq('status', status);
  }

  if (created_gte) {
    query = query.gte('created_at', created_gte);
  }

  if (created_lte) {
    query = query.lte('created_at', created_lte);
  }

  if (starting_after) {
    query = query.gt('id', starting_after);
  }

  if (ending_before) {
    query = query.lt('id', ending_before);
  }

  const { data: paymentIntents, error } = await query;

  if (error) {
    throw createError(500, 'database_error', 'Failed to retrieve payment intents');
  }

  const formattedData = paymentIntents.map(pi => ({
    id: pi.id,
    object: 'payment_intent',
    amount: pi.amount,
    currency: pi.currency,
    status: pi.status,
    description: pi.description,
    customer_email: pi.customer_email,
    created_at: pi.created_at,
    updated_at: pi.updated_at
  }));

  res.json({
    object: 'list',
    data: formattedData,
    has_more: formattedData.length === Number(limit)
  });
}));

// Update a payment intent
router.patch('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  
  const validationResult = validatePaymentIntentUpdate(req.body);
  if (!validationResult.success) {
    throw createError(400, 'validation_error', validationResult.error);
  }

  // Check if payment intent exists and belongs to merchant
  const { data: existingPI, error: fetchError } = await supabase
    .from('payment_intents')
    .select('*')
    .eq('id', id)
    .eq('payment_gateway_merchant_id', req.merchant!.id)
    .single();

  if (fetchError || !existingPI) {
    throw createError(404, 'resource_not_found', 'Payment intent not found');
  }

  // Check if payment intent can be updated
  if (['succeeded', 'canceled'].includes(existingPI.status)) {
    throw createError(400, 'invalid_state', 'Cannot update a payment intent that has already succeeded or been canceled');
  }

  const allowedUpdates = ['description', 'metadata', 'customer_email', 'customer_name'];
  const updates: any = {};
  
  for (const field of allowedUpdates) {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  }

  updates.updated_at = new Date().toISOString();

  const { data: updatedPI, error: updateError } = await supabase
    .from('payment_intents')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (updateError) {
    throw createError(500, 'database_error', 'Failed to update payment intent');
  }

  res.json({
    id: updatedPI.id,
    object: 'payment_intent',
    amount: updatedPI.amount,
    currency: updatedPI.currency,
    status: updatedPI.status,
    description: updatedPI.description,
    customer_email: updatedPI.customer_email,
    customer_name: updatedPI.customer_name,
    metadata: updatedPI.metadata,
    updated_at: updatedPI.updated_at
  });
}));

// Cancel a payment intent
router.post('/:id/cancel', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if payment intent exists and belongs to merchant
  const { data: existingPI, error: fetchError } = await supabase
    .from('payment_intents')
    .select('*')
    .eq('id', id)
    .eq('payment_gateway_merchant_id', req.merchant!.id)
    .single();

  if (fetchError || !existingPI) {
    throw createError(404, 'resource_not_found', 'Payment intent not found');
  }

  // Check if payment intent can be canceled
  if (['succeeded', 'canceled'].includes(existingPI.status)) {
    throw createError(400, 'invalid_state', 'Payment intent is already in a final state');
  }

  const { data: canceledPI, error: cancelError } = await supabase
    .from('payment_intents')
    .update({
      status: 'canceled',
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  if (cancelError) {
    throw createError(500, 'database_error', 'Failed to cancel payment intent');
  }

  res.json({
    id: canceledPI.id,
    object: 'payment_intent',
    status: canceledPI.status,
    updated_at: canceledPI.updated_at
  });
}));

export default router;
