{"name": "crypto-payment-gateway-api", "version": "1.0.0", "description": "Payment Gateway API for cryptocurrency payments", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/api/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "crypto": "^1.0.1", "qrcode": "^1.5.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/node": "^20.8.0", "@types/qrcode": "^1.5.5", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2", "jest": "^29.7.0", "@types/jest": "^29.5.5", "eslint": "^8.51.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4"}, "engines": {"node": ">=18.0.0"}, "keywords": ["cryptocurrency", "payment-gateway", "api", "blockchain", "solana", "ethereum", "bitcoin"], "author": "Your Company", "license": "MIT"}