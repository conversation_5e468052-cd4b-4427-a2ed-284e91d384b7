/**
 * Payment Gateway API Routes
 * 
 * Main API router for handling payment gateway requests
 */

import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';
import paymentIntentsRouter from './routes/payment-intents';
import paymentLinksRouter from './routes/payment-links';
import apiKeysRouter from './routes/api-keys';
import webhooksRouter from './routes/webhooks';
import { authenticateApiKey } from './middleware/auth';
import { errorHandler } from './middleware/error-handler';
import { rateLimiter } from './middleware/rate-limiter';

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.raw({ type: 'application/json' })); // For webhook signatures

// Rate limiting
app.use('/api/v1', rateLimiter);

// Health check
app.get('/api/v1/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API Routes (protected by API key authentication)
app.use('/api/v1/payment_intents', authenticateApiKey, paymentIntentsRouter);
app.use('/api/v1/payment_links', authenticateApiKey, paymentLinksRouter);
app.use('/api/v1/api_keys', authenticateApiKey, apiKeysRouter);

// Webhook routes (no API key required, uses webhook signatures)
app.use('/api/v1/webhooks', webhooksRouter);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      type: 'not_found',
      message: 'The requested endpoint does not exist'
    }
  });
});

export default app;
