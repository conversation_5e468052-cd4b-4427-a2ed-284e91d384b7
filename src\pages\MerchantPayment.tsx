/**
 * Merchant Payment Page - Customers pay merchants via QR code
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { reliableExchangeRateService } from '@/services/reliableExchangeRateService';
import { 
  Store, 
  CreditCard, 
  ArrowRight,
  CheckCircle,
  AlertCircle,
  MapPin,
  Phone,
  Mail
} from 'lucide-react';

interface MerchantAccount {
  id: string;
  business_name: string;
  business_type: string;
  business_description: string;
  business_address: string;
  business_phone: string;
  business_email: string;
  bank_name: string;
  account_number: string;
  account_name: string;
  qr_code_id: string;
  accepts_sol: boolean;
  accepts_usdc: boolean;
  min_payment_amount: number;
  max_payment_amount: number;
  is_active: boolean;
  is_verified: boolean;
  collection_preference: 'naira' | 'crypto';
  crypto_wallet_address?: string;
}

const MerchantPayment: React.FC = () => {
  const { merchantId } = useParams<{ merchantId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [merchant, setMerchant] = useState<MerchantAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [paymentDescription, setPaymentDescription] = useState('');
  const [usdcAmount, setUsdcAmount] = useState('');
  const [exchangeRate, setExchangeRate] = useState<number>(1540); // Default USDC rate
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    if (merchantId) {
      loadMerchant();
    }
  }, [merchantId]);

  useEffect(() => {
    fetchExchangeRate();
  }, []);

  useEffect(() => {
    calculateUsdcAmount();
  }, [paymentAmount, exchangeRate]);

  const fetchExchangeRate = async () => {
    try {
      console.log('💱 Fetching USDC exchange rate...');
      const rate = await reliableExchangeRateService.getTokenRate('USDC');
      setExchangeRate(rate);
      console.log('✅ USDC rate fetched:', rate);
    } catch (error) {
      console.error('❌ Error fetching exchange rate:', error);
      // This shouldn't happen as the service has built-in fallbacks
      setExchangeRate(1540);
    }
  };

  const calculateUsdcAmount = () => {
    if (paymentAmount && exchangeRate) {
      const ngnAmount = parseFloat(paymentAmount);
      const usdcEquivalent = ngnAmount / exchangeRate;
      setUsdcAmount(usdcEquivalent.toFixed(6)); // 6 decimal places for USDC
    } else {
      setUsdcAmount('');
    }
  };

  const loadMerchant = async () => {
    try {
      setLoading(true);

      console.log('Looking for merchant with ID:', merchantId);

      // First try exact match
      let { data, error } = await supabase
        .from('merchant_accounts')
        .select('*')
        .eq('qr_code_id', merchantId)
        .eq('is_active', true);

      console.log('Query result:', { data, error });

      // If no exact match, try partial match (in case of truncation)
      if (!data || data.length === 0) {
        console.log('Trying partial match...');
        const { data: partialData, error: partialError } = await supabase
          .from('merchant_accounts')
          .select('*')
          .like('qr_code_id', `${merchantId}%`)
          .eq('is_active', true);

        console.log('Partial match result:', { partialData, partialError });
        data = partialData;
        error = partialError;
      }

      if (error || !data || data.length === 0) {
        console.error('Merchant not found:', error);
        toast({
          title: "Merchant Not Found",
          description: "This merchant is not registered or inactive",
          variant: "destructive",
        });
        return;
      }

      setMerchant(data[0]); // Take first match
    } catch (error) {
      console.error('Error loading merchant:', error);
      toast({
        title: "Error",
        description: "Failed to load merchant information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!user) {
      toast({
        title: "Login Required",
        description: "Please login to make a payment",
        variant: "destructive",
      });
      navigate('/login');
      return;
    }

    if (!merchant || !paymentAmount || !paymentDescription.trim()) {
      toast({
        title: "Invalid Payment",
        description: "Please enter payment amount and description",
        variant: "destructive",
      });
      return;
    }

    const amount = parseFloat(paymentAmount);
    
    if (amount < merchant.min_payment_amount || amount > merchant.max_payment_amount) {
      toast({
        title: "Invalid Amount",
        description: `Amount must be between ₦${merchant.min_payment_amount.toLocaleString()} and ₦${merchant.max_payment_amount.toLocaleString()}`,
        variant: "destructive",
      });
      return;
    }

    setProcessing(true);

    try {
      // Check merchant's collection preference
      const isDirectCrypto = merchant.collection_preference === 'crypto';

      // Store merchant payment info in localStorage
      const merchantPaymentData = {
        merchantId: merchant.id,
        merchantName: merchant.business_name,
        merchantBankName: merchant.bank_name,
        merchantAccountNumber: merchant.account_number,
        merchantAccountName: merchant.account_name,
        paymentAmount: amount,
        paymentDescription: paymentDescription.trim(),
        usdcAmount: parseFloat(usdcAmount),
        exchangeRate: exchangeRate,
        paymentType: 'merchant_payment',
        collectionPreference: merchant.collection_preference,
        cryptoWalletAddress: merchant.crypto_wallet_address,
        isDirectCrypto: isDirectCrypto
      };

      localStorage.setItem('merchantPaymentData', JSON.stringify(merchantPaymentData));

      if (isDirectCrypto) {
        toast({
          title: "Redirecting to Crypto Payment! 🚀",
          description: `Sending ${usdcAmount} USDC directly to ${merchant.business_name}'s wallet`,
        });

        // Redirect to direct crypto payment flow
        navigate('/crypto-payment', {
          state: {
            merchantPayment: true,
            merchantData: merchantPaymentData,
            cryptoAmount: usdcAmount,
            cryptoSymbol: 'USDC',
            recipientAddress: merchant.crypto_wallet_address,
            paymentDescription: paymentDescription.trim()
          }
        });
      } else {
        toast({
          title: "Redirecting to Payment! 🚀",
          description: `Processing payment of ₦${amount.toLocaleString()} to ${merchant.business_name}`,
        });

        // Redirect to off-ramp page with merchant payment context
        navigate('/off-ramp', {
          state: {
            merchantPayment: true,
            merchantData: merchantPaymentData,
            prefilledAmount: amount.toString(),
            prefilledCrypto: 'USDC', // Always use USDC for merchant payments
            prefilledBankAccount: {
              bankName: merchant.bank_name,
              accountNumber: merchant.account_number,
              accountName: merchant.account_name
            }
          }
        });
      }

    } catch (error) {
      console.error('Payment error:', error);
      toast({
        title: "Payment Error",
        description: "An error occurred while processing your payment",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto p-4">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading merchant information...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!merchant) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto p-4">
          <Card>
            <CardContent className="p-8 text-center">
              <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Merchant Not Found</h2>
              <p className="text-gray-600 mb-4">
                This merchant is not registered or inactive. Please check the QR code and try again.
              </p>
              <Button onClick={() => navigate('/')}>
                Go Home
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto p-4 space-y-6">
        {/* Merchant Info */}
        <Card>
          <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-3">
              <Store className="h-6 w-6" />
              Pay {merchant.business_name}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-bold">{merchant.business_name}</h3>
                <p className="text-gray-600">{merchant.business_type}</p>
                {merchant.is_verified && (
                  <Badge className="bg-green-500 mt-2">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Verified Business
                  </Badge>
                )}
              </div>

              {merchant.business_description && (
                <p className="text-gray-700">{merchant.business_description}</p>
              )}

              <div className="space-y-2">
                {merchant.business_address && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{merchant.business_address}</span>
                  </div>
                )}
                
                {merchant.business_phone && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Phone className="h-4 w-4" />
                    <span>{merchant.business_phone}</span>
                  </div>
                )}
                
                {merchant.business_email && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{merchant.business_email}</span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Make Payment
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Payment Description */}
            <div>
              <Label htmlFor="description">What are you paying for?</Label>
              <Input
                id="description"
                type="text"
                value={paymentDescription}
                onChange={(e) => setPaymentDescription(e.target.value)}
                placeholder="e.g., Coffee and pastry, Haircut, Groceries..."
                maxLength={100}
              />
              <p className="text-xs text-gray-500 mt-1">
                Describe what you're purchasing (max 100 characters)
              </p>
            </div>

            {/* Amount Input */}
            <div>
              <Label htmlFor="amount">Payment Amount (NGN)</Label>
              <Input
                id="amount"
                type="number"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(e.target.value)}
                placeholder="Enter amount"
                min={merchant.min_payment_amount}
                max={merchant.max_payment_amount}
              />
              <div className="flex justify-between items-center mt-1">
                <p className="text-xs text-gray-500">
                  Min: ₦{merchant.min_payment_amount.toLocaleString()} - Max: ₦{merchant.max_payment_amount.toLocaleString()}
                </p>
                {usdcAmount && (
                  <p className="text-xs font-medium text-blue-600">
                    ≈ {usdcAmount} USDC
                  </p>
                )}
              </div>
            </div>

            {/* Payment Method Info */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">$</span>
                </div>
                <span className="font-medium text-blue-800">Paying with USDC</span>
              </div>
              <p className="text-sm text-blue-700">
                All merchant payments are processed using USDC for fast and secure transactions.
              </p>
              {exchangeRate && (
                <p className="text-xs text-blue-600 mt-1">
                  Current rate: 1 USDC = ₦{exchangeRate.toLocaleString()}
                </p>
              )}
            </div>

            {/* Payment Summary */}
            {paymentAmount && paymentDescription && (
              <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-medium mb-3 text-gray-900">Payment Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Item/Service:</span>
                    <span className="font-medium">{paymentDescription}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount (NGN):</span>
                    <span className="font-medium">₦{parseFloat(paymentAmount || '0').toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount (USDC):</span>
                    <span className="font-medium text-blue-600">{usdcAmount} USDC</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment Method:</span>
                    <span className="font-medium text-purple-600">
                      {merchant.collection_preference === 'crypto' ? 'Direct Crypto' : 'Convert to Naira'}
                    </span>
                  </div>
                  {merchant.collection_preference === 'crypto' && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Recipient Wallet:</span>
                      <span className="font-medium text-xs">
                        {merchant.crypto_wallet_address?.slice(0, 8)}...{merchant.crypto_wallet_address?.slice(-8)}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between border-t border-green-200 pt-2">
                    <span className="text-gray-600">Merchant:</span>
                    <span className="font-medium">{merchant.business_name}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Pay Button */}
            <Button
              onClick={handlePayment}
              disabled={!paymentAmount || !paymentDescription.trim() || processing}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              size="lg"
            >
              {processing ? (
                "Processing..."
              ) : (
                <>
                  Pay {usdcAmount ? `${usdcAmount} USDC` : `₦${parseFloat(paymentAmount || '0').toLocaleString()}`}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default MerchantPayment;
