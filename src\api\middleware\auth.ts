/**
 * API Authentication Middleware
 * 
 * Validates API keys and sets merchant context
 */

import { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

const supabase = createClient(
  process.env.VITE_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY! // Use service role key for API access
);

interface AuthenticatedRequest extends Request {
  merchant?: {
    id: string;
    business_name: string;
    verification_status: string;
    is_active: boolean;
  };
  apiKey?: {
    id: string;
    key_type: 'test' | 'live';
    permissions: string[];
  };
}

export const authenticateApiKey = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: {
          type: 'authentication_required',
          message: 'API key required. Include Authorization: Bearer <your_api_key> header.'
        }
      });
    }

    const apiKey = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Validate API key format
    if (!apiKey.startsWith('pk_test_') && !apiKey.startsWith('pk_live_') && 
        !apiKey.startsWith('sk_test_') && !apiKey.startsWith('sk_live_')) {
      return res.status(401).json({
        error: {
          type: 'invalid_api_key',
          message: 'Invalid API key format'
        }
      });
    }

    // Extract key type and prefix
    const keyType = apiKey.includes('_test_') ? 'test' : 'live';
    const keyPrefix = apiKey.startsWith('pk_') ? 'pk' : 'sk';

    // Hash the API key for database lookup
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    // Look up API key in database
    const { data: apiKeyData, error: keyError } = await supabase
      .from('merchant_api_keys')
      .select(`
        id,
        key_type,
        key_prefix,
        is_active,
        last_used_at,
        payment_gateway_merchant_id,
        payment_gateway_merchants!inner (
          id,
          business_name,
          verification_status,
          is_active
        )
      `)
      .eq('key_hash', keyHash)
      .eq('is_active', true)
      .single();

    if (keyError || !apiKeyData) {
      return res.status(401).json({
        error: {
          type: 'invalid_api_key',
          message: 'Invalid or inactive API key'
        }
      });
    }

    // Check if merchant is active and verified
    const merchant = apiKeyData.payment_gateway_merchants;
    if (!merchant.is_active || merchant.verification_status !== 'approved') {
      return res.status(403).json({
        error: {
          type: 'merchant_not_active',
          message: 'Merchant account is not active or not yet approved'
        }
      });
    }

    // Update last used timestamp
    await supabase
      .from('merchant_api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', apiKeyData.id);

    // Set merchant and API key context
    req.merchant = {
      id: merchant.id,
      business_name: merchant.business_name,
      verification_status: merchant.verification_status,
      is_active: merchant.is_active
    };

    req.apiKey = {
      id: apiKeyData.id,
      key_type: apiKeyData.key_type,
      permissions: ['read', 'write'] // Default permissions
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      error: {
        type: 'internal_error',
        message: 'Authentication failed'
      }
    });
  }
};

export const requireLiveKey = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (req.apiKey?.key_type !== 'live') {
    return res.status(403).json({
      error: {
        type: 'live_key_required',
        message: 'This operation requires a live API key'
      }
    });
  }
  next();
};

export const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.apiKey?.permissions.includes(permission)) {
      return res.status(403).json({
        error: {
          type: 'insufficient_permissions',
          message: `This operation requires ${permission} permission`
        }
      });
    }
    next();
  };
};

export type { AuthenticatedRequest };
