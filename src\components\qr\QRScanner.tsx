/**
 * QR Scanner Component
 * Allows customers to scan merchant QR codes and make payments
 */

import React, { useState, useRef, useEffect } from 'react';
import Layout from '@/components/Layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { MerchantQRService, MerchantAccount } from '@/services/merchantQRService';
import { exchangeRateService } from '@/services/exchangeRateService';
import {
  QrCode,
  Camera,
  X,
  Store,
  Banknote,
  ArrowRight,
  CheckCircle,
  AlertTriangle,
  Loader2
} from 'lucide-react';

interface QRScannerProps {
  onPaymentComplete?: (paymentId: string) => void;
  onClose?: () => void;
}

const QRScanner: React.FC<QRScannerProps> = ({
  onPaymentComplete,
  onClose
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const [scanning, setScanning] = useState(false);
  const [merchant, setMerchant] = useState<MerchantAccount | null>(null);
  const [step, setStep] = useState<'scan' | 'payment' | 'success'>('scan');
  const [processing, setProcessing] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  
  const [paymentData, setPaymentData] = useState({
    amountNgn: '',
    cryptoSymbol: 'USDC' as 'SOL' | 'USDC',
    customerNote: ''
  });
  
  const [exchangeRate, setExchangeRate] = useState(0);
  const [cryptoAmount, setCryptoAmount] = useState(0);

  useEffect(() => {
    if (step === 'scan' && scanning) {
      startCamera();
    }
    
    return () => {
      stopCamera();
    };
  }, [scanning, step]);

  useEffect(() => {
    if (paymentData.amountNgn && paymentData.cryptoSymbol) {
      calculateCryptoAmount();
    }
  }, [paymentData.amountNgn, paymentData.cryptoSymbol]);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: 'environment', // Use back camera
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });
      
      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.play();
      }
      
      // Start scanning for QR codes
      startQRDetection();
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast({
        title: "Camera Access Failed",
        description: "Please allow camera access to scan QR codes",
        variant: "destructive",
      });
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  };

  const startQRDetection = () => {
    const detectQR = () => {
      if (!videoRef.current || !canvasRef.current || step !== 'scan') {
        return;
      }

      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (video.readyState === video.HAVE_ENOUGH_DATA && context) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        // In production, use a QR code detection library like:
        // - jsQR
        // - qr-scanner
        // - ZXing-js
        
        // For now, simulate QR detection
        // This would be replaced with actual QR code detection
        setTimeout(() => {
          if (scanning) {
            // Simulate finding a QR code
            const mockQRData = JSON.stringify({
              merchantId: 'merchant_1735484078163_abc123def',
              businessName: 'Test Merchant',
              type: 'solpay_merchant_payment'
            });
            
            handleQRDetected(mockQRData);
          }
        }, 3000);
      }

      if (scanning && step === 'scan') {
        requestAnimationFrame(detectQR);
      }
    };

    detectQR();
  };

  const handleQRDetected = async (qrData: string) => {
    try {
      setScanning(false);
      stopCamera();
      
      // Parse QR data
      const parsedData = JSON.parse(qrData);
      
      if (parsedData.type !== 'solpay_merchant_payment') {
        toast({
          title: "Invalid QR Code",
          description: "This is not a SolPay merchant QR code",
          variant: "destructive",
        });
        return;
      }

      // Get merchant details
      const merchantData = await MerchantQRService.getMerchantByQRId(parsedData.merchantId);
      
      if (!merchantData) {
        toast({
          title: "Merchant Not Found",
          description: "This merchant is not registered or inactive",
          variant: "destructive",
        });
        return;
      }

      setMerchant(merchantData);
      setStep('payment');
      
      toast({
        title: "Merchant Found! 🏪",
        description: `Ready to pay ${merchantData.businessName}`,
      });
    } catch (error) {
      console.error('Error processing QR code:', error);
      toast({
        title: "QR Code Error",
        description: "Could not process the QR code. Please try again.",
        variant: "destructive",
      });
    }
  };

  const calculateCryptoAmount = async () => {
    try {
      const amountNgn = parseFloat(paymentData.amountNgn);
      if (isNaN(amountNgn) || amountNgn <= 0) {
        setCryptoAmount(0);
        setExchangeRate(0);
        return;
      }

      const rate = await exchangeRateService.getCryptoToNGNRate(paymentData.cryptoSymbol);
      setExchangeRate(rate);
      setCryptoAmount(amountNgn / rate);
    } catch (error) {
      console.error('Error calculating crypto amount:', error);
    }
  };

  const handlePayment = async () => {
    if (!user || !merchant) {
      toast({
        title: "Authentication Required",
        description: "Please log in to make payments",
        variant: "destructive",
      });
      return;
    }

    const amountNgn = parseFloat(paymentData.amountNgn);
    
    if (isNaN(amountNgn) || amountNgn <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid payment amount",
        variant: "destructive",
      });
      return;
    }

    if (amountNgn < merchant.minPaymentAmount || amountNgn > merchant.maxPaymentAmount) {
      toast({
        title: "Amount Out of Range",
        description: `Payment must be between ₦${merchant.minPaymentAmount.toLocaleString()} and ₦${merchant.maxPaymentAmount.toLocaleString()}`,
        variant: "destructive",
      });
      return;
    }

    setProcessing(true);

    try {
      const result = await MerchantQRService.processQRPayment({
        merchantId: merchant.qrCodeId,
        customerId: user.id,
        amountNgn,
        cryptoSymbol: paymentData.cryptoSymbol,
        paymentMethod: 'qr_scan',
        customerNote: paymentData.customerNote
      });

      if (result.success) {
        setStep('success');
        
        toast({
          title: "Payment Successful! ✅",
          description: `₦${amountNgn.toLocaleString()} sent to ${merchant.businessName}`,
        });

        onPaymentComplete?.(result.paymentId!);
      } else {
        toast({
          title: "Payment Failed",
          description: result.error || "Payment could not be processed",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast({
        title: "Payment Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const resetScanner = () => {
    setStep('scan');
    setMerchant(null);
    setPaymentData({
      amountNgn: '',
      cryptoSymbol: 'USDC',
      customerNote: ''
    });
    setScanning(true);
  };

  if (step === 'success') {
    return (
      <Layout>
        <div className="max-w-md mx-auto p-4">
          <Card className="p-6 text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-green-900 mb-2">Payment Successful!</h2>
            <p className="text-green-700 mb-4">
              Your payment of ₦{paymentData.amountNgn} has been sent to {merchant?.businessName}
            </p>

            <div className="space-y-3">
              <Button onClick={resetScanner} className="w-full">
                Scan Another QR Code
              </Button>

              <Button variant="outline" onClick={() => window.history.back()} className="w-full">
                Go Back
              </Button>
            </div>
          </Card>
        </div>
      </Layout>
    );
  }

  if (step === 'payment' && merchant) {
    return (
      <Layout>
        <div className="max-w-md mx-auto p-4">
          <Card className="p-6">
          {/* Merchant Info */}
          <div className="flex items-center gap-3 mb-6">
            <Store className="h-8 w-8 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold">{merchant.businessName}</h2>
              <p className="text-gray-600">{merchant.businessType || 'Business'}</p>
            </div>
          </div>

          {/* Payment Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="amount">Amount (₦)</Label>
              <Input
                id="amount"
                type="number"
                value={paymentData.amountNgn}
                onChange={(e) => setPaymentData(prev => ({ ...prev, amountNgn: e.target.value }))}
                placeholder="Enter amount in Naira"
                min={merchant.minPaymentAmount}
                max={merchant.maxPaymentAmount}
              />
              <p className="text-xs text-gray-500 mt-1">
                Min: ₦{merchant.minPaymentAmount.toLocaleString()} - Max: ₦{merchant.maxPaymentAmount.toLocaleString()}
              </p>
            </div>

            <div>
              <Label htmlFor="crypto">Pay With</Label>
              <Select 
                value={paymentData.cryptoSymbol} 
                onValueChange={(value: 'SOL' | 'USDC') => setPaymentData(prev => ({ ...prev, cryptoSymbol: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {merchant.acceptsUsdc && <SelectItem value="USDC">USDC</SelectItem>}
                  {merchant.acceptsSol && <SelectItem value="SOL">SOL</SelectItem>}
                </SelectContent>
              </Select>
            </div>

            {cryptoAmount > 0 && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span>You'll pay:</span>
                  <span className="font-bold">{cryptoAmount.toFixed(6)} {paymentData.cryptoSymbol}</span>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <span>Exchange rate:</span>
                  <span>1 {paymentData.cryptoSymbol} = ₦{exchangeRate.toLocaleString()}</span>
                </div>
              </div>
            )}

            <div>
              <Label htmlFor="note">Note (Optional)</Label>
              <Input
                id="note"
                value={paymentData.customerNote}
                onChange={(e) => setPaymentData(prev => ({ ...prev, customerNote: e.target.value }))}
                placeholder="Payment description"
              />
            </div>

            <div className="flex gap-3">
              <Button 
                variant="outline" 
                onClick={() => setStep('scan')}
                className="flex-1"
              >
                Back to Scan
              </Button>
              
              <Button 
                onClick={handlePayment}
                disabled={processing || !paymentData.amountNgn}
                className="flex-1"
              >
                {processing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Pay ₦{paymentData.amountNgn || '0'}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-md mx-auto p-4">
        <Card className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <QrCode className="h-8 w-8 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold">Scan QR Code</h2>
              <p className="text-gray-600">Point camera at merchant QR code</p>
            </div>
          </div>
          
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Camera View */}
        <div className="relative mb-6">
          <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
            {scanning ? (
              <>
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover"
                  autoPlay
                  playsInline
                  muted
                />
                <canvas
                  ref={canvasRef}
                  className="hidden"
                />
                
                {/* Scanning Overlay */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-48 h-48 border-2 border-white rounded-lg relative">
                    <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500 rounded-tl-lg"></div>
                    <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500 rounded-tr-lg"></div>
                    <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500 rounded-bl-lg"></div>
                    <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500 rounded-br-lg"></div>
                  </div>
                </div>
                
                <div className="absolute bottom-4 left-0 right-0 text-center">
                  <p className="text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full inline-block">
                    Scanning for QR code...
                  </p>
                </div>
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <Camera className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Camera not active</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="space-y-3">
          {!scanning ? (
            <Button onClick={() => setScanning(true)} className="w-full">
              <Camera className="h-4 w-4 mr-2" />
              Start Camera
            </Button>
          ) : (
            <Button variant="outline" onClick={() => setScanning(false)} className="w-full">
              Stop Scanning
            </Button>
          )}
          
          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-700">
                <p className="font-medium mb-1">How to pay:</p>
                <ul className="text-xs space-y-1">
                  <li>• Point camera at merchant's QR code</li>
                  <li>• Enter payment amount</li>
                  <li>• Choose SOL or USDC</li>
                  <li>• Confirm payment</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Card>
      </div>
    </Layout>
  );
};

export default QRScanner;
