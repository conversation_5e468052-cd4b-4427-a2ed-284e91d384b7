
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { CreditCard, Plus, RefreshCw } from "lucide-react";
import { useState } from "react";
import { useCard } from "@/contexts/CardContext";
import { CardProvider } from "@/types/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "@/hooks/use-toast";

interface ApplyForCardButtonProps {
  variant?: "default" | "outline" | "secondary" | "destructive" | "ghost" | "link" | null;
  size?: "default" | "sm" | "lg" | "icon" | null;
}

export default function ApplyForCardButton({ 
  variant = "default", 
  size = "default" 
}: ApplyForCardButtonProps) {
  const navigate = useNavigate();
  const { generateCard, cards } = useCard();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newCardName, setNewCardName] = useState('');
  const [newCardProvider, setNewCardProvider] = useState<CardProvider>(CardProvider.VISA);
  const [newCardType, setNewCardType] = useState<'personal' | 'group'>('personal');
  const [isCreatingCard, setIsCreatingCard] = useState(false);

  const handleCreateCard = async () => {
    if (!newCardName.trim()) {
      toast({
        title: "Card Name Required",
        description: "Please enter a name for your card.",
        variant: "destructive"
      });
      return;
    }

    // Check if user already has 2 cards
    if (cards.length >= 2) {
      toast({
        title: "Card Limit Reached",
        description: "You can have a maximum of 2 virtual cards. Please delete an existing card to create a new one.",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingCard(true);
    try {
      const isPersonal = newCardType === 'personal';
      console.log(`Generating new card with provider: ${newCardProvider} and name: ${newCardName} isPersonal: ${isPersonal}`);
      const newCard = await generateCard(newCardProvider, newCardName, isPersonal);
      if (newCard) {
        toast({
          title: "Card Created Successfully",
          description: `Your new ${newCardName} card is ready to use.`
        });
        setIsDialogOpen(false);
        
        // Reset form values
        setNewCardName('');
        setNewCardProvider(CardProvider.VISA);
        setNewCardType('personal');
        
        // Navigate to the virtual card page
        navigate("/virtual-card");
      }
    } catch (error) {
      console.error("Error creating card:", error);
      toast({
        title: "Card Creation Failed",
        description: "There was an error creating your card. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingCard(false);
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className="gap-2"
        >
          <CreditCard className="h-4 w-4" />
          Apply for Virtual Card
          {size !== "sm" && <Plus className="h-4 w-4" />}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Apply for a Virtual Card</DialogTitle>
          <DialogDescription>
            Fill in the details below to create your virtual card.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="card-name">Card Name</Label>
            <Input
              id="card-name"
              placeholder="e.g. My Personal Card"
              value={newCardName}
              onChange={(e) => setNewCardName(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="card-provider">Card Provider</Label>
            <Select
              value={newCardProvider}
              onValueChange={(value) => setNewCardProvider(value as CardProvider)}
            >
              <SelectTrigger id="card-provider">
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={CardProvider.VISA}>Visa</SelectItem>
                <SelectItem value={CardProvider.MASTERCARD}>Mastercard</SelectItem>
                <SelectItem value={CardProvider.AMEX}>American Express</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Card Type</Label>
            <RadioGroup
              value={newCardType}
              onValueChange={(value) => setNewCardType(value as 'personal' | 'group')}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-muted/50">
                <RadioGroupItem value="personal" id="apply-personal" />
                <Label htmlFor="apply-personal" className="flex items-center cursor-pointer">
                  Personal Card
                </Label>
              </div>
              <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-muted/50">
                <RadioGroupItem value="group" id="apply-group" />
                <Label htmlFor="apply-group" className="flex items-center cursor-pointer">
                  Group Card
                </Label>
              </div>
            </RadioGroup>
          </div>
        </div>

        <div className="flex gap-2 justify-end">
          <Button
            variant="outline"
            onClick={() => setIsDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreateCard}
            disabled={isCreatingCard || !newCardName.trim()}
          >
            {isCreatingCard ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Card"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
