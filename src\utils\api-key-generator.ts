/**
 * API Key Generator for Testing
 * 
 * Generates test API keys for payment gateway merchants
 */

import { supabase } from '@/integrations/supabase/client';
import crypto from 'crypto';

interface ApiKeyResult {
  success: boolean;
  apiKey?: string;
  error?: string;
}

/**
 * Generate API key for a payment gateway merchant
 */
export const generateApiKeyForMerchant = async (
  merchantId: string,
  keyType: 'test' | 'live' = 'test'
): Promise<ApiKeyResult> => {
  try {
    // Generate API key
    const randomBytes = crypto.randomBytes(24).toString('hex');
    const apiKey = `pk_${keyType}_${randomBytes}`;
    
    // Hash the API key for storage
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    // Store in database
    const { data, error } = await supabase
      .from('merchant_api_keys')
      .insert({
        payment_gateway_merchant_id: merchantId,
        key_id: apiKey,
        key_type: keyType,
        key_prefix: 'pk',
        key_hash: keyHash,
        name: `${keyType} API Key`,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating API key:', error);
      return {
        success: false,
        error: 'Failed to create API key'
      };
    }

    return {
      success: true,
      apiKey: apiKey
    };

  } catch (error) {
    console.error('Error generating API key:', error);
    return {
      success: false,
      error: 'Failed to generate API key'
    };
  }
};

/**
 * Get API keys for a merchant
 */
export const getMerchantApiKeys = async (merchantId: string) => {
  try {
    const { data, error } = await supabase
      .from('merchant_api_keys')
      .select('*')
      .eq('payment_gateway_merchant_id', merchantId)
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching API keys:', error);
      return { success: false, error: 'Failed to fetch API keys' };
    }

    return {
      success: true,
      apiKeys: data.map(key => ({
        id: key.id,
        key_id: key.key_id,
        key_type: key.key_type,
        name: key.name,
        created_at: key.created_at,
        last_used_at: key.last_used_at
      }))
    };

  } catch (error) {
    console.error('Error fetching API keys:', error);
    return { success: false, error: 'Failed to fetch API keys' };
  }
};

/**
 * Create test API key for demo purposes
 */
export const createTestApiKey = (): string => {
  const randomBytes = crypto.randomBytes(24).toString('hex');
  return `pk_test_${randomBytes}`;
};
