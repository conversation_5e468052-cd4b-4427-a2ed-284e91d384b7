/**
 * Admin Merchant Management Page
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Store, 
  CheckCircle, 
  Clock, 
  X, 
  Eye,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

interface MerchantAccount {
  id: string;
  user_id: string;
  business_name: string;
  business_type: string;
  business_description: string;
  business_address: string;
  business_phone: string;
  business_email: string;
  bank_name: string;
  account_number: string;
  account_name: string;
  bank_code: string;
  qr_code_id: string;
  is_verified: boolean;
  verification_status: string;
  created_at: string;
  profiles?: {
    full_name: string;
    email: string;
  };
}

const AdminMerchants: React.FC = () => {
  const { toast } = useToast();
  const [merchants, setMerchants] = useState<MerchantAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'verified'>('all');

  useEffect(() => {
    loadMerchants();
  }, []);

  const loadMerchants = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('merchant_accounts')
        .select(`
          *,
          profiles:user_id (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading merchants:', error);
        toast({
          title: "Error",
          description: "Failed to load merchant accounts",
          variant: "destructive",
        });
        return;
      }

      setMerchants(data || []);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const verifyMerchant = async (merchantId: string) => {
    try {
      const { error } = await supabase
        .from('merchant_accounts')
        .update({
          is_verified: true,
          verification_status: 'verified',
          bank_verified: true
        })
        .eq('id', merchantId);

      if (error) {
        console.error('Error verifying merchant:', error);
        toast({
          title: "Error",
          description: "Failed to verify merchant",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Merchant Verified! ✅",
        description: "Merchant account has been verified successfully",
      });

      loadMerchants();
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const rejectMerchant = async (merchantId: string) => {
    try {
      const { error } = await supabase
        .from('merchant_accounts')
        .update({
          is_verified: false,
          verification_status: 'rejected'
        })
        .eq('id', merchantId);

      if (error) {
        console.error('Error rejecting merchant:', error);
        toast({
          title: "Error",
          description: "Failed to reject merchant",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Merchant Rejected",
        description: "Merchant account has been rejected",
        variant: "destructive",
      });

      loadMerchants();
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const filteredMerchants = merchants.filter(merchant => {
    if (filter === 'pending') return !merchant.is_verified;
    if (filter === 'verified') return merchant.is_verified;
    return true;
  });

  const stats = {
    total: merchants.length,
    pending: merchants.filter(m => !m.is_verified).length,
    verified: merchants.filter(m => m.is_verified).length
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading merchant accounts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Merchant Management</h1>
          <p className="text-gray-600">Verify and manage merchant accounts</p>
        </div>
        <Button onClick={loadMerchants} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Store className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total Merchants</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Pending Verification</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Verified</p>
                <p className="text-2xl font-bold">{stats.verified}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-2">
        <Button 
          variant={filter === 'all' ? 'default' : 'outline'}
          onClick={() => setFilter('all')}
        >
          All ({stats.total})
        </Button>
        <Button 
          variant={filter === 'pending' ? 'default' : 'outline'}
          onClick={() => setFilter('pending')}
        >
          Pending ({stats.pending})
        </Button>
        <Button 
          variant={filter === 'verified' ? 'default' : 'outline'}
          onClick={() => setFilter('verified')}
        >
          Verified ({stats.verified})
        </Button>
      </div>

      {/* Merchants List */}
      <div className="space-y-4">
        {filteredMerchants.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Store className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No merchants found</h3>
              <p className="text-gray-600">
                {filter === 'pending' ? 'No pending merchant verifications' : 
                 filter === 'verified' ? 'No verified merchants yet' : 
                 'No merchant accounts registered yet'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredMerchants.map((merchant) => (
            <Card key={merchant.id} className="overflow-hidden">
              <CardHeader className="bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Store className="h-6 w-6 text-blue-600" />
                    <div>
                      <CardTitle className="text-lg">{merchant.business_name}</CardTitle>
                      <p className="text-sm text-gray-600">{merchant.business_type}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={merchant.is_verified ? "default" : "secondary"}>
                      {merchant.is_verified ? "Verified" : "Pending"}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {new Date(merchant.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Business Info */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">Business Information</h4>
                    
                    <div className="space-y-3">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span>{merchant.business_address || 'No address provided'}</span>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span>{merchant.business_phone || 'No phone provided'}</span>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <span>{merchant.business_email || 'No email provided'}</span>
                      </div>
                    </div>

                    {merchant.business_description && (
                      <div>
                        <p className="text-sm text-gray-600 italic">"{merchant.business_description}"</p>
                      </div>
                    )}
                  </div>

                  {/* Bank Info */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">Bank Information</h4>
                    
                    <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4 text-gray-400" />
                        <span className="font-medium">{merchant.bank_name}</span>
                      </div>
                      <p className="text-sm">Account: {merchant.account_number}</p>
                      <p className="text-sm">Name: {merchant.account_name}</p>
                      <p className="text-sm text-gray-600">QR ID: {merchant.qr_code_id}</p>
                    </div>

                    {/* User Info */}
                    {merchant.profiles && (
                      <div>
                        <h5 className="font-medium text-gray-700 mb-2">Account Owner</h5>
                        <p className="text-sm">{merchant.profiles.full_name}</p>
                        <p className="text-sm text-gray-600">{merchant.profiles.email}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                {!merchant.is_verified && (
                  <div className="flex gap-3 mt-6 pt-4 border-t">
                    <Button 
                      onClick={() => verifyMerchant(merchant.id)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Verify Merchant
                    </Button>
                    <Button 
                      onClick={() => rejectMerchant(merchant.id)}
                      variant="destructive"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default AdminMerchants;
