/**
 * Merchant Payment Notification Service
 * 
 * Handles all notifications for merchant payment process
 * Integrates with existing notification infrastructure
 */

import { notificationService, NotificationData } from './notificationService';
import { supabase } from '@/integrations/supabase/client';

export interface MerchantPaymentTransaction {
  id: string;
  merchant_id: string;
  customer_id: string;
  amount_ngn: number;
  crypto_symbol: 'USDC' | 'SOL';
  crypto_amount: number;
  exchange_rate: number;
  payment_method: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  transaction_reference?: string;
  created_at: string;
  completed_at?: string;
  failure_reason?: string;
  merchant_name?: string;
  customer_name?: string;
}

class MerchantPaymentNotificationService {
  /**
   * Send notification when customer initiates payment to merchant
   */
  async notifyMerchantPaymentInitiated(transaction: MerchantPaymentTransaction): Promise<void> {
    console.log('📧 MerchantPaymentNotificationService: Creating payment initiation notification');
    
    // Notification for customer
    const customerNotification: NotificationData = {
      user_id: transaction.customer_id,
      type: 'merchant_payment_initiated',
      title: '🏪 Merchant Payment Initiated',
      message: `Your payment of ₦${transaction.amount_ngn.toLocaleString()} to ${transaction.merchant_name || 'merchant'} has been initiated and is being processed.`,
      data: {
        transaction_id: transaction.id,
        merchant_id: transaction.merchant_id,
        merchant_name: transaction.merchant_name,
        amount_ngn: transaction.amount_ngn,
        crypto_amount: transaction.crypto_amount,
        crypto_symbol: transaction.crypto_symbol,
        exchange_rate: transaction.exchange_rate,
        payment_type: 'merchant_payment'
      },
      priority: 'medium',
      channels: ['in_app']
    };

    // Store in database for customer
    await supabase
      .from('notifications')
      .insert({
        user_id: transaction.customer_id,
        title: customerNotification.title,
        message: customerNotification.message,
        type: 'merchant_payment',
        metadata: customerNotification.data
      });

    console.log('✅ MerchantPaymentNotificationService: Customer notification sent');
  }

  /**
   * Send notification when merchant payment is processing
   */
  async notifyMerchantPaymentProcessing(transaction: MerchantPaymentTransaction): Promise<void> {
    // Notification for customer
    const customerNotification: NotificationData = {
      user_id: transaction.customer_id,
      type: 'merchant_payment_processing',
      title: '⚡ Processing Merchant Payment',
      message: `Your payment to ${transaction.merchant_name || 'merchant'} is now being processed. The merchant will receive ₦${transaction.amount_ngn.toLocaleString()} shortly.`,
      data: {
        transaction_id: transaction.id,
        merchant_id: transaction.merchant_id,
        merchant_name: transaction.merchant_name,
        amount_ngn: transaction.amount_ngn,
        crypto_symbol: transaction.crypto_symbol,
        payment_type: 'merchant_payment'
      },
      priority: 'medium',
      channels: ['in_app']
    };

    // Store in database for customer
    await supabase
      .from('notifications')
      .insert({
        user_id: transaction.customer_id,
        title: customerNotification.title,
        message: customerNotification.message,
        type: 'merchant_payment',
        metadata: customerNotification.data
      });

    console.log('✅ MerchantPaymentNotificationService: Processing notification sent');
  }

  /**
   * Send notification when merchant payment is completed
   */
  async notifyMerchantPaymentCompleted(transaction: MerchantPaymentTransaction): Promise<void> {
    // Get merchant info
    const { data: merchant } = await supabase
      .from('merchant_accounts')
      .select('user_id, business_name')
      .eq('id', transaction.merchant_id)
      .single();

    // Notification for customer
    const customerNotification: NotificationData = {
      user_id: transaction.customer_id,
      type: 'merchant_payment_completed',
      title: '✅ Payment Completed Successfully!',
      message: `Your payment of ₦${transaction.amount_ngn.toLocaleString()} to ${transaction.merchant_name || 'merchant'} has been completed successfully.`,
      data: {
        transaction_id: transaction.id,
        merchant_id: transaction.merchant_id,
        merchant_name: transaction.merchant_name,
        amount_ngn: transaction.amount_ngn,
        crypto_amount: transaction.crypto_amount,
        crypto_symbol: transaction.crypto_symbol,
        payment_type: 'merchant_payment',
        completed_at: transaction.completed_at
      },
      priority: 'high',
      channels: ['in_app']
    };

    // Notification for merchant
    if (merchant) {
      const merchantNotification: NotificationData = {
        user_id: merchant.user_id,
        type: 'merchant_payment_received',
        title: '💰 Payment Received!',
        message: `You received a payment of ₦${transaction.amount_ngn.toLocaleString()} from a customer. Funds will be transferred to your bank account.`,
        data: {
          transaction_id: transaction.id,
          customer_id: transaction.customer_id,
          amount_ngn: transaction.amount_ngn,
          crypto_amount: transaction.crypto_amount,
          crypto_symbol: transaction.crypto_symbol,
          payment_type: 'merchant_payment_received',
          completed_at: transaction.completed_at
        },
        priority: 'high',
        channels: ['in_app']
      };

      // Store notifications in database
      await Promise.all([
        supabase
          .from('notifications')
          .insert({
            user_id: transaction.customer_id,
            title: customerNotification.title,
            message: customerNotification.message,
            type: 'merchant_payment',
            metadata: customerNotification.data
          }),
        supabase
          .from('notifications')
          .insert({
            user_id: merchant.user_id,
            title: merchantNotification.title,
            message: merchantNotification.message,
            type: 'merchant_payment',
            metadata: merchantNotification.data
          })
      ]);
    }

    console.log('✅ MerchantPaymentNotificationService: Completion notifications sent');
  }

  /**
   * Send notification when merchant payment fails
   */
  async notifyMerchantPaymentFailed(transaction: MerchantPaymentTransaction): Promise<void> {
    const customerNotification: NotificationData = {
      user_id: transaction.customer_id,
      type: 'merchant_payment_failed',
      title: '❌ Merchant Payment Failed',
      message: `Your payment to ${transaction.merchant_name || 'merchant'} failed. ${transaction.failure_reason || 'Please try again or contact support.'}`,
      data: {
        transaction_id: transaction.id,
        merchant_id: transaction.merchant_id,
        merchant_name: transaction.merchant_name,
        amount_ngn: transaction.amount_ngn,
        crypto_symbol: transaction.crypto_symbol,
        failure_reason: transaction.failure_reason,
        payment_type: 'merchant_payment',
        support_contact: '<EMAIL>'
      },
      priority: 'high',
      channels: ['in_app']
    };

    // Store in database
    await supabase
      .from('notifications')
      .insert({
        user_id: transaction.customer_id,
        title: customerNotification.title,
        message: customerNotification.message,
        type: 'merchant_payment',
        metadata: customerNotification.data
      });

    console.log('✅ MerchantPaymentNotificationService: Failure notification sent');
  }

  /**
   * Get transaction duration for display
   */
  private getTransactionDuration(transaction: MerchantPaymentTransaction): string {
    if (!transaction.completed_at) return 'N/A';
    
    const start = new Date(transaction.created_at);
    const end = new Date(transaction.completed_at);
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.round(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'less than a minute';
    if (diffMins === 1) return '1 minute';
    if (diffMins < 60) return `${diffMins} minutes`;
    
    const diffHours = Math.round(diffMins / 60);
    return diffHours === 1 ? '1 hour' : `${diffHours} hours`;
  }

  /**
   * Send batch notification for multiple merchant payments (admin use)
   */
  async notifyBatchMerchantPayments(transactions: MerchantPaymentTransaction[]): Promise<void> {
    const promises = transactions.map(transaction => {
      switch (transaction.status) {
        case 'processing':
          return this.notifyMerchantPaymentProcessing(transaction);
        case 'completed':
          return this.notifyMerchantPaymentCompleted(transaction);
        case 'failed':
          return this.notifyMerchantPaymentFailed(transaction);
        default:
          return Promise.resolve();
      }
    });

    await Promise.allSettled(promises);
  }
}

export const merchantPaymentNotificationService = new MerchantPaymentNotificationService();
