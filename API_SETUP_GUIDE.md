# 🚀 SOLPAY Payment Gateway API Setup Guide

## 📋 Prerequisites

1. **Node.js 18+** installed
2. **Supabase database** already set up (from previous steps)
3. **Environment variables** configured

## 🔧 Step 1: Install API Dependencies

```bash
# Copy the API package.json
cp api-package.json package-api.json

# Install dependencies
npm install --prefix api express @supabase/supabase-js cors helmet compression qrcode
npm install --prefix api --save-dev @types/express @types/cors @types/compression @types/node @types/qrcode ts-node-dev typescript
```

## 🌍 Step 2: Environment Variables

Create a `.env` file in your project root with:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# API Configuration
API_PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Production API URL (update when deploying)
VITE_API_BASE_URL=http://localhost:3001
```

## 🚀 Step 3: Start the API Server

### Option 1: Development Mode
```bash
# Start the API server in development mode
npm run dev --prefix api
```

### Option 2: Build and Run
```bash
# Build the TypeScript
npm run build --prefix api

# Start the production server
npm run start --prefix api
```

## 🧪 Step 4: Test the API

### Health Check
```bash
curl http://localhost:3001/health
```

### Create a Test Payment Intent
```bash
curl -X POST http://localhost:3001/api/v1/payment_intents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer pk_test_your_api_key_here" \
  -d '{
    "amount": 2999,
    "currency": "USD",
    "accepted_cryptocurrencies": ["SOL", "USDC"],
    "description": "Test Payment"
  }'
```

## 🔑 Step 5: Generate API Keys

### Method 1: Via Registration Flow
1. Go to `/payment-gateway-register`
2. Complete the registration
3. API keys will be auto-generated

### Method 2: Manual Generation (for testing)
```typescript
import { generateApiKeyForMerchant } from '@/utils/api-key-generator';

// After merchant registration
const result = await generateApiKeyForMerchant(merchantId, 'test');
console.log('API Key:', result.apiKey);
```

## 📡 API Endpoints

### Payment Intents
- `POST /api/v1/payment_intents` - Create payment intent
- `GET /api/v1/payment_intents/:id` - Get payment intent
- `GET /api/v1/payment_intents` - List payment intents
- `PATCH /api/v1/payment_intents/:id` - Update payment intent
- `POST /api/v1/payment_intents/:id/cancel` - Cancel payment intent

### Authentication
All endpoints require API key authentication:
```
Authorization: Bearer pk_test_your_api_key_here
```

## 🔧 Step 6: Update Frontend Configuration

Update your frontend to use the real API:

### 1. Environment Variables
```env
# In your .env file
VITE_API_BASE_URL=http://localhost:3001
```

### 2. API Configuration
```typescript
// In your components
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
```

## 🧪 Step 7: Test the Complete Flow

### 1. Register a Payment Gateway Merchant
- Go to `/payment-gateway-register`
- Complete the 4-step registration
- Note the merchant ID from the database

### 2. Generate API Key
```sql
-- In Supabase SQL Editor
SELECT * FROM payment_gateway_merchants WHERE user_id = 'your_user_id';
```

### 3. Test Payment Widget
- Go to `/demo`
- Use a real API key instead of 'demo_merchant'
- Test payment creation

### 4. Verify in Database
```sql
-- Check created payment intents
SELECT * FROM payment_intents ORDER BY created_at DESC LIMIT 5;
```

## 🚨 Troubleshooting

### API Server Won't Start
1. Check environment variables are set
2. Verify Supabase connection
3. Check port 3001 is available

### Authentication Errors
1. Verify API key format: `pk_test_...` or `pk_live_...`
2. Check merchant is approved in database
3. Verify API key exists in `merchant_api_keys` table

### CORS Errors
1. Check `FRONTEND_URL` environment variable
2. Verify CORS configuration in `server.ts`

### Database Errors
1. Verify all tables exist (run schema again if needed)
2. Check RLS policies are set up correctly
3. Verify service role key has proper permissions

## 📊 Monitoring

### API Logs
```bash
# View API server logs
tail -f api.log
```

### Database Monitoring
```sql
-- Check API key usage
SELECT 
  pgm.business_name,
  mak.key_type,
  mak.last_used_at,
  COUNT(pi.id) as payment_intents_created
FROM payment_gateway_merchants pgm
JOIN merchant_api_keys mak ON pgm.id = mak.payment_gateway_merchant_id
LEFT JOIN payment_intents pi ON pgm.id = pi.payment_gateway_merchant_id
GROUP BY pgm.id, mak.id
ORDER BY mak.last_used_at DESC;
```

## 🎯 Next Steps

1. **Test thoroughly** with the demo page
2. **Create webhook endpoints** for payment notifications
3. **Add rate limiting** for production
4. **Set up monitoring** and logging
5. **Deploy to production** environment

## 🔐 Security Notes

- Never expose service role keys in frontend
- Use test keys for development
- Implement proper rate limiting
- Validate all input data
- Use HTTPS in production
- Rotate API keys regularly

## 📞 Support

If you encounter issues:
1. Check the API server logs
2. Verify database schema is complete
3. Test with curl commands first
4. Check environment variables

The SOLPAY API is now ready for production use! 🚀
