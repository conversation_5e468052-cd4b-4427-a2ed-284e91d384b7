/**
 * Merchant Payment Details Modal
 * Shows full information when merchant payment notification is clicked
 */

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { 
  Store, 
  User, 
  CreditCard, 
  Clock, 
  CheckCircle, 
  XCircle,
  ArrowRight,
  Copy,
  ExternalLink
} from 'lucide-react';

interface MerchantPaymentDetailsProps {
  isOpen: boolean;
  onClose: () => void;
  paymentId: string;
}

interface PaymentDetails {
  id: string;
  merchant_id: string;
  customer_id: string;
  amount_ngn: number;
  crypto_symbol: string;
  crypto_amount: number;
  exchange_rate: number;
  payment_method: string;
  status: string;
  transaction_reference?: string;
  created_at: string;
  completed_at?: string;
  failure_reason?: string;
  merchant?: {
    business_name: string;
    business_type: string;
    business_email: string;
    business_phone: string;
  };
  customer?: {
    full_name: string;
    email: string;
  };
}

const MerchantPaymentDetails: React.FC<MerchantPaymentDetailsProps> = ({
  isOpen,
  onClose,
  paymentId
}) => {
  const [payment, setPayment] = useState<PaymentDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isOpen && paymentId) {
      loadPaymentDetails();
    }
  }, [isOpen, paymentId]);

  const loadPaymentDetails = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('qr_payments')
        .select(`
          *,
          merchant:merchant_accounts!merchant_id (
            business_name,
            business_type,
            business_email,
            business_phone
          ),
          customer:profiles!customer_id (
            full_name,
            email
          )
        `)
        .eq('id', paymentId)
        .single();

      if (error) {
        console.error('Error loading payment details:', error);
        return;
      }

      setPayment(data);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'processing': return 'bg-blue-500';
      case 'pending': return 'bg-yellow-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'processing': return <Clock className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'failed': return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading payment details...</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!payment) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <div className="text-center py-8">
            <XCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Payment Not Found</h3>
            <p className="text-gray-600">Could not load payment details.</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            Merchant Payment Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Header */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(payment.status)}
                  <div>
                    <h3 className="text-xl font-bold">₦{payment.amount_ngn.toLocaleString()}</h3>
                    <p className="text-gray-600">
                      Payment {payment.status === 'completed' ? 'to' : 'for'} {payment.merchant?.business_name}
                    </p>
                  </div>
                </div>
                <Badge className={`${getStatusColor(payment.status)} text-white`}>
                  {payment.status.toUpperCase()}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Amount (NGN)</p>
                    <p className="text-lg font-semibold">₦{payment.amount_ngn.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Crypto Amount</p>
                    <p className="text-lg font-semibold">{payment.crypto_amount} {payment.crypto_symbol}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Exchange Rate</p>
                    <p className="text-sm">₦{payment.exchange_rate.toLocaleString()}/{payment.crypto_symbol}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Payment Method</p>
                    <p className="text-sm capitalize">{payment.payment_method.replace('_', ' ')}</p>
                  </div>
                </div>

                {payment.transaction_reference && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Transaction Reference</p>
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-mono">{payment.transaction_reference}</p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(payment.transaction_reference!)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-600">Payment ID</p>
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-mono">{payment.id}</p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(payment.id)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Merchant Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Store className="h-5 w-5" />
                  Merchant Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold text-lg">{payment.merchant?.business_name}</h4>
                  <p className="text-gray-600">{payment.merchant?.business_type}</p>
                </div>

                {payment.merchant?.business_email && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Email</p>
                    <p className="text-sm">{payment.merchant.business_email}</p>
                  </div>
                )}

                {payment.merchant?.business_phone && (
                  <div>
                    <p className="text-sm font-medium text-gray-600">Phone</p>
                    <p className="text-sm">{payment.merchant.business_phone}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-600">Merchant ID</p>
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-mono">{payment.merchant_id}</p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(payment.merchant_id)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Payment Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="font-medium">Payment Initiated</p>
                    <p className="text-sm text-gray-600">
                      {new Date(payment.created_at).toLocaleString()}
                    </p>
                  </div>
                </div>

                {payment.status === 'processing' && (
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Processing Payment</p>
                      <p className="text-sm text-gray-600">Payment is being processed</p>
                    </div>
                  </div>
                )}

                {payment.completed_at && (
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${payment.status === 'completed' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <div>
                      <p className="font-medium">
                        Payment {payment.status === 'completed' ? 'Completed' : 'Failed'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {new Date(payment.completed_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {payment.failure_reason && (
                  <div className="bg-red-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-red-800">Failure Reason:</p>
                    <p className="text-sm text-red-700">{payment.failure_reason}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MerchantPaymentDetails;
