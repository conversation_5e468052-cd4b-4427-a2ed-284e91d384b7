
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import lithicProxyPlugin from "./plugins/lithicProxyPlugin";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8081,
    proxy: {
      '/api/crypto-prices': {
        target: 'https://api.coingecko.com/api/v3/simple/price',
        changeOrigin: true,
        rewrite: (path) => {
          // Extract query params and rewrite the path
          const url = new URL(path, 'http://localhost');
          const ids = url.searchParams.get('ids');
          return `?ids=${ids}&vs_currencies=usd`;
        },
        configure: (proxy, options) => {
          proxy.on('proxyRes', (proxyRes, req, res) => {
            // Add CORS headers
            proxyRes.headers['Access-Control-Allow-Origin'] = '*';
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS';
            proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type';
          });
        }
      }
    }
  },
  plugins: [
    react(),
    lithicProxyPlugin(), // Add our Lithic proxy plugin
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  // Configure for SPA routing
  preview: {
    port: 8081,
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
        },
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Make environment variables available to the client
  define: {
    'process.env.LITHIC_API_KEY': JSON.stringify(process.env.LITHIC_API_KEY || '')
  }
}));
