/**
 * Get list of Nigerian banks from Paystack
 */

export default async function handler(req, res) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const paystackSecretKey = process.env.PAYSTACK_SECRET_KEY;
  
  if (!paystackSecretKey) {
    return res.status(500).json({ 
      success: false,
      error: 'Paystack not configured' 
    });
  }

  try {
    const response = await fetch('https://api.paystack.co/bank?country=nigeria', {
      method: 'GET',
      headers: {
        'Authorization': `<PERSON><PERSON> ${paystackSecretKey}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!data.status) {
      return res.status(400).json({
        success: false,
        error: data.message || 'Failed to fetch banks'
      });
    }

    // Sort banks alphabetically
    const banks = data.data.sort((a, b) => a.name.localeCompare(b.name));

    return res.status(200).json({
      success: true,
      data: banks
    });

  } catch (error) {
    console.error('Error fetching banks:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch banks'
    });
  }
}
