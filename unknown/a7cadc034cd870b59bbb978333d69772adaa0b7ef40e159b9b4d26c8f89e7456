/**
 * Test endpoint to verify Telegram webhook is working
 */

export default async function handler(req, res) {
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  
  if (!botToken) {
    return res.status(500).json({ 
      error: 'TELEGRAM_BOT_TOKEN not configured',
      hasToken: false
    });
  }

  try {
    // Get bot info to verify token works
    const response = await fetch(`https://api.telegram.org/bot${botToken}/getMe`);
    const botInfo = await response.json();
    
    if (botInfo.ok) {
      return res.status(200).json({
        success: true,
        botInfo: botInfo.result,
        webhookUrl: `${req.headers['x-forwarded-proto'] || 'https'}://${req.headers.host}/api/telegram/webhook`,
        timestamp: new Date().toISOString()
      });
    } else {
      return res.status(400).json({
        error: 'Invalid bot token',
        details: botInfo
      });
    }
  } catch (error) {
    return res.status(500).json({
      error: 'Failed to connect to Telegram API',
      details: error.message
    });
  }
}
