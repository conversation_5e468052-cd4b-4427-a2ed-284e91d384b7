// solpay Service Worker
// Update this version number manually when deploying major changes
const CACHE_VERSION = '7.0.0';
const CACHE_NAME = `solpay-v${CACHE_VERSION}-${Date.now()}`; // Dynamic cache name
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/sola.png',
  '/solana.png',
  '/icon-192.png',
  '/icon-512.png',
  '/favicon.ico',
  '/favicon-64x64.png',
  '/favicon-32x32.png',
  '/favicon-16x16.png',
  '/manifest.json'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
  console.log('solpay: Installing new service worker');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('solpay: Cache opened with name:', CACHE_NAME);
        return cache.addAll(urlsToCache.map(url => new Request(url, { cache: 'reload' })));
      })
      .catch((error) => {
        console.log('solpay: Cache failed:', error);
      })
  );
  // Force immediate activation
  self.skipWaiting();
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  // Skip API requests to avoid caching dynamic data
  if (event.request.url.includes('/api/')) return;

  // Skip external API requests (CORS proxies, etc.)
  if (event.request.url.includes('allorigins.win') ||
      event.request.url.includes('corsproxy.io') ||
      event.request.url.includes('coincap.io') ||
      event.request.url.includes('coingecko.com')) return;

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        if (response) {
          return response;
        }
        return fetch(event.request).catch(() => {
          // If both cache and network fail, return offline page for navigation requests
          if (event.request.destination === 'document') {
            return caches.match('/');
          }
        });
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('solpay: Activating new service worker');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      console.log('solpay: Found caches:', cacheNames);
      return Promise.all(
        cacheNames.map((cacheName) => {
          // Delete ALL old caches, including any solpay caches
          if (cacheName !== CACHE_NAME && cacheName.startsWith('solpay-')) {
            console.log('solpay: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('solpay: Cache cleanup complete');
      // Force immediate control of all clients
      return self.clients.claim();
    })
  );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    console.log('solpay: Background sync triggered');
    // Handle background sync for offline transactions
  }
});

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/s.png',
      badge: '/s.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey
      }
    };
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});
