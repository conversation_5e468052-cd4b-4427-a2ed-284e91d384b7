/**
 * Payment Gateway Service
 * 
 * Core service for handling business payment integrations
 * Similar to <PERSON><PERSON> but for cryptocurrency payments
 */

import { supabase } from '@/integrations/supabase/client';
import { exchangeRateService } from './exchangeRateService';
import crypto from 'crypto';

export interface PaymentIntentCreateRequest {
  amount: number;
  currency: string;
  accepted_cryptocurrencies?: string[];
  settlement_currency?: 'NGN' | 'crypto';
  metadata?: Record<string, any>;
  description?: string;
  success_url?: string;
  cancel_url?: string;
  customer_email?: string;
}

export interface PaymentIntent {
  id: string;
  client_secret: string;
  status: string;
  amount: number;
  currency: string;
  payment_url: string;
  qr_code?: string;
  accepted_cryptocurrencies: string[];
  settlement_currency: string;
  metadata: Record<string, any>;
  created_at: string;
}

export interface PaymentMethod {
  id: string;
  payment_intent_id: string;
  crypto_currency: string;
  crypto_amount: number;
  wallet_address: string;
  recipient_address: string;
  transaction_hash?: string;
  status: string;
}

class PaymentGatewayService {
  private static readonly BASE_URL = process.env.VITE_APP_URL || 'https://yourplatform.com';

  /**
   * Create a new payment intent
   */
  static async createPaymentIntent(
    merchantId: string,
    request: PaymentIntentCreateRequest
  ): Promise<{ success: boolean; payment_intent?: PaymentIntent; error?: string }> {
    try {
      // Generate unique payment intent ID
      const paymentIntentId = `pi_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
      const clientSecret = `${paymentIntentId}_secret_${crypto.randomBytes(16).toString('hex')}`;
      
      // Generate payment URL
      const paymentUrl = `${this.BASE_URL}/pay/${paymentIntentId}`;

      // Default accepted cryptocurrencies
      const acceptedCryptos = request.accepted_cryptocurrencies || ['SOL', 'USDC'];
      
      // Insert payment intent
      const { data, error } = await supabase
        .from('payment_intents')
        .insert({
          id: paymentIntentId,
          merchant_id: merchantId,
          client_secret: clientSecret,
          amount: request.amount,
          currency: request.currency,
          settlement_currency: request.settlement_currency || 'NGN',
          accepted_cryptocurrencies: acceptedCryptos,
          success_url: request.success_url,
          cancel_url: request.cancel_url,
          payment_url: paymentUrl,
          metadata: request.metadata || {},
          description: request.description,
          status: 'requires_payment_method'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating payment intent:', error);
        return { success: false, error: 'Failed to create payment intent' };
      }

      // Generate QR code for the payment URL
      const qrCode = await this.generateQRCode(paymentUrl);

      const paymentIntent: PaymentIntent = {
        id: data.id,
        client_secret: data.client_secret,
        status: data.status,
        amount: data.amount,
        currency: data.currency,
        payment_url: data.payment_url,
        qr_code: qrCode,
        accepted_cryptocurrencies: data.accepted_cryptocurrencies,
        settlement_currency: data.settlement_currency,
        metadata: data.metadata,
        created_at: data.created_at
      };

      return { success: true, payment_intent: paymentIntent };

    } catch (error) {
      console.error('Payment intent creation error:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Retrieve a payment intent
   */
  static async retrievePaymentIntent(
    paymentIntentId: string
  ): Promise<{ success: boolean; payment_intent?: PaymentIntent; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('payment_intents')
        .select('*')
        .eq('id', paymentIntentId)
        .single();

      if (error || !data) {
        return { success: false, error: 'Payment intent not found' };
      }

      const paymentIntent: PaymentIntent = {
        id: data.id,
        client_secret: data.client_secret,
        status: data.status,
        amount: data.amount,
        currency: data.currency,
        payment_url: data.payment_url,
        accepted_cryptocurrencies: data.accepted_cryptocurrencies,
        settlement_currency: data.settlement_currency,
        metadata: data.metadata,
        created_at: data.created_at
      };

      return { success: true, payment_intent: paymentIntent };

    } catch (error) {
      console.error('Error retrieving payment intent:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Confirm payment intent with crypto payment
   */
  static async confirmPaymentIntent(
    paymentIntentId: string,
    cryptoCurrency: string,
    walletAddress: string,
    transactionHash: string
  ): Promise<{ success: boolean; payment_method?: PaymentMethod; error?: string }> {
    try {
      // Get payment intent
      const { data: paymentIntent, error: piError } = await supabase
        .from('payment_intents')
        .select('*')
        .eq('id', paymentIntentId)
        .single();

      if (piError || !paymentIntent) {
        return { success: false, error: 'Payment intent not found' };
      }

      // Check if crypto currency is accepted
      if (!paymentIntent.accepted_cryptocurrencies.includes(cryptoCurrency)) {
        return { success: false, error: 'Cryptocurrency not accepted' };
      }

      // Get current exchange rate
      const exchangeRate = await exchangeRateService.getExchangeRate(cryptoCurrency, paymentIntent.currency);
      const cryptoAmount = paymentIntent.amount / exchangeRate;

      // Get merchant's receiving wallet for this crypto
      const recipientAddress = await this.getMerchantWalletAddress(paymentIntent.merchant_id, cryptoCurrency);

      // Create payment method
      const paymentMethodId = `pm_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
      
      const { data: paymentMethod, error: pmError } = await supabase
        .from('payment_methods')
        .insert({
          id: paymentMethodId,
          payment_intent_id: paymentIntentId,
          crypto_currency: cryptoCurrency,
          crypto_amount: cryptoAmount,
          wallet_address: walletAddress,
          recipient_address: recipientAddress,
          transaction_hash: transactionHash,
          status: 'pending'
        })
        .select()
        .single();

      if (pmError) {
        console.error('Error creating payment method:', pmError);
        return { success: false, error: 'Failed to create payment method' };
      }

      // Update payment intent status
      await supabase
        .from('payment_intents')
        .update({
          status: 'processing',
          payment_method_id: paymentMethodId,
          crypto_amount: cryptoAmount,
          crypto_currency: cryptoCurrency,
          exchange_rate: exchangeRate,
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentIntentId);

      // Start blockchain verification process
      this.verifyBlockchainTransaction(paymentMethodId, transactionHash, cryptoCurrency);

      return {
        success: true,
        payment_method: {
          id: paymentMethod.id,
          payment_intent_id: paymentMethod.payment_intent_id,
          crypto_currency: paymentMethod.crypto_currency,
          crypto_amount: paymentMethod.crypto_amount,
          wallet_address: paymentMethod.wallet_address,
          recipient_address: paymentMethod.recipient_address,
          transaction_hash: paymentMethod.transaction_hash,
          status: paymentMethod.status
        }
      };

    } catch (error) {
      console.error('Error confirming payment intent:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Generate QR code for payment URL
   */
  private static async generateQRCode(url: string): Promise<string> {
    try {
      const QRCode = await import('qrcode');
      return await QRCode.toDataURL(url);
    } catch (error) {
      console.error('Error generating QR code:', error);
      return '';
    }
  }

  /**
   * Get merchant's wallet address for specific cryptocurrency
   */
  private static async getMerchantWalletAddress(merchantId: string, cryptoCurrency: string): Promise<string> {
    try {
      // Get merchant's crypto wallet address from their settings
      const { data: merchant, error } = await supabase
        .from('merchant_accounts')
        .select('crypto_wallet_address')
        .eq('id', merchantId)
        .single();

      if (error || !merchant?.crypto_wallet_address) {
        // Return platform's default receiving wallet for this crypto
        return this.getPlatformWalletAddress(cryptoCurrency);
      }

      return merchant.crypto_wallet_address;
    } catch (error) {
      console.error('Error getting merchant wallet address:', error);
      return this.getPlatformWalletAddress(cryptoCurrency);
    }
  }

  /**
   * Get platform's default wallet address for cryptocurrency
   */
  private static getPlatformWalletAddress(cryptoCurrency: string): string {
    const platformWallets = {
      'SOL': process.env.VITE_PLATFORM_SOL_WALLET || 'PLATFORM_SOL_WALLET_ADDRESS',
      'USDC': process.env.VITE_PLATFORM_USDC_WALLET || 'PLATFORM_USDC_WALLET_ADDRESS',
      'ETH': process.env.VITE_PLATFORM_ETH_WALLET || 'PLATFORM_ETH_WALLET_ADDRESS'
    };

    return platformWallets[cryptoCurrency as keyof typeof platformWallets] || 'PLATFORM_DEFAULT_WALLET';
  }

  /**
   * Verify blockchain transaction (async process)
   */
  private static async verifyBlockchainTransaction(
    paymentMethodId: string,
    transactionHash: string,
    cryptoCurrency: string
  ): Promise<void> {
    // This would integrate with blockchain verification services
    // For now, simulate verification after 30 seconds
    setTimeout(async () => {
      try {
        // Update payment method status
        await supabase
          .from('payment_methods')
          .update({
            status: 'confirmed',
            confirmations: 1,
            confirmed_at: new Date().toISOString()
          })
          .eq('id', paymentMethodId);

        // Update payment intent status
        const { data: paymentMethod } = await supabase
          .from('payment_methods')
          .select('payment_intent_id')
          .eq('id', paymentMethodId)
          .single();

        if (paymentMethod) {
          await supabase
            .from('payment_intents')
            .update({
              status: 'succeeded',
              succeeded_at: new Date().toISOString()
            })
            .eq('id', paymentMethod.payment_intent_id);

          // Trigger webhook
          this.triggerWebhook(paymentMethod.payment_intent_id, 'payment_intent.succeeded');
        }

      } catch (error) {
        console.error('Error verifying transaction:', error);
      }
    }, 30000); // 30 seconds simulation
  }

  /**
   * Trigger webhook for merchant
   */
  private static async triggerWebhook(paymentIntentId: string, eventType: string): Promise<void> {
    try {
      // Get payment intent with merchant info
      const { data: paymentIntent, error } = await supabase
        .from('payment_intents')
        .select('*, merchant:merchant_accounts(*)')
        .eq('id', paymentIntentId)
        .single();

      if (error || !paymentIntent) {
        console.error('Error fetching payment intent for webhook:', error);
        return;
      }

      // Import webhook service dynamically to avoid circular dependency
      const { WebhookService } = await import('./webhookService');

      // Send webhook event
      await WebhookService.sendWebhookEvent(
        paymentIntent.merchant_id,
        eventType,
        paymentIntent,
        paymentIntentId
      );

      console.log(`Webhook triggered for ${paymentIntentId}: ${eventType}`);
    } catch (error) {
      console.error('Error triggering webhook:', error);
    }
  }
}

export { PaymentGatewayService };
