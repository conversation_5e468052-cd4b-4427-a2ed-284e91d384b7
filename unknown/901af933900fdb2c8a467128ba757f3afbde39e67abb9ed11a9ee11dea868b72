import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { VirtualCard } from "@/types/card";
import { CheckCircle2, CreditCard, ArrowRight, Wallet, ShieldCheck } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface CardCreationSuccessProps {
  card: VirtualCard;
}

export default function CardCreationSuccess({ card }: CardCreationSuccessProps) {
  const navigate = useNavigate();

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto bg-green-100 dark:bg-green-900 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4">
          <CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <CardTitle className="text-2xl">Virtual Card Created Successfully!</CardTitle>
        <CardDescription>
          Your virtual card is now ready to use for subscription services
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-muted p-4 rounded-lg space-y-4">
          <h3 className="text-lg font-medium flex items-center">
            <CreditCard className="h-5 w-5 mr-2 text-primary" />
            Card Details
          </h3>
          
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="font-medium">Card Number:</div>
            <div className="font-mono">{card.maskedNumber}</div>
            <div className="font-medium">Cardholder Name:</div>
            <div>{card.name}</div>
            <div className="font-medium">Expiry Date:</div>
            <div>{card.expiry}</div>
            <div className="font-medium">Card Provider:</div>
            <div>{card.provider}</div>
            <div className="font-medium">Status:</div>
            <div className="flex items-center">
              <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
              Active
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-muted p-4 rounded-lg">
            <h3 className="text-md font-medium flex items-center mb-2">
              <Wallet className="h-5 w-5 mr-2 text-primary" />
              Fund Your Card
            </h3>
            <p className="text-sm mb-2">
              Your card has been created with a $0 balance. Add funds to start using it for subscriptions.
            </p>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => navigate("/virtual-card")}
            >
              Add Funds
            </Button>
          </div>
          
          <div className="bg-muted p-4 rounded-lg">
            <h3 className="text-md font-medium flex items-center mb-2">
              <ShieldCheck className="h-5 w-5 mr-2 text-primary" />
              Security Tips
            </h3>
            <ul className="text-sm list-disc list-inside space-y-1">
              <li>Never share your full card details</li>
              <li>Monitor transactions regularly</li>
              <li>Freeze your card when not in use</li>
            </ul>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row gap-2 justify-center">
        <Button 
          variant="outline" 
          onClick={() => navigate("/marketplace")}
          className="w-full sm:w-auto"
        >
          Browse Subscription Services
        </Button>
        <Button 
          onClick={() => navigate("/virtual-card")}
          className="w-full sm:w-auto gap-2"
        >
          Go to Card Dashboard
          <ArrowRight className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}
