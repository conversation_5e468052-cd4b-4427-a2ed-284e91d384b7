/**
 * Voice Command Service
 * Handles voice-to-text processing and command execution
 */

import { supabase } from '@/integrations/supabase/client';
import { CrossChainWalletService } from './crossChainWalletService';
import { exchangeRateService } from './exchangeRateService';
import { offRampProcessingService } from './offRampProcessingService';
import { MerchantQRService } from './merchantQRService';

export interface VoiceCommand {
  id: string;
  userId: string;
  commandText: string;
  originalAudioUrl?: string;
  languageDetected?: string;
  confidenceScore?: number;
  intent?: string;
  entities?: Record<string, any>;
  actionTaken?: string;
  success: boolean;
  responseText?: string;
  responseAudioUrl?: string;
  createdAt: string;
}

export interface VoiceProcessingResult {
  success: boolean;
  intent?: string;
  entities?: Record<string, any>;
  responseText: string;
  responseAudio?: string;
  actionTaken?: string;
}

export interface ExtractedEntities {
  amount?: number;
  token?: 'SOL' | 'USDC';
  bank?: string;
  merchantId?: string;
  action?: 'balance' | 'deposit' | 'withdraw' | 'pay' | 'rates';
}

class VoiceCommandService {
  /**
   * Process voice command from audio or text
   */
  static async processVoiceCommand(
    userId: string,
    audioUrl?: string,
    transcribedText?: string,
    languageCode: string = 'en'
  ): Promise<VoiceProcessingResult> {
    try {
      let commandText = transcribedText;
      let confidenceScore = 1.0;

      // If audio URL provided, transcribe it
      if (audioUrl && !transcribedText) {
        const transcription = await this.transcribeAudio(audioUrl, languageCode);
        commandText = transcription.text;
        confidenceScore = transcription.confidence;
      }

      if (!commandText) {
        return {
          success: false,
          responseText: 'Sorry, I could not understand your command. Please try again.'
        };
      }

      console.log('🎤 Processing voice command:', commandText);

      // Extract intent and entities
      const { intent, entities } = this.extractIntentAndEntities(commandText, languageCode);
      
      // Execute the command
      const result = await this.executeCommand(userId, intent, entities);
      
      // Log the command
      await this.logVoiceCommand({
        userId,
        commandText,
        originalAudioUrl: audioUrl,
        languageDetected: languageCode,
        confidenceScore,
        intent,
        entities,
        actionTaken: result.actionTaken,
        success: result.success,
        responseText: result.responseText
      });

      return result;
    } catch (error) {
      console.error('Error processing voice command:', error);
      return {
        success: false,
        responseText: 'Sorry, something went wrong. Please try again.'
      };
    }
  }

  /**
   * Transcribe audio to text (mock implementation)
   */
  private static async transcribeAudio(
    audioUrl: string,
    languageCode: string
  ): Promise<{ text: string; confidence: number }> {
    try {
      // In production, integrate with:
      // - Google Speech-to-Text API
      // - Azure Speech Services
      // - AWS Transcribe
      // - OpenAI Whisper API
      
      console.log('🎵 Transcribing audio:', audioUrl);
      
      // Mock transcription for now
      return {
        text: 'check my balance',
        confidence: 0.95
      };
    } catch (error) {
      console.error('Error transcribing audio:', error);
      return {
        text: '',
        confidence: 0
      };
    }
  }

  /**
   * Extract intent and entities from text
   */
  private static extractIntentAndEntities(
    text: string,
    languageCode: string
  ): { intent: string; entities: ExtractedEntities } {
    const normalizedText = text.toLowerCase().trim();
    
    // Intent patterns for different languages
    const patterns = {
      en: {
        balance: /\b(check|show|get|what|my)\s*(balance|money|crypto|wallet)/i,
        deposit: /\b(deposit|send|transfer|address|wallet)\s*(address|money|crypto)/i,
        withdraw: /\b(withdraw|send|transfer|convert|off.?ramp)\s*(\d+)?\s*(sol|usdc|naira|ngn)/i,
        pay: /\b(pay|payment|send)\s*(merchant|shop|store|business)/i,
        rates: /\b(rate|price|exchange|how\s*much|current)/i
      },
      yo: { // Yoruba
        balance: /\b(wo|check|show)\s*(owo|money|balance)/i,
        deposit: /\b(fi|send|deposit)\s*(owo|money)/i,
        withdraw: /\b(gba|withdraw|convert)\s*(\d+)?\s*(sol|usdc|naira)/i,
        pay: /\b(san|pay|payment)/i,
        rates: /\b(iye|rate|price|exchange)/i
      },
      ha: { // Hausa
        balance: /\b(duba|check|show)\s*(kudi|money|balance)/i,
        deposit: /\b(tura|send|deposit)\s*(kudi|money)/i,
        withdraw: /\b(karba|withdraw|convert)\s*(\d+)?\s*(sol|usdc|naira)/i,
        pay: /\b(biya|pay|payment)/i,
        rates: /\b(farashi|rate|price|exchange)/i
      },
      ig: { // Igbo
        balance: /\b(lee|check|show)\s*(ego|money|balance)/i,
        deposit: /\b(zipu|send|deposit)\s*(ego|money)/i,
        withdraw: /\b(weputa|withdraw|convert)\s*(\d+)?\s*(sol|usdc|naira)/i,
        pay: /\b(kwuo|pay|payment)/i,
        rates: /\b(onu|rate|price|exchange)/i
      }
    };

    const langPatterns = patterns[languageCode as keyof typeof patterns] || patterns.en;
    
    // Determine intent
    let intent = 'unknown';
    for (const [intentName, pattern] of Object.entries(langPatterns)) {
      if (pattern.test(normalizedText)) {
        intent = intentName;
        break;
      }
    }

    // Extract entities
    const entities: ExtractedEntities = { action: intent as any };

    // Extract amount
    const amountMatch = normalizedText.match(/(\d+(?:\.\d+)?)/);
    if (amountMatch) {
      entities.amount = parseFloat(amountMatch[1]);
    }

    // Extract token
    if (/\bsol\b/i.test(normalizedText)) {
      entities.token = 'SOL';
    } else if (/\busdc\b/i.test(normalizedText)) {
      entities.token = 'USDC';
    }

    // Extract bank
    const bankPatterns = [
      /\b(gtbank|gt\s*bank|guaranty\s*trust)\b/i,
      /\b(access\s*bank|access)\b/i,
      /\b(zenith\s*bank|zenith)\b/i,
      /\b(uba|united\s*bank)\b/i,
      /\b(first\s*bank|firstbank)\b/i,
      /\b(fidelity\s*bank|fidelity)\b/i
    ];

    for (const pattern of bankPatterns) {
      const match = normalizedText.match(pattern);
      if (match) {
        entities.bank = this.normalizeBankName(match[0]);
        break;
      }
    }

    // Extract merchant ID (for payment commands)
    const merchantMatch = normalizedText.match(/merchant[_\s]*(\w+)/i);
    if (merchantMatch) {
      entities.merchantId = merchantMatch[1];
    }

    return { intent, entities };
  }

  /**
   * Execute the extracted command
   */
  private static async executeCommand(
    userId: string,
    intent: string,
    entities: ExtractedEntities
  ): Promise<VoiceProcessingResult> {
    try {
      switch (intent) {
        case 'balance':
          return await this.handleBalanceCommand(userId);
        
        case 'deposit':
          return await this.handleDepositCommand(userId);
        
        case 'withdraw':
          return await this.handleWithdrawCommand(userId, entities);
        
        case 'pay':
          return await this.handlePayCommand(userId, entities);
        
        case 'rates':
          return await this.handleRatesCommand();
        
        default:
          return {
            success: false,
            intent,
            entities,
            responseText: 'I understand you want to ' + intent + ', but I need more information. Please try again with more details.',
            actionTaken: 'unknown_command'
          };
      }
    } catch (error) {
      console.error('Error executing command:', error);
      return {
        success: false,
        responseText: 'Sorry, I encountered an error while processing your request.',
        actionTaken: 'error'
      };
    }
  }

  /**
   * Handle balance command
   */
  private static async handleBalanceCommand(userId: string): Promise<VoiceProcessingResult> {
    try {
      const wallets = await CrossChainWalletService.getAllWallets(userId);
      
      if (wallets.length === 0) {
        return {
          success: false,
          intent: 'balance',
          responseText: 'You have no wallets set up. Please create wallets in the app first.',
          actionTaken: 'no_wallets'
        };
      }

      let totalValueUSD = 0;
      let hasBalances = false;

      for (const wallet of wallets) {
        if (wallet.balances) {
          for (const balance of wallet.balances) {
            if (parseFloat(balance.balance) > 0) {
              hasBalances = true;
              totalValueUSD += balance.balanceUSD || 0;
            }
          }
        }
      }

      if (!hasBalances) {
        return {
          success: true,
          intent: 'balance',
          responseText: 'Your wallet balances are currently zero. You can deposit crypto using the addresses in the app.',
          actionTaken: 'zero_balance'
        };
      }

      const usdToNgn = await exchangeRateService.getUSDToNGNRate();
      const totalNGN = totalValueUSD * usdToNgn;

      return {
        success: true,
        intent: 'balance',
        responseText: `Your total portfolio value is ${totalValueUSD.toFixed(2)} dollars, which is approximately ${totalNGN.toLocaleString()} naira.`,
        actionTaken: 'balance_retrieved'
      };
    } catch (error) {
      console.error('Error in handleBalanceCommand:', error);
      return {
        success: false,
        intent: 'balance',
        responseText: 'Sorry, I could not retrieve your balance at the moment.',
        actionTaken: 'balance_error'
      };
    }
  }

  /**
   * Handle deposit command
   */
  private static async handleDepositCommand(userId: string): Promise<VoiceProcessingResult> {
    try {
      const wallets = await CrossChainWalletService.getAllWallets(userId);
      
      if (wallets.length === 0) {
        return {
          success: false,
          intent: 'deposit',
          responseText: 'You have no wallets set up. Please create wallets in the app first.',
          actionTaken: 'no_wallets'
        };
      }

      return {
        success: true,
        intent: 'deposit',
        responseText: `You have ${wallets.length} wallet addresses available for deposits. Please check the app to see your deposit addresses for each blockchain.`,
        actionTaken: 'deposit_info_provided'
      };
    } catch (error) {
      console.error('Error in handleDepositCommand:', error);
      return {
        success: false,
        intent: 'deposit',
        responseText: 'Sorry, I could not get your deposit information.',
        actionTaken: 'deposit_error'
      };
    }
  }

  /**
   * Handle withdraw command
   */
  private static async handleWithdrawCommand(
    userId: string,
    entities: ExtractedEntities
  ): Promise<VoiceProcessingResult> {
    try {
      if (!entities.amount || !entities.token) {
        return {
          success: false,
          intent: 'withdraw',
          entities,
          responseText: 'To withdraw, please specify the amount and token. For example, say "withdraw 100 USDC to GTBank".',
          actionTaken: 'insufficient_info'
        };
      }

      // Get exchange rate
      const rate = await exchangeRateService.getCryptoToNGNRate(entities.token);
      const ngnAmount = entities.amount * rate;

      return {
        success: true,
        intent: 'withdraw',
        entities,
        responseText: `You want to withdraw ${entities.amount} ${entities.token}, which is approximately ${ngnAmount.toLocaleString()} naira. Please complete this transaction in the app.`,
        actionTaken: 'withdraw_info_provided'
      };
    } catch (error) {
      console.error('Error in handleWithdrawCommand:', error);
      return {
        success: false,
        intent: 'withdraw',
        responseText: 'Sorry, I could not process your withdrawal request.',
        actionTaken: 'withdraw_error'
      };
    }
  }

  /**
   * Handle pay command
   */
  private static async handlePayCommand(
    userId: string,
    entities: ExtractedEntities
  ): Promise<VoiceProcessingResult> {
    try {
      if (!entities.merchantId || !entities.amount) {
        return {
          success: false,
          intent: 'pay',
          entities,
          responseText: 'To pay a merchant, please specify the merchant ID and amount. For example, say "pay merchant 123 with 5000 naira using USDC".',
          actionTaken: 'insufficient_info'
        };
      }

      return {
        success: true,
        intent: 'pay',
        entities,
        responseText: `You want to pay merchant ${entities.merchantId} ${entities.amount} naira using ${entities.token || 'crypto'}. Please complete this payment in the app.`,
        actionTaken: 'payment_info_provided'
      };
    } catch (error) {
      console.error('Error in handlePayCommand:', error);
      return {
        success: false,
        intent: 'pay',
        responseText: 'Sorry, I could not process your payment request.',
        actionTaken: 'payment_error'
      };
    }
  }

  /**
   * Handle rates command
   */
  private static async handleRatesCommand(): Promise<VoiceProcessingResult> {
    try {
      const solRate = await exchangeRateService.getCryptoToNGNRate('SOL');
      const usdcRate = await exchangeRateService.getCryptoToNGNRate('USDC');

      return {
        success: true,
        intent: 'rates',
        responseText: `Current exchange rates: 1 SOL equals ${solRate.toLocaleString()} naira, and 1 USDC equals ${usdcRate.toLocaleString()} naira.`,
        actionTaken: 'rates_provided'
      };
    } catch (error) {
      console.error('Error in handleRatesCommand:', error);
      return {
        success: false,
        intent: 'rates',
        responseText: 'Sorry, I could not get the current exchange rates.',
        actionTaken: 'rates_error'
      };
    }
  }

  /**
   * Normalize bank name
   */
  private static normalizeBankName(bankInput: string): string {
    const bankMap: Record<string, string> = {
      'gtbank': 'GTBank',
      'gt bank': 'GTBank',
      'guaranty trust': 'GTBank',
      'access': 'Access Bank',
      'access bank': 'Access Bank',
      'zenith': 'Zenith Bank',
      'zenith bank': 'Zenith Bank',
      'uba': 'UBA',
      'united bank': 'UBA',
      'first bank': 'First Bank',
      'firstbank': 'First Bank',
      'fidelity': 'Fidelity Bank',
      'fidelity bank': 'Fidelity Bank'
    };

    const normalized = bankInput.toLowerCase().trim();
    return bankMap[normalized] || bankInput;
  }

  /**
   * Log voice command to database
   */
  private static async logVoiceCommand(commandData: {
    userId: string;
    commandText: string;
    originalAudioUrl?: string;
    languageDetected?: string;
    confidenceScore?: number;
    intent?: string;
    entities?: Record<string, any>;
    actionTaken?: string;
    success: boolean;
    responseText?: string;
    responseAudioUrl?: string;
  }): Promise<void> {
    try {
      await supabase
        .from('voice_commands')
        .insert({
          user_id: commandData.userId,
          command_text: commandData.commandText,
          original_audio_url: commandData.originalAudioUrl,
          language_detected: commandData.languageDetected,
          confidence_score: commandData.confidenceScore,
          intent: commandData.intent,
          entities: commandData.entities,
          action_taken: commandData.actionTaken,
          success: commandData.success,
          response_text: commandData.responseText,
          response_audio_url: commandData.responseAudioUrl
        });
    } catch (error) {
      console.error('Error logging voice command:', error);
    }
  }

  /**
   * Get voice command history for user
   */
  static async getVoiceHistory(userId: string, limit: number = 50): Promise<VoiceCommand[]> {
    try {
      const { data, error } = await supabase
        .from('voice_commands')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error getting voice history:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getVoiceHistory:', error);
      return [];
    }
  }
}

export { VoiceCommandService };
