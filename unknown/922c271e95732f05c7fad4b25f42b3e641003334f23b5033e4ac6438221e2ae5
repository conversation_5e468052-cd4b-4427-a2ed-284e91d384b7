/**
 * Crypto API Service - Multiple API sources for better reliability
 */

import { CHAIN_CONFIGS, SupportedChain } from '@/types/crossChain';

interface CryptoPriceData {
  [key: string]: {
    usd: number;
    usd_24h_change?: number;
  };
}

interface ApiResponse {
  data: CryptoPriceData;
  source: string;
  timestamp: string;
}

class CryptoApiService {
  private readonly coinIds: string[];

  constructor() {
    // Dynamically build coinIds from all chain configs
    const allCoinIds = new Set<string>();

    // Add base coins
    allCoinIds.add('solana');
    allCoinIds.add('usd-coin');
    allCoinIds.add('bitcoin');
    allCoinIds.add('ethereum');
    allCoinIds.add('matic-network');
    allCoinIds.add('binancecoin');
    allCoinIds.add('avalanche-2');
    allCoinIds.add('arbitrum');

    // Add all tokens from chain configs
    Object.values(CHAIN_CONFIGS).forEach(chainConfig => {
      chainConfig.supportedTokens.forEach(token => {
        if (token.coingeckoId) {
          allCoinIds.add(token.coingeckoId);
        }
      });
    });

    this.coinIds = Array.from(allCoinIds);
    console.log('🪙 CryptoApiService initialized with coins:', this.coinIds);
  }

  /**
   * Fetch crypto prices with multiple fallback options
   */
  async fetchCryptoPrices(): Promise<CryptoPriceData> {
    console.log('⚠️ CryptoApiService: DISABLED - Using reliable exchange rate service instead');

    // Return mock data to prevent crashes
    return {
      'solana': { usd: 150 },
      'usd-coin': { usd: 1 },
      'ethereum': { usd: 2500 },
      'bitcoin': { usd: 45000 }
    };
  }

  /**
   * Try to use your own API endpoint
   */
  private async fetchFromOwnApi(): Promise<CryptoPriceData> {
    const response = await fetch('/api/crypto-prices', {
      headers: { 'Accept': 'application/json' },
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    if (!response.ok) {
      throw new Error(`Own API error: ${response.status}`);
    }

    const result: ApiResponse = await response.json();
    return result.data;
  }

  /**
   * Use CORS proxy to access CoinGecko
   */
  private async fetchFromCorsProxy(proxyUrl: string): Promise<CryptoPriceData> {
    const apiUrl = `https://api.coingecko.com/api/v3/simple/price?ids=${this.coinIds.join(',')}&vs_currencies=usd&include_24hr_change=true`;
    
    const response = await fetch(
      `${proxyUrl}${encodeURIComponent(apiUrl)}`,
      {
        headers: { 'Accept': 'application/json' },
        signal: AbortSignal.timeout(15000) // 15 second timeout
      }
    );

    if (!response.ok) {
      throw new Error(`CORS proxy error: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * Use alternative crypto API (CoinCap as example)
   */
  private async fetchFromAlternativeApi(): Promise<CryptoPriceData> {
    // CoinCap API as alternative (has CORS support)
    const coinCapIds = {
      'solana': 'solana',
      'usd-coin': 'usd-coin',
      'bitcoin': 'bitcoin',
      'ethereum': 'ethereum',
      'matic-network': 'polygon',
      'binancecoin': 'binance-coin',
      'avalanche-2': 'avalanche',
      'arbitrum': 'arbitrum'
    };

    const promises = Object.entries(coinCapIds).map(async ([coinGeckoId, coinCapId]) => {
      const response = await fetch(`https://api.coincap.io/v2/assets/${coinCapId}`, {
        headers: { 'Accept': 'application/json' },
        signal: AbortSignal.timeout(10000)
      });

      if (!response.ok) {
        throw new Error(`CoinCap API error for ${coinCapId}: ${response.status}`);
      }

      const result = await response.json();
      return {
        id: coinGeckoId,
        price: parseFloat(result.data.priceUsd),
        change: parseFloat(result.data.changePercent24Hr)
      };
    });

    const results = await Promise.all(promises);
    
    // Convert to CoinGecko format
    const data: CryptoPriceData = {};
    results.forEach(({ id, price, change }) => {
      data[id] = {
        usd: price,
        usd_24h_change: change
      };
    });

    return data;
  }
}

export const cryptoApiService = new CryptoApiService();
