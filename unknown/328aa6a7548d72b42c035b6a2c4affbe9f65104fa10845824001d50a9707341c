/**
 * Exchange Rate Test Page
 * Test the reliable exchange rate service
 */

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { reliableExchangeRateService } from '@/services/reliableExchangeRateService';
import { CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';

const ExchangeRateTest: React.FC = () => {
  const [rates, setRates] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [testAmount, setTestAmount] = useState('10000');
  const [usdcResult, setUsdcResult] = useState('');
  const [solResult, setSolResult] = useState('');

  useEffect(() => {
    loadRates();
  }, []);

  useEffect(() => {
    if (rates && testAmount) {
      calculateConversions();
    }
  }, [rates, testAmount]);

  const loadRates = async () => {
    setLoading(true);
    try {
      const currentRates = await reliableExchangeRateService.getCurrentRates();
      setRates(currentRates);
      console.log('✅ Rates loaded:', currentRates);
    } catch (error) {
      console.error('❌ Error loading rates:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateConversions = async () => {
    if (!testAmount || !rates) return;

    try {
      const ngnAmount = parseFloat(testAmount);
      const usdcAmount = await reliableExchangeRateService.convertNgnToCrypto(ngnAmount, 'USDC');
      const solAmount = await reliableExchangeRateService.convertNgnToCrypto(ngnAmount, 'SOL');

      setUsdcResult(usdcAmount.toFixed(6));
      setSolResult(solAmount.toFixed(6));
    } catch (error) {
      console.error('Error calculating conversions:', error);
    }
  };

  const forceUpdate = async () => {
    setLoading(true);
    try {
      await reliableExchangeRateService.updateRates();
      await loadRates();
    } catch (error) {
      console.error('Error updating rates:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAgeColor = (lastUpdated: number) => {
    const ageMs = Date.now() - lastUpdated;
    const ageMinutes = ageMs / (1000 * 60);
    
    if (ageMinutes < 5) return 'text-green-600';
    if (ageMinutes < 15) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatAge = (lastUpdated: number) => {
    const ageMs = Date.now() - lastUpdated;
    const ageMinutes = Math.floor(ageMs / (1000 * 60));
    
    if (ageMinutes < 1) return 'Just now';
    if (ageMinutes < 60) return `${ageMinutes}m ago`;
    const hours = Math.floor(ageMinutes / 60);
    return `${hours}h ${ageMinutes % 60}m ago`;
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Exchange Rate Test</h1>
            <p className="text-gray-600">Test the reliable exchange rate service</p>
          </div>
          <Button onClick={forceUpdate} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Force Update
          </Button>
        </div>

        {/* Current Rates */}
        <Card>
          <CardHeader>
            <CardTitle>Current Exchange Rates</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4">
                <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                <p>Loading rates...</p>
              </div>
            ) : rates ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold">USDC/NGN</h3>
                        <p className="text-2xl font-bold">₦{rates.USDC.toLocaleString()}</p>
                      </div>
                      <CheckCircle className="h-6 w-6 text-green-500" />
                    </div>
                  </div>
                  
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold">SOL/NGN</h3>
                        <p className="text-2xl font-bold">₦{rates.SOL.toLocaleString()}</p>
                      </div>
                      <CheckCircle className="h-6 w-6 text-green-500" />
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <p className={`text-sm ${getAgeColor(rates.lastUpdated)}`}>
                    Last updated: {formatAge(rates.lastUpdated)}
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <AlertTriangle className="h-6 w-6 text-red-500 mx-auto mb-2" />
                <p>Failed to load rates</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Conversion Test */}
        <Card>
          <CardHeader>
            <CardTitle>Conversion Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="amount">NGN Amount</Label>
              <Input
                id="amount"
                type="number"
                value={testAmount}
                onChange={(e) => setTestAmount(e.target.value)}
                placeholder="Enter NGN amount"
              />
            </div>

            {testAmount && rates && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold mb-2">USDC Equivalent</h4>
                  <p className="text-xl font-bold text-blue-600">{usdcResult} USDC</p>
                  <p className="text-sm text-gray-600">
                    Rate: 1 USDC = ₦{rates.USDC.toLocaleString()}
                  </p>
                </div>

                <div className="p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-semibold mb-2">SOL Equivalent</h4>
                  <p className="text-xl font-bold text-purple-600">{solResult} SOL</p>
                  <p className="text-sm text-gray-600">
                    Rate: 1 SOL = ₦{rates.SOL.toLocaleString()}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Service Health */}
        <Card>
          <CardHeader>
            <CardTitle>Service Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Reliable Exchange Rate Service: Active</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Multiple API Fallbacks: Enabled</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Rate Caching: 5 minute cache</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <span>Old Services: Disabled to prevent conflicts</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default ExchangeRateTest;
