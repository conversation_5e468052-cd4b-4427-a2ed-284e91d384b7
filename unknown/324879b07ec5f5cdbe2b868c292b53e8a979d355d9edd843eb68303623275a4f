/**
 * Simple verification API that works with the webhook
 */

export default async function handler(req, res) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight request
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method === 'POST') {
    // Verify a code
    const { code, userId } = req.body;
    
    if (!code || !userId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Code and userId required' 
      });
    }

    // Check global verification codes
    const verificationCodes = global.verificationCodes || new Map();
    const codeData = verificationCodes.get(code.toUpperCase());
    
    if (!codeData) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid verification code' 
      });
    }

    // Check if code is expired (30 minutes)
    const isExpired = Date.now() - codeData.timestamp > 30 * 60 * 1000;
    if (isExpired) {
      verificationCodes.delete(code.toUpperCase());
      return res.status(400).json({ 
        success: false, 
        error: 'Verification code expired' 
      });
    }

    // Mark as verified
    codeData.verified = true;
    codeData.userId = userId;

    console.log(`User ${userId} successfully verified with Telegram ID ${codeData.telegramId}`);

    return res.status(200).json({
      success: true,
      telegramId: codeData.telegramId
    });

  } else if (req.method === 'GET') {
    // Debug endpoint - show all codes
    const verificationCodes = global.verificationCodes || new Map();
    const codes = Array.from(verificationCodes.entries()).map(([code, data]) => ({
      code,
      telegramId: data.telegramId,
      timestamp: new Date(data.timestamp).toISOString(),
      verified: data.verified,
      expired: Date.now() - data.timestamp > 30 * 60 * 1000
    }));

    return res.status(200).json({ codes });

  } else {
    return res.status(405).json({ error: 'Method not allowed' });
  }
}
