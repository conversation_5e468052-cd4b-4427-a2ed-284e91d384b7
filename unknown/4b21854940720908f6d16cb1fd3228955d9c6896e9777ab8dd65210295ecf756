/**
 * Direct Crypto Payment Page
 * Handles direct crypto transfers to merchant wallets
 */

import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useWallet } from '@/contexts/WalletContext';
import { 
  Wallet, 
  ArrowRight, 
  Copy,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  QrCode
} from 'lucide-react';

const CryptoPayment: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { wallets, sendTransaction } = useWallet();
  const { toast } = useToast();
  
  const [paymentData, setPaymentData] = useState<any>(null);
  const [processing, setProcessing] = useState(false);
  const [transactionSignature, setTransactionSignature] = useState<string>('');

  useEffect(() => {
    const locationState = location.state as any;
    if (locationState?.merchantPayment && locationState?.merchantData) {
      setPaymentData(locationState);
    } else {
      // Redirect back if no payment data
      navigate('/');
    }
  }, [location, navigate]);

  const handleCryptoPayment = async () => {
    if (!user || !paymentData) {
      toast({
        title: "Invalid Payment Data",
        description: "Payment information is missing",
        variant: "destructive",
      });
      return;
    }

    setProcessing(true);

    try {
      const { merchantData, cryptoAmount, cryptoSymbol, recipientAddress } = paymentData;

      console.log('🚀 Starting crypto payment:', {
        amount: cryptoAmount,
        symbol: cryptoSymbol,
        to: recipientAddress,
        merchant: merchantData.merchantName
      });

      // For now, we'll simulate the payment and record it in the database
      // In production, you would integrate with actual wallet providers like Phantom, Solflare, etc.

      // Simulate transaction signature (in production, this comes from the actual blockchain transaction)
      const simulatedSignature = `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Record the payment in database
      const { error: paymentError } = await supabase.from('merchant_crypto_payments').insert({
        merchant_id: merchantData.merchantId,
        customer_id: user.id,
        amount_ngn: merchantData.paymentAmount,
        crypto_symbol: cryptoSymbol,
        crypto_amount: parseFloat(cryptoAmount),
        exchange_rate: merchantData.exchangeRate,
        recipient_address: recipientAddress,
        transaction_signature: simulatedSignature,
        payment_description: merchantData.paymentDescription,
        status: 'sent' // Will be 'confirmed' when actual blockchain integration is added
      });

      if (paymentError) {
        throw new Error('Failed to record payment in database');
      }

      setTransactionSignature(simulatedSignature);

      // Update customer's wallet balance (deduct the crypto amount)
      const customerWallet = wallets.find(w => w.token_type === cryptoSymbol);
      if (customerWallet) {
        const newBalance = customerWallet.balance - parseFloat(cryptoAmount);
        await supabase
          .from('wallets')
          .update({ balance: Math.max(0, newBalance) })
          .eq('id', customerWallet.id);
      }

      toast({
        title: "Payment Sent Successfully! 🎉",
        description: `${cryptoAmount} ${cryptoSymbol} sent to ${merchantData.merchantName}`,
      });

      console.log('✅ Crypto payment recorded:', simulatedSignature);

      // Show success state
      setTimeout(() => {
        navigate('/dashboard');
      }, 5000);

    } catch (error) {
      console.error('Crypto payment error:', error);
      toast({
        title: "Payment Failed",
        description: error.message || "Failed to send crypto payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const copyAddress = () => {
    if (paymentData?.recipientAddress) {
      navigator.clipboard.writeText(paymentData.recipientAddress);
      toast({
        title: "Address Copied! 📋",
        description: "Wallet address copied to clipboard",
      });
    }
  };

  const viewTransaction = () => {
    if (transactionSignature) {
      window.open(`https://solscan.io/tx/${transactionSignature}`, '_blank');
    }
  };

  if (!paymentData) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto p-4">
          <Card>
            <CardContent className="p-8 text-center">
              <AlertTriangle className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Invalid Payment</h2>
              <p className="text-gray-600 mb-4">
                No payment data found. Please scan the QR code again.
              </p>
              <Button onClick={() => navigate('/')}>
                Go Home
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  if (transactionSignature) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto p-4">
          <Card className="bg-gradient-to-br from-green-50 to-blue-50 border-green-200">
            <CardContent className="p-8 text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2 text-green-800">Payment Successful! 🎉</h2>
              <p className="text-gray-600 mb-6">
                Your crypto payment has been sent successfully to {paymentData.merchantData.merchantName}
              </p>
              
              <div className="bg-white p-4 rounded-lg border mb-6">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-medium">{paymentData.cryptoAmount} {paymentData.cryptoSymbol}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>To:</span>
                    <span className="font-medium">{paymentData.merchantData.merchantName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>For:</span>
                    <span className="font-medium">{paymentData.merchantData.paymentDescription}</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-3 justify-center">
                <Button onClick={viewTransaction} variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Transaction
                </Button>
                <Button onClick={() => navigate('/dashboard')}>
                  Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto p-4 space-y-6">
        {/* Payment Header */}
        <Card>
          <CardHeader className="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-3">
              <Wallet className="h-6 w-6" />
              Direct Crypto Payment
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="text-xl font-bold mb-2">{paymentData.merchantData.merchantName}</h3>
              <p className="text-gray-600 mb-4">{paymentData.merchantData.paymentDescription}</p>
              
              <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <p className="text-sm text-purple-700 mb-2">You're paying directly with crypto</p>
                <p className="text-2xl font-bold text-purple-800">
                  {paymentData.cryptoAmount} {paymentData.cryptoSymbol}
                </p>
                <p className="text-sm text-gray-600">
                  ≈ ₦{paymentData.merchantData.paymentAmount.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recipient Details */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Recipient Wallet Address</label>
              <div className="flex items-center gap-2 mt-1">
                <div className="flex-1 p-3 bg-gray-50 rounded-lg border font-mono text-sm">
                  {paymentData.recipientAddress}
                </div>
                <Button onClick={copyAddress} variant="outline" size="sm">
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Amount</label>
                <p className="text-lg font-semibold">{paymentData.cryptoAmount} {paymentData.cryptoSymbol}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Network</label>
                <p className="text-lg font-semibold">Solana</p>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-start gap-2">
                <QrCode className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-800">Direct Crypto Payment</h4>
                  <p className="text-sm text-blue-700">
                    This payment will be sent directly to the merchant's wallet. 
                    No conversion to Naira will occur.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Button */}
        <Card>
          <CardContent className="p-6">
            <Button 
              onClick={handleCryptoPayment}
              disabled={processing || !wallets.length}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              size="lg"
            >
              {processing ? (
                "Sending Payment..."
              ) : !wallets.length ? (
                "Connect Wallet to Pay"
              ) : (
                <>
                  Send {paymentData.cryptoAmount} {paymentData.cryptoSymbol}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>

            {!wallets.length && (
              <p className="text-center text-sm text-gray-600 mt-2">
                Please connect your Solana wallet to make crypto payments
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default CryptoPayment;
