/**
 * Telegram Bot Setup Page (Admin Only)
 * For setting up the Telegram bot
 */

import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { 
  Bo<PERSON>, 
  CheckCircle, 
  AlertTriangle, 
  ExternalLink,
  Copy,
  Settings
} from 'lucide-react';

const TelegramSetup: React.FC = () => {
  const { toast } = useToast();
  const [botToken, setBotToken] = useState('');
  const [webhookUrl, setWebhookUrl] = useState('');
  const [isSetup, setIsSetup] = useState(false);
  const [loading, setLoading] = useState(false);
  const [botUsername, setBotUsername] = useState('');

  const setupBot = async () => {
    if (!botToken || !webhookUrl) {
      toast({
        title: "Missing Information",
        description: "Please provide both bot token and webhook URL",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // Direct API calls to Telegram (client-side setup)
      console.log('Setting up Telegram bot...');

      // Set webhook (only if URL provided)
      let webhookResult = { ok: true, description: 'Webhook skipped for local testing' };

      if (webhookUrl && webhookUrl.trim()) {
        const webhookResponse = await fetch(`https://api.telegram.org/bot${botToken}/setWebhook`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: webhookUrl,
            allowed_updates: ['message']
          }),
        });

        webhookResult = await webhookResponse.json();

        if (!webhookResult.ok) {
          throw new Error(`Failed to set webhook: ${webhookResult.description}`);
        }
      }

      // Set bot commands
      const commands = [
        { command: 'start', description: 'Start using the bot and link your account' },
        { command: 'balance', description: 'Check your crypto balances' },
        { command: 'deposit', description: 'Get deposit addresses' },
        { command: 'withdraw', description: 'Withdraw crypto to bank' },
        { command: 'rates', description: 'Get current exchange rates' },
        { command: 'pay', description: 'Pay merchant via QR' },
        { command: 'history', description: 'View transaction history' },
        { command: 'help', description: 'Show all commands' }
      ];

      const commandsResponse = await fetch(`https://api.telegram.org/bot${botToken}/setMyCommands`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          commands: commands
        }),
      });

      const commandsResult = await commandsResponse.json();

      if (!commandsResult.ok) {
        throw new Error(`Failed to set commands: ${commandsResult.description}`);
      }

      setIsSetup(true);
      toast({
        title: "Bot Setup Successful! 🎉",
        description: "Your Telegram bot is now ready to use",
      });

    } catch (error) {
      console.error('Setup error:', error);
      toast({
        title: "Setup Failed",
        description: error.message || "Failed to set up bot",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const copyBotLink = () => {
    const link = `https://t.me/${botUsername}`;
    navigator.clipboard.writeText(link);
    toast({
      title: "Bot Link Copied! 📋",
      description: "Share this link with your users",
    });
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Bot className="h-8 w-8 text-blue-600" />
              <div>
                <CardTitle className="text-2xl">Telegram Bot Setup</CardTitle>
                <p className="text-gray-600">Configure your SolPay Telegram bot</p>
              </div>
            </div>
          </CardHeader>
        </Card>

        {!isSetup ? (
          <>
            {/* Setup Instructions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Setup Instructions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
                    <div>
                      <p className="font-medium">Create Bot with BotFather</p>
                      <p className="text-sm text-gray-600">
                        1. Search for @BotFather in Telegram<br/>
                        2. Send /newbot command<br/>
                        3. Choose bot name: "SolPay Crypto Bot"<br/>
                        4. Choose username: "solpay_crypto_bot"<br/>
                        5. Copy the bot token
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
                    <div>
                      <p className="font-medium">Get Your Webhook URL</p>
                      <p className="text-sm text-gray-600">
                        Your webhook URL should be: https://your-domain.com/api/telegram/webhook
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
                    <div>
                      <p className="font-medium">Configure Bot</p>
                      <p className="text-sm text-gray-600">
                        Enter the bot token and webhook URL below to complete setup
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Setup Form */}
            <Card>
              <CardHeader>
                <CardTitle>Bot Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="botToken">Bot Token</Label>
                  <Input
                    id="botToken"
                    type="password"
                    value={botToken}
                    onChange={(e) => setBotToken(e.target.value)}
                    placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Get this from @BotFather after creating your bot
                  </p>
                </div>

                <div>
                  <Label htmlFor="webhookUrl">Webhook URL (Optional for testing)</Label>
                  <Input
                    id="webhookUrl"
                    value={webhookUrl}
                    onChange={(e) => setWebhookUrl(e.target.value)}
                    placeholder="https://your-app.vercel.app/api/telegram/webhook"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Leave empty for testing. Only needed when deployed to production.
                  </p>
                </div>

                <div>
                  <Label htmlFor="botUsername">Bot Username (Optional)</Label>
                  <Input
                    id="botUsername"
                    value={botUsername}
                    onChange={(e) => setBotUsername(e.target.value)}
                    placeholder="solpay_crypto_bot"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    For generating share links
                  </p>
                </div>

                <Button
                  onClick={setupBot}
                  disabled={loading || !botToken}
                  className="w-full"
                >
                  {loading ? 'Setting up...' : 'Setup Bot'}
                </Button>
              </CardContent>
            </Card>
          </>
        ) : (
          /* Success State */
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div>
                  <h3 className="text-xl font-bold text-green-900">Bot Setup Complete!</h3>
                  <p className="text-green-700">Your Telegram bot is now ready for users</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>Bot Link for Users</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      value={`https://t.me/${botUsername}`}
                      readOnly
                      className="bg-white"
                    />
                    <Button variant="outline" onClick={copyBotLink}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="bg-white p-4 rounded-lg border">
                  <h4 className="font-medium mb-2">Next Steps:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Share the bot link with your users</li>
                    <li>• Users start the bot with /start</li>
                    <li>• They get a verification code</li>
                    <li>• They enter the code in your app</li>
                    <li>• Bot is ready to use!</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Bot Commands Reference */}
        <Card>
          <CardHeader>
            <CardTitle>Available Bot Commands</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/start</code>
                  <p className="text-sm text-gray-600">Link account with verification code</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/balance</code>
                  <p className="text-sm text-gray-600">Check crypto balances</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/deposit</code>
                  <p className="text-sm text-gray-600">Get deposit addresses</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/rates</code>
                  <p className="text-sm text-gray-600">Current exchange rates</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/withdraw 100 USDC GTBank</code>
                  <p className="text-sm text-gray-600">Withdraw to bank account</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/pay merchant_123 5000 USDC</code>
                  <p className="text-sm text-gray-600">Pay merchant via QR</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/history</code>
                  <p className="text-sm text-gray-600">Transaction history</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <code className="text-blue-600">/help</code>
                  <p className="text-sm text-gray-600">Show all commands</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default TelegramSetup;
