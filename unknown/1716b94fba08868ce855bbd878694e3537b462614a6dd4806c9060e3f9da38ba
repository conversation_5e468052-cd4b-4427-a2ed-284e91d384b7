/**
 * Voice Commands Component
 * Allows users to interact with the app using voice commands
 */

import React, { useState, useRef, useEffect } from 'react';
import Layout from '@/components/Layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { VoiceCommandService } from '@/services/voiceCommandService';
import { 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Play,
  Square,
  Languages,
  MessageSquare,
  Zap,
  AlertTriangle
} from 'lucide-react';

const VoiceCommands: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [language, setLanguage] = useState('en');
  const [audioEnabled, setAudioEnabled] = useState(true);
  
  const recognitionRef = useRef<any>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);

  useEffect(() => {
    // Initialize speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      recognitionRef.current = new SpeechRecognition();

      // Configure for better recognition
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.maxAlternatives = 1;
      recognitionRef.current.lang = getLanguageCode(language);
      
      recognitionRef.current.onstart = () => {
        setIsListening(true);
        setTranscript('');
        setResponse('');
      };
      
      recognitionRef.current.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        // Update display with interim results
        setTranscript(finalTranscript + interimTranscript);

        // Process final results
        if (finalTranscript) {
          console.log('🎤 Voice input received:', finalTranscript);
          handleVoiceCommand(finalTranscript.trim());
        }
      };
      
      recognitionRef.current.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        
        toast({
          title: "Voice Recognition Error",
          description: "Could not process voice input. Please try again.",
          variant: "destructive",
        });
      };
      
      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }

    // Initialize speech synthesis
    if ('speechSynthesis' in window) {
      synthRef.current = window.speechSynthesis;
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (synthRef.current) {
        synthRef.current.cancel();
      }
    };
  }, [language]);

  const getLanguageCode = (lang: string): string => {
    const codes: Record<string, string> = {
      'en': 'en-US',
      'yo': 'en-NG', // Use English-Nigeria for Yoruba (better support)
      'ha': 'en-NG', // Use English-Nigeria for Hausa (better support)
      'ig': 'en-NG'  // Use English-Nigeria for Igbo (better support)
    };
    return codes[lang] || 'en-US';
  };

  const getLanguageName = (lang: string): string => {
    const names: Record<string, string> = {
      'en': 'English',
      'yo': 'Yoruba',
      'ha': 'Hausa',
      'ig': 'Igbo'
    };
    return names[lang] || 'English';
  };

  const startListening = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to use voice commands",
        variant: "destructive",
      });
      return;
    }

    if (!recognitionRef.current) {
      toast({
        title: "Voice Recognition Not Supported",
        description: "Your browser doesn't support voice recognition. Please use Chrome, Edge, or Safari.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Request microphone permission first
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Stop any existing recognition
      if (isListening) {
        recognitionRef.current.stop();
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Configure and start recognition
      recognitionRef.current.lang = getLanguageCode(language);
      recognitionRef.current.start();

      console.log('🎤 Voice recognition started in', getLanguageName(language));

      toast({
        title: "🎤 Listening...",
        description: `Say a command in ${getLanguageName(language)}`,
      });

    } catch (error) {
      console.error('Error starting voice recognition:', error);

      if (error.name === 'NotAllowedError') {
        toast({
          title: "Microphone Permission Denied",
          description: "Please allow microphone access to use voice commands",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Voice Recognition Error",
          description: "Could not start voice recognition. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    setIsListening(false);
  };

  const handleVoiceCommand = async (transcript: string) => {
    if (!user) return;

    setIsProcessing(true);

    try {
      const command = transcript.toLowerCase().trim();
      console.log('🎤 Processing voice command:', command);

      // Flexible command matching with natural language understanding
      let commandExecuted = false;
      let responseText = '';

      // Helper function to get response in current language
      const getResponse = (responses: any) => {
        return responses[language] || responses.en;
      };

      // Helper function for better phonetic matching
      const matchesPhonetically = (command: string, keywords: string[]) => {
        const cleanCommand = command.toLowerCase().replace(/[^a-z\s]/g, '');
        return keywords.some(keyword => {
          const cleanKeyword = keyword.toLowerCase();
          // Exact match
          if (cleanCommand.includes(cleanKeyword)) return true;
          // Phonetic variations (remove repeated letters)
          const phoneticCommand = cleanCommand.replace(/(.)\1+/g, '$1');
          const phoneticKeyword = cleanKeyword.replace(/(.)\1+/g, '$1');
          if (phoneticCommand.includes(phoneticKeyword)) return true;
          // Starts with match for short words
          if (cleanKeyword.length <= 4 && cleanCommand.startsWith(cleanKeyword)) return true;
          return false;
        });
      };

      // SIMPLIFIED CORE COMMANDS ONLY - 4 main commands

      // 1. HOME/DASHBOARD - Most important
      const homeKeywords = [
        // English
        'home', 'dashboard',
        // Yoruba - phonetic variations
        'ile', 'ilee', 'ileee', 'dashboard',
        // Hausa - phonetic variations
        'gida', 'gidaa', 'gidaaa', 'dashboard',
        // Igbo - phonetic variations
        'ulo', 'uloo', 'ulooo', 'dashboard'
      ];

      // 2. WALLET/MONEY - Second most important
      const walletKeywords = [
        // English
        'wallet', 'money', 'balance',
        // Yoruba - how people actually say it
        'owo', 'owoo', 'owooo', 'apo owo', 'apo', 'wallet',
        // Hausa - how people actually say it
        'kudi', 'kudii', 'kudiii', 'jakar kudi', 'jaka', 'wallet',
        // Igbo - how people actually say it
        'ego', 'egoo', 'egooo', 'akpa ego', 'akpa', 'wallet'
      ];

      // 3. SELL/CASH OUT - Third most important
      const sellKeywords = [
        // English
        'sell', 'cash out', 'convert',
        // Yoruba - simple words
        'ta', 'taa', 'taaa', 'sell',
        // Hausa - simple words
        'sayar', 'sayaa', 'sayaaa', 'sell',
        // Igbo - simple words
        'ree', 'reee', 'reeee', 'sell'
      ];

      // 4. HELP - Fourth most important
      const helpKeywords = [
        // English
        'help', 'commands',
        // Yoruba - simple
        'help', 'iranlowo',
        // Hausa - simple
        'help', 'taimako',
        // Igbo - simple
        'help', 'enyemaka'
      ];

      // SIMPLIFIED COMMAND PROCESSING - Only 4 core commands with better phonetic matching

      // 1. HOME/DASHBOARD
      if (matchesPhonetically(command, homeKeywords)) {
        navigate('/');
        responseText = getResponse({
          en: 'Going home',
          yo: 'N lo si ile',
          ha: 'Ina je gida',
          ig: 'Ana aga ulo'
        });
        commandExecuted = true;
      }
      // 2. WALLET/MONEY
      else if (matchesPhonetically(command, walletKeywords)) {
        navigate('/wallets');
        responseText = getResponse({
          en: 'Checking your money',
          yo: 'N wo owo rẹ',
          ha: 'Ina duba kudi ɗinku',
          ig: 'Ana ele ego gi'
        });
        commandExecuted = true;
      }
      // 3. SELL/CASH OUT
      else if (matchesPhonetically(command, sellKeywords)) {
        navigate('/off-ramp');
        responseText = getResponse({
          en: 'Selling your crypto',
          yo: 'N ta crypto rẹ',
          ha: 'Ina sayar da crypto ɗinku',
          ig: 'Ana ere crypto gi'
        });
        commandExecuted = true;
      }
      // 4. HELP
      else if (matchesPhonetically(command, helpKeywords)) {
        responseText = getResponse({
          en: 'Say: "home", "money", "sell", or "send 1000"',
          yo: 'Sọ: "ile", "owo", "ta", tabi "fi 1000 ranṣẹ"',
          ha: 'Ku ce: "gida", "kudi", "sayar", ko "aika 1000"',
          ig: 'Kwuo: "ulo", "ego", "ree", ma ọ bụ "ziga 1000"'
        });
        commandExecuted = true;
      }
      // Check for amount-based commands (send money)
      else if (command.match(/(\d+)/)) {
        const amountMatch = command.match(/(\d+(?:\.\d+)?)/);
        if (amountMatch) {
          const amount = amountMatch[1];
          navigate('/off-ramp', { state: { prefilledAmount: amount } });
          responseText = getResponse({
            en: `Preparing to send ${amount} naira`,
            yo: `N mura lati fi ${amount} naira ranṣẹ`,
            ha: `Ina shirya aika naira ${amount}`,
            ig: `Ana akwado iziga naira ${amount}`
          });
          commandExecuted = true;
        }
      }

      setResponse(responseText);

      if (commandExecuted) {
        toast({
          title: getResponse({
            en: "✅ Command Executed",
            yo: "✅ Aṣẹ Ti Ṣe",
            ha: "✅ An Aiwatar Da Umarnin",
            ig: "✅ Emere Iwu Ahụ"
          }),
          description: responseText,
        });

        // Speak the response if audio is enabled
        if (audioEnabled && synthRef.current) {
          speakResponse(responseText);
        }
      } else {
        const errorMessage = getResponse({
          en: "I didn't understand that. Try saying: 'dashboard', 'wallet', 'off-ramp', 'cards', 'merchant', 'help', or 'send 1000 naira'",
          yo: "Mi o ye mi. Gbiyanju sọ: 'dashboard', 'wallet', 'off-ramp', 'cards', 'merchant', 'iranlowo', tabi 'fi 1000 naira ranṣẹ'",
          ha: "Ban fahimce ba. Ku gwada cewa: 'dashboard', 'wallet', 'off-ramp', 'cards', 'merchant', 'taimako', ko 'aika 1000 naira'",
          ig: "Aghọtaghị m nke ahụ. Gbalịa ikwu: 'dashboard', 'wallet', 'off-ramp', 'cards', 'merchant', 'enyemaka', ma ọ bụ 'ziga 1000 naira'"
        });
        setResponse(errorMessage);
        toast({
          title: getResponse({
            en: "❓ Command Not Recognized",
            yo: "❓ Aṣẹ Ko Ye Mi",
            ha: "❓ Ban Fahimci Umarnin Ba",
            ig: "❓ Aghọtaghị Iwu Ahụ"
          }),
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error processing voice command:', error);
      const errorMessage = 'Sorry, I could not process your command. Please try again.';
      setResponse(errorMessage);
      
      toast({
        title: "Processing Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const speakResponse = (text: string) => {
    if (!synthRef.current || !audioEnabled) return;

    // Cancel any ongoing speech
    synthRef.current.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = getLanguageCode(language);
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 0.8;

    synthRef.current.speak(utterance);
  };

  const playResponse = () => {
    if (response) {
      speakResponse(response);
    }
  };

  const exampleCommands = {
    en: [
      "Home", "Dashboard",
      "Money", "Wallet", "Balance",
      "Sell", "Cash out",
      "Send 1000", "Send 5000",
      "Help", "Commands"
    ],
    yo: [
      "Ile", "Dashboard",
      "Owo", "Wallet", "Balance",
      "Ta", "Cash out",
      "Fi 1000 ranṣẹ", "Fi 5000 ranṣẹ",
      "Help", "Iranlowo"
    ],
    ha: [
      "Gida", "Dashboard",
      "Kudi", "Wallet", "Balance",
      "Sayar", "Cash out",
      "Aika 1000", "Aika 5000",
      "Help", "Taimako"
    ],
    ig: [
      "Ulo", "Dashboard",
      "Ego", "Wallet", "Balance",
      "Ree", "Cash out",
      "Ziga 1000", "Ziga 5000",
      "Help", "Enyemaka"
    ]
  };

  return (
    <Layout>
      <div className="max-w-2xl mx-auto p-4 space-y-6">
      {/* Header */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Mic className="h-8 w-8 text-purple-600" />
          <div>
            <h2 className="text-2xl font-bold">Voice Commands</h2>
            <p className="text-gray-600">Control your crypto with voice in multiple languages</p>
          </div>
        </div>
      </Card>

      {/* Language Selection */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Languages className="h-6 w-6 text-blue-600" />
          <h3 className="text-lg font-semibold">Language Settings</h3>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">🇺🇸 English</SelectItem>
                <SelectItem value="yo">🇳🇬 Yoruba</SelectItem>
                <SelectItem value="ha">🇳🇬 Hausa</SelectItem>
                <SelectItem value="ig">🇳🇬 Igbo</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAudioEnabled(!audioEnabled)}
            className="flex items-center gap-2"
          >
            {audioEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            {audioEnabled ? 'Audio On' : 'Audio Off'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                await navigator.mediaDevices.getUserMedia({ audio: true });
                toast({
                  title: "✅ Microphone Test Passed",
                  description: "Your microphone is working correctly",
                });
              } catch (error) {
                toast({
                  title: "❌ Microphone Test Failed",
                  description: "Please allow microphone access in your browser",
                  variant: "destructive",
                });
              }
            }}
            className="flex items-center gap-2"
          >
            <Mic className="h-4 w-4" />
            Test Mic
          </Button>
        </div>
      </Card>

      {/* Voice Interface */}
      <Card className="p-6">
        <div className="text-center space-y-6">
          {/* Microphone Button */}
          <div className="relative">
            <Button
              onClick={isListening ? stopListening : startListening}
              disabled={isProcessing}
              className={`w-24 h-24 rounded-full text-white transition-all duration-300 ${
                isListening 
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse' 
                  : 'bg-purple-500 hover:bg-purple-600'
              }`}
            >
              {isListening ? (
                <Square className="h-8 w-8" />
              ) : (
                <Mic className="h-8 w-8" />
              )}
            </Button>
            
            {isListening && (
              <div className="absolute inset-0 rounded-full border-4 border-red-300 animate-ping"></div>
            )}
          </div>

          {/* Status */}
          <div className="space-y-2">
            {isListening && (
              <p className="text-red-600 font-medium animate-pulse">
                🎤 Listening in {getLanguageName(language)}...
              </p>
            )}
            
            {isProcessing && (
              <p className="text-blue-600 font-medium">
                🔄 Processing your command...
              </p>
            )}
            
            {!isListening && !isProcessing && (
              <p className="text-gray-600">
                Tap the microphone to start speaking
              </p>
            )}
          </div>

          {/* Transcript */}
          {transcript && (
            <Card className="p-4 bg-blue-50 border-blue-200">
              <div className="flex items-start gap-3">
                <MessageSquare className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="text-left">
                  <p className="font-medium text-blue-900 mb-1">You said:</p>
                  <p className="text-blue-700">"{transcript}"</p>
                </div>
              </div>
            </Card>
          )}

          {/* Response */}
          {response && (
            <Card className="p-4 bg-green-50 border-green-200">
              <div className="flex items-start gap-3">
                <Zap className="h-5 w-5 text-green-600 mt-0.5" />
                <div className="text-left flex-1">
                  <p className="font-medium text-green-900 mb-1">Response:</p>
                  <p className="text-green-700">{response}</p>
                </div>
                
                {audioEnabled && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={playResponse}
                    className="text-green-600"
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </Card>
          )}
        </div>
      </Card>

      {/* Example Commands */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <MessageSquare className="h-6 w-6 text-gray-600" />
          <h3 className="text-lg font-semibold">Example Commands ({getLanguageName(language)})</h3>
        </div>

        <div className="grid grid-cols-1 gap-2">
          {exampleCommands[language as keyof typeof exampleCommands].map((command, index) => (
            <div key={index} className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm font-mono text-gray-700">"{command}"</p>
            </div>
          ))}
        </div>
      </Card>

      {/* Features */}
      <Card className="p-6 bg-purple-50 border-purple-200">
        <div className="flex items-start gap-3">
          <Zap className="h-5 w-5 text-purple-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-purple-900 mb-2">Voice Command Features</h3>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• <strong>Multi-language support</strong> - English, Yoruba, Hausa, Igbo</li>
              <li>• <strong>Balance checking</strong> - "Check my balance"</li>
              <li>• <strong>Deposit addresses</strong> - "Show my deposit addresses"</li>
              <li>• <strong>Exchange rates</strong> - "What are the current rates?"</li>
              <li>• <strong>Withdrawals</strong> - "Withdraw 100 USDC to GTBank"</li>
              <li>• <strong>Merchant payments</strong> - "Pay merchant 123 with 5000 naira"</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Browser Support Notice */}
      <Card className="p-6 bg-yellow-50 border-yellow-200">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-900 mb-2">Browser Compatibility</h3>
            <p className="text-sm text-yellow-700">
              Voice commands work best in <strong>Chrome</strong> and <strong>Edge</strong> browsers. 
              Some features may not be available in Safari or Firefox. 
              Make sure to allow microphone access when prompted.
            </p>
          </div>
        </div>
      </Card>
      </div>
    </Layout>
  );
};

export default VoiceCommands;
