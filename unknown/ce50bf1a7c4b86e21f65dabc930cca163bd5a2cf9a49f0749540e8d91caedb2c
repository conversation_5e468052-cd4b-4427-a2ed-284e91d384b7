/**
 * Telegram Bot Webhook Handler for Vercel
 * Handles incoming messages from Telegram
 */

// Simple in-memory storage for verification codes (in production, use a database)
const verificationCodes = new Map();

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const update = req.body;
    console.log('Received Telegram update:', JSON.stringify(update, null, 2));
    
    // Check if it's a message update
    if (update.message && update.message.text) {
      const message = {
        telegramId: update.message.from.id,
        text: update.message.text.trim(),
        messageId: update.message.message_id,
        chatId: update.message.chat.id,
        username: update.message.from.username,
        firstName: update.message.from.first_name,
        lastName: update.message.from.last_name
      };

      console.log('Processing message:', message);

      // Process the message and get response
      const response = await processMessage(message);
      
      // Send response back to user
      if (response) {
        await sendTelegramMessage(message.chatId, response);
      }
    }

    res.status(200).json({ ok: true });
  } catch (error) {
    console.error('Telegram webhook error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Process incoming Telegram message
 */
async function processMessage(message) {
  const { telegramId, text, firstName, username } = message;
  const command = text.toLowerCase();

  console.log(`Processing command: ${command} from user: ${telegramId}`);

  try {
    // Handle different commands
    if (command === '/start') {
      return await handleStart(telegramId, firstName, username);
    } else if (command === '/help') {
      return handleHelp();
    } else if (command === '/balance') {
      return handleBalance(telegramId);
    } else if (command === '/deposit') {
      return handleDeposit(telegramId);
    } else if (command === '/rates') {
      return handleRates();
    } else if (command.startsWith('/withdraw')) {
      return handleWithdraw(telegramId, text);
    } else if (command.startsWith('/pay')) {
      return handlePay(telegramId, text);
    } else if (command === '/history') {
      return handleHistory(telegramId);
    } else {
      return getUnknownCommandResponse(command);
    }
  } catch (error) {
    console.error('Error processing message:', error);
    return '❌ Sorry, something went wrong. Please try again later.';
  }
}

/**
 * Handle /start command - Check if user is already linked
 */
async function handleStart(telegramId, firstName, username) {
  try {
    // First, check if this Telegram user is already linked to a SolPay account
    const isAlreadyLinked = await checkIfUserIsLinked(telegramId);

    if (isAlreadyLinked) {
      return `✅ Welcome back, ${firstName || 'there'}!

Your Telegram account is already connected to SolPay.

Available commands:
/balance - Check your crypto balances
/deposit - Get deposit addresses
/rates - Current exchange rates
/help - Show all commands

You're all set! 🚀`;
    }

    // If not linked, generate verification code
    const verificationCode = Math.random().toString(36).substr(2, 8).toUpperCase();

    // Store verification code
    global.verificationCodes = global.verificationCodes || new Map();
    global.verificationCodes.set(verificationCode, {
      telegramId,
      telegramUsername: username,
      telegramFirstName: firstName,
      timestamp: Date.now(),
      verified: false
    });

    console.log(`Generated verification code ${verificationCode} for new user ${telegramId}`);

    return `🚀 Welcome to SolPay Bot, ${firstName || 'there'}!

To link your account:
1. Open SolPay app
2. Go to Settings → Telegram Integration
3. Enter this code: \`${verificationCode}\`

Once verified, you can use all bot features!

Use /help to see available commands.`;
  } catch (error) {
    console.error('Error in handleStart:', error);
    return `❌ Sorry, something went wrong. Please try again later.`;
  }
}

/**
 * Check if Telegram user is already linked to a SolPay account
 */
async function checkIfUserIsLinked(telegramId) {
  try {
    // In a real implementation, this would check your database
    // For now, we'll use a simple approach

    // Check if we have any stored verification codes for this user that were verified
    const verificationCodes = global.verificationCodes || new Map();

    for (const [code, data] of verificationCodes.entries()) {
      if (data.telegramId === telegramId && data.verified) {
        console.log(`User ${telegramId} is already verified`);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking if user is linked:', error);
    return false;
  }
}

/**
 * Handle /help command
 */
function handleHelp() {
  return `🤖 **SolPay Bot Commands**

/start - Link your account with verification code
/balance - Check crypto balances across all chains
/deposit - Get deposit addresses for all chains
/withdraw [amount] [token] [bank] - Withdraw crypto to bank
/rates - Current exchange rates
/pay [merchant_id] [amount] [token] - Pay merchants
/history - View transaction history
/help - Show this help message

💡 **Tips:**
• Link your account with /start first
• Check balances with /balance
• Get deposit addresses with /deposit
• View rates with /rates`;
}

/**
 * Handle /balance command
 */
function handleBalance(telegramId) {
  // Check if user is verified (in production, check database)
  const isVerified = Array.from(verificationCodes.values()).some(
    code => code.telegramId === telegramId && code.verified
  );

  if (!isVerified) {
    return '❌ Please link your account first using /start';
  }

  // In production, fetch real balances from your database
  return `💰 **Your Crypto Balances**

🔗 **SOLANA**
📍 \`9WzDXw...7Qx2\`
  • 0.5 SOL ($90.25)
  • 100 USDC ($100.00)

🔗 **ETHEREUM**
📍 \`0x1234...5678\`
  • 50 USDC ($50.00)

💵 **Total Portfolio Value: $240.25**
🇳🇬 **≈ ₦369,585**

Use /deposit to get deposit addresses.`;
}

/**
 * Handle /deposit command
 */
function handleDeposit(telegramId) {
  const isVerified = Array.from(verificationCodes.values()).some(
    code => code.telegramId === telegramId && code.verified
  );

  if (!isVerified) {
    return '❌ Please link your account first using /start';
  }

  return `📥 **Your Deposit Addresses**

🔗 **SOLANA**
📍 \`9WzDXwVvNMjb7QGzhTsN2M7Qx2\`
💰 Accepts: SOL, USDC

🔗 **ETHEREUM**
📍 \`******************************************\`
💰 Accepts: USDC only

🔗 **POLYGON**
📍 \`******************************************\`
💰 Accepts: USDC only

⚠️ **Important:**
• Only send supported tokens to each address
• Solana: SOL + USDC supported
• Other chains: USDC only
• Wrong tokens may be lost forever!`;
}

/**
 * Handle /rates command
 */
function handleRates() {
  return `💱 **Current Exchange Rates**

🟡 **SOL**: ₦277,860
🔵 **USDC**: ₦1,540
💵 **USD**: ₦1,540.00

⏰ Updated: ${new Date().toLocaleTimeString()}
📊 Rates update every 5 minutes`;
}

/**
 * Handle other commands
 */
function handleWithdraw(telegramId, text) {
  const isVerified = Array.from(verificationCodes.values()).some(
    code => code.telegramId === telegramId && code.verified
  );

  if (!isVerified) {
    return '❌ Please link your account first using /start';
  }

  return `🔄 Withdrawal request received!

Please complete the withdrawal in the SolPay app for security.

Example: /withdraw 100 USDC GTBank`;
}

function handlePay(telegramId, text) {
  const isVerified = Array.from(verificationCodes.values()).some(
    code => code.telegramId === telegramId && code.verified
  );

  if (!isVerified) {
    return '❌ Please link your account first using /start';
  }

  return `💳 Payment request received!

Please complete the payment in the SolPay app.

Example: /pay merchant_123 5000 USDC`;
}

function handleHistory(telegramId) {
  const isVerified = Array.from(verificationCodes.values()).some(
    code => code.telegramId === telegramId && code.verified
  );

  if (!isVerified) {
    return '❌ Please link your account first using /start';
  }

  return `📜 **Recent Transactions**

✅ **₦50,000**
   USDC → Mama Cass Restaurant
   Dec 29, 2024

✅ **₦25,000**
   SOL → GTBank Withdrawal
   Dec 28, 2024

View full history in the SolPay app.`;
}

function getUnknownCommandResponse(command) {
  return `❓ Unknown command: ${command}

Use /help to see all available commands.`;
}

/**
 * Send message to Telegram user
 */
async function sendTelegramMessage(chatId, text) {
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  
  if (!botToken) {
    console.error('TELEGRAM_BOT_TOKEN not configured');
    return;
  }

  try {
    console.log(`Sending message to chat ${chatId}: ${text.substring(0, 100)}...`);
    
    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: text,
        parse_mode: 'Markdown'
      }),
    });

    const result = await response.json();
    
    if (!result.ok) {
      console.error('Failed to send Telegram message:', result);
    } else {
      console.log('Message sent successfully');
    }
  } catch (error) {
    console.error('Error sending Telegram message:', error);
  }
}
