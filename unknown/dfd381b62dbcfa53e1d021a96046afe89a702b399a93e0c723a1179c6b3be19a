/**
 * Merchant QR Payment Service
 * Handles QR code generation, merchant registration, and payment processing
 */

import QRCode from 'qrcode';
import { supabase } from '@/integrations/supabase/client';
import { exchangeRateService } from './exchangeRateService';
import { offRampProcessingService } from './offRampProcessingService';

export interface MerchantAccount {
  id: string;
  userId: string;
  businessName: string;
  businessType?: string;
  businessDescription?: string;
  businessAddress?: string;
  contactPhone?: string;
  contactEmail?: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  qrCodeId: string;
  qrCodeUrl?: string;
  qrCodeData?: string;
  isActive: boolean;
  acceptsSol: boolean;
  acceptsUsdc: boolean;
  minPaymentAmount: number;
  maxPaymentAmount: number;
  createdAt: string;
  updatedAt: string;
}

export interface QRPayment {
  id: string;
  merchantId: string;
  customerId: string;
  amountCrypto: number;
  cryptoSymbol: string;
  amountNgn: number;
  exchangeRate: number;
  transactionHash?: string;
  blockchainNetwork: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  paymentMethod: 'qr_scan' | 'telegram_bot' | 'voice_command';
  merchantFee: number;
  customerFee: number;
  platformFee: number;
  paymentReference?: string;
  customerNote?: string;
  merchantNote?: string;
  createdAt: string;
  completedAt?: string;
}

export interface QRPaymentRequest {
  merchantId: string;
  customerId: string;
  amountNgn: number;
  cryptoSymbol: 'SOL' | 'USDC';
  paymentMethod?: 'qr_scan' | 'telegram_bot' | 'voice_command';
  customerNote?: string;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  transactionId?: string;
  error?: string;
  amountProcessed?: number;
}

class MerchantQRService {
  /**
   * Register a new merchant account
   */
  static async registerMerchant(merchantData: {
    userId: string;
    businessName: string;
    businessType?: string;
    businessDescription?: string;
    businessAddress?: string;
    contactPhone?: string;
    contactEmail?: string;
    bankName: string;
    bankCode?: string;
    accountNumber: string;
    accountName: string;
    acceptsSol?: boolean;
    acceptsUsdc?: boolean;
    minPaymentAmount?: number;
    maxPaymentAmount?: number;
  }): Promise<{ success: boolean; merchant?: MerchantAccount; error?: string }> {
    try {
      // First, check if user already has a merchant account
      const { data: existingMerchant, error: checkError } = await supabase
        .from('merchant_accounts')
        .select('*')
        .eq('user_id', merchantData.userId)
        .single();

      if (existingMerchant && !checkError) {
        console.log('⚠️ User already has a merchant account');
        return {
          success: false,
          error: 'You already have a merchant account registered. Please contact support if you need to update your details.'
        };
      }

      // Generate unique QR code ID
      const qrCodeId = `merchant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Create QR code data
      const qrCodeData = JSON.stringify({
        merchantId: qrCodeId,
        businessName: merchantData.businessName,
        type: 'solpay_merchant_payment'
      });
      
      // Generate QR code image
      const qrCodeUrl = await this.generateQRCode(qrCodeData);
      
      // Insert merchant account
      const { data, error } = await supabase
        .from('merchant_accounts')
        .insert({
          user_id: merchantData.userId,
          business_name: merchantData.businessName,
          business_type: merchantData.businessType,
          business_description: merchantData.businessDescription,
          business_address: merchantData.businessAddress,
          business_phone: merchantData.contactPhone,
          business_email: merchantData.contactEmail,
          bank_name: merchantData.bankName,
          account_number: merchantData.accountNumber,
          account_name: merchantData.accountName,
          bank_code: merchantData.bankCode || null,
          qr_code_id: qrCodeId,
          qr_code_data: qrCodeData,
          accepts_sol: merchantData.acceptsSol ?? true,
          accepts_usdc: merchantData.acceptsUsdc ?? true,
          min_payment_amount: merchantData.minPaymentAmount ?? 100,
          max_payment_amount: merchantData.maxPaymentAmount ?? 500000,
          is_active: true,
          is_verified: false,
          verification_status: 'pending',
          bank_verified: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error registering merchant:', error);

        // Handle specific constraint violations
        if (error.code === '23505') {
          if (error.message.includes('user_id_key')) {
            return { success: false, error: 'You already have a merchant account registered.' };
          }
          if (error.message.includes('qr_code_id')) {
            return { success: false, error: 'QR code generation failed. Please try again.' };
          }
        }

        return { success: false, error: error.message };
      }

      console.log('✅ Merchant registered successfully:', data.business_name);
      return { success: true, merchant: data };
    } catch (error) {
      console.error('Error in registerMerchant:', error);
      return { success: false, error: 'Failed to register merchant' };
    }
  }

  /**
   * Generate QR code image from data
   */
  private static async generateQRCode(data: string): Promise<string> {
    try {
      const qrCodeDataUrl = await QRCode.toDataURL(data, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      });
      
      return qrCodeDataUrl;
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw new Error('Failed to generate QR code');
    }
  }

  /**
   * Get merchant by QR code ID
   */
  static async getMerchantByQRId(qrCodeId: string): Promise<MerchantAccount | null> {
    try {
      const { data, error } = await supabase
        .from('merchant_accounts')
        .select('*')
        .eq('qr_code_id', qrCodeId)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        console.error('Merchant not found:', qrCodeId);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting merchant:', error);
      return null;
    }
  }

  /**
   * Get merchant accounts for a user
   */
  static async getUserMerchants(userId: string): Promise<MerchantAccount[]> {
    try {
      const { data, error } = await supabase
        .from('merchant_accounts')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting user merchants:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserMerchants:', error);
      return [];
    }
  }

  /**
   * Process QR payment
   */
  static async processQRPayment(request: QRPaymentRequest): Promise<PaymentResult> {
    try {
      console.log('🔄 Processing QR payment:', request);

      // 1. Get merchant details
      const merchant = await this.getMerchantByQRId(request.merchantId);
      if (!merchant) {
        return { success: false, error: 'Merchant not found' };
      }

      // 2. Validate payment amount
      if (request.amountNgn < merchant.minPaymentAmount || request.amountNgn > merchant.maxPaymentAmount) {
        return { 
          success: false, 
          error: `Payment amount must be between ₦${merchant.minPaymentAmount} and ₦${merchant.maxPaymentAmount}` 
        };
      }

      // 3. Check if merchant accepts the crypto
      if (request.cryptoSymbol === 'SOL' && !merchant.acceptsSol) {
        return { success: false, error: 'Merchant does not accept SOL payments' };
      }
      if (request.cryptoSymbol === 'USDC' && !merchant.acceptsUsdc) {
        return { success: false, error: 'Merchant does not accept USDC payments' };
      }

      // 4. Get exchange rate
      const exchangeRate = await exchangeRateService.getCryptoToNGNRate(request.cryptoSymbol);
      if (!exchangeRate || exchangeRate <= 0) {
        return { success: false, error: 'Unable to get exchange rate' };
      }

      const amountCrypto = request.amountNgn / exchangeRate;

      // 5. Calculate fees
      const merchantFee = request.amountNgn * 0.015; // 1.5%
      const customerFee = request.amountNgn * 0.005; // 0.5%
      const platformFee = merchantFee + customerFee;
      const merchantReceives = request.amountNgn - merchantFee;

      // 6. Create payment record
      const { data: payment, error: paymentError } = await supabase
        .from('qr_payments')
        .insert({
          merchant_id: merchant.id,
          customer_id: request.customerId,
          amount_crypto: amountCrypto,
          crypto_symbol: request.cryptoSymbol,
          amount_ngn: request.amountNgn,
          exchange_rate: exchangeRate,
          blockchain_network: request.cryptoSymbol === 'SOL' ? 'solana' : 'ethereum',
          status: 'processing',
          payment_method: request.paymentMethod || 'qr_scan',
          merchant_fee: merchantFee,
          customer_fee: customerFee,
          platform_fee: platformFee,
          customer_note: request.customerNote,
          payment_reference: `QR_${Date.now()}`
        })
        .select()
        .single();

      if (paymentError) {
        console.error('Error creating payment record:', paymentError);
        return { success: false, error: 'Failed to create payment record' };
      }

      // 7. Process the actual payment (send Naira to merchant)
      const offRampResult = await offRampProcessingService.processOffRamp({
        userId: request.customerId,
        cryptoAmount: amountCrypto,
        cryptoSymbol: request.cryptoSymbol,
        chain: request.cryptoSymbol === 'SOL' ? 'solana' : 'ethereum',
        bankAccount: {
          bankName: merchant.bankName,
          bankCode: this.getBankCode(merchant.bankName),
          accountNumber: merchant.accountNumber,
          accountName: merchant.accountName
        }
      });

      // 8. Update payment status
      if (offRampResult.success) {
        await supabase
          .from('qr_payments')
          .update({
            status: 'completed',
            transaction_hash: offRampResult.transactionId,
            completed_at: new Date().toISOString()
          })
          .eq('id', payment.id);

        console.log('✅ QR payment completed successfully');
        return {
          success: true,
          paymentId: payment.id,
          transactionId: offRampResult.transactionId,
          amountProcessed: merchantReceives
        };
      } else {
        await supabase
          .from('qr_payments')
          .update({ status: 'failed' })
          .eq('id', payment.id);

        return { success: false, error: offRampResult.message || 'Payment processing failed' };
      }
    } catch (error) {
      console.error('Error processing QR payment:', error);
      return { success: false, error: 'Payment processing failed' };
    }
  }

  /**
   * Get bank code from bank name
   */
  private static getBankCode(bankName: string): string {
    const bankCodes: Record<string, string> = {
      'Access Bank': '044',
      'Zenith Bank': '057',
      'GTBank': '058',
      'First Bank': '011',
      'UBA': '033',
      'Fidelity Bank': '070',
      'FCMB': '214',
      'Sterling Bank': '232',
      'Union Bank': '032',
      'Wema Bank': '035'
    };
    
    return bankCodes[bankName] || '044';
  }

  /**
   * Get payment history for merchant
   */
  static async getMerchantPayments(merchantId: string, limit: number = 50): Promise<QRPayment[]> {
    try {
      const { data, error } = await supabase
        .from('qr_payments')
        .select('*')
        .eq('merchant_id', merchantId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error getting merchant payments:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getMerchantPayments:', error);
      return [];
    }
  }

  /**
   * Get payment history for customer
   */
  static async getCustomerPayments(customerId: string, limit: number = 50): Promise<QRPayment[]> {
    try {
      const { data, error } = await supabase
        .from('qr_payments')
        .select(`
          *,
          merchant_accounts!inner(business_name, business_type)
        `)
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error getting customer payments:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getCustomerPayments:', error);
      return [];
    }
  }
}

export { MerchantQRService };
