/**
 * Merchant Payment Intents Page
 * 
 * Main page for merchants to manage their payment intents
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import PaymentIntentManagement from '@/components/merchant/PaymentIntentManagement';
import {
  ArrowLeft,
  CreditCard,
  TrendingUp,
  DollarSign,
  Users,
  BarChart3,
  Calendar,
  ExternalLink
} from 'lucide-react';

interface MerchantAccount {
  id: string;
  business_name: string;
  is_verified: boolean;
}

const MerchantPaymentIntents: React.FC = () => {
  const [merchant, setMerchant] = useState<MerchantAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total_intents: 0,
    successful_payments: 0,
    total_revenue: 0,
    pending_payments: 0
  });
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    loadMerchantAccount();
  }, [user]);

  const loadMerchantAccount = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('merchant_accounts')
        .select('id, business_name, is_verified')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error loading merchant account:', error);
        toast({
          title: "Error",
          description: "Failed to load merchant account. Please register as a merchant first.",
          variant: "destructive",
        });
        navigate('/merchant-registration');
        return;
      }

      setMerchant(data);
      loadStats(data.id);
    } catch (error) {
      console.error('Error loading merchant account:', error);
      toast({
        title: "Error",
        description: "Failed to load merchant account",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async (merchantId: string) => {
    try {
      // This would load actual stats from the database
      // For now, we'll use mock data
      setStats({
        total_intents: 24,
        successful_payments: 18,
        total_revenue: 12450.00,
        pending_payments: 3
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!merchant) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Merchant Account Required</h2>
              <p className="text-gray-600 mb-4">
                You need to register as a merchant to access payment intents.
              </p>
              <Button onClick={() => navigate('/merchant-registration')}>
                Register as Merchant
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/merchant-dashboard')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Payment Intents</h1>
                <p className="text-sm text-gray-600">{merchant.business_name}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!merchant.is_verified && (
                <Badge variant="outline" className="text-amber-600 border-amber-600">
                  Verification Pending
                </Badge>
              )}
              {merchant.is_verified && (
                <Badge className="bg-green-100 text-green-800">
                  Verified Merchant
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="intents" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="intents" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Payment Intents
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Payment Intents Tab */}
          <TabsContent value="intents" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Intents</CardTitle>
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total_intents}</div>
                  <p className="text-xs text-muted-foreground">
                    +12% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Successful Payments</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.successful_payments}</div>
                  <p className="text-xs text-muted-foreground">
                    {((stats.successful_payments / stats.total_intents) * 100).toFixed(1)}% success rate
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${stats.total_revenue.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +8% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.pending_payments}</div>
                  <p className="text-xs text-muted-foreground">
                    Awaiting confirmation
                  </p>
                </CardContent>
              </Card>
            </div>

            <PaymentIntentManagement merchantId={merchant.id} />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Volume</CardTitle>
                  <CardDescription>
                    Payment volume over the last 30 days
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <p>Analytics chart would go here</p>
                      <p className="text-sm">Integration with charting library needed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Payment Methods</CardTitle>
                  <CardDescription>
                    Breakdown by cryptocurrency
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">USDC</span>
                      </div>
                      <span className="text-sm font-medium">45%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span className="text-sm">SOL</span>
                      </div>
                      <span className="text-sm font-medium">35%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm">ETH</span>
                      </div>
                      <span className="text-sm font-medium">20%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest payment intent activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between py-2">
                    <div>
                      <p className="text-sm font-medium">Payment Intent Created</p>
                      <p className="text-xs text-gray-500">pi_1234567890 • $50.00</p>
                    </div>
                    <Badge variant="outline">2 hours ago</Badge>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <div>
                      <p className="text-sm font-medium">Payment Completed</p>
                      <p className="text-xs text-gray-500">pi_0987654321 • $25.00</p>
                    </div>
                    <Badge className="bg-green-100 text-green-800">5 hours ago</Badge>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <div>
                      <p className="text-sm font-medium">Payment Intent Created</p>
                      <p className="text-xs text-gray-500">pi_1122334455 • $100.00</p>
                    </div>
                    <Badge variant="outline">1 day ago</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Settings</CardTitle>
                <CardDescription>
                  Configure your payment intent defaults
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-2">Default Settings</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Default Currency</label>
                      <p className="text-sm text-gray-600">USD</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Accepted Cryptocurrencies</label>
                      <p className="text-sm text-gray-600">SOL, USDC, ETH</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Settlement Currency</label>
                      <p className="text-sm text-gray-600">Nigerian Naira (NGN)</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Webhook Settings</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Payment Intent Succeeded</span>
                      <Badge className="bg-green-100 text-green-800">Active</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Payment Intent Failed</span>
                      <Badge className="bg-green-100 text-green-800">Active</Badge>
                    </div>
                  </div>
                </div>

                <Button variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Configure Settings
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MerchantPaymentIntents;
