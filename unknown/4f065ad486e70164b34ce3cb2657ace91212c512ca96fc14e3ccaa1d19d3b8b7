
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CardProvider } from "@/types/card";
import { CheckCircle2, AlertCircle, CreditCard, ArrowRight } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface CardConfirmationProps {
  formData?: {
    fullName: string;
    email: string;
    phoneNumber: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    cardProvider: string;
  };
  onConfirm?: () => void;
  onEdit?: () => void;
  onClose?: () => void;
  isLoading?: boolean;
}

export default function CardConfirmation({
  formData = {
    fullName: "John Doe",
    email: "<EMAIL>",
    phoneNumber: "+****************",
    address: "123 Main St",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    country: "United States",
    cardProvider: CardProvider.VISA,
  },
  onConfirm = () => {},
  onEdit = () => {},
  onClose = () => {},
  isLoading = false,
}: CardConfirmationProps) {
  // Get the card provider name for display
  const getCardProviderName = (provider: string) => {
    switch (provider) {
      case CardProvider.VISA:
        return "Visa";
      case CardProvider.MASTERCARD:
        return "Mastercard";
      case CardProvider.AMEX:
        return "American Express";
      default:
        return "Unknown";
    }
  };

  const handleConfirm = () => {
    if (onConfirm) onConfirm();
  };

  const handleEdit = () => {
    if (onEdit) onEdit();
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Confirm Your Virtual Card Details</CardTitle>
        <CardDescription>
          Please review your information before we create your virtual card
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Important</AlertTitle>
          <AlertDescription>
            Once your card is created, some details cannot be changed. Please verify all information is correct.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium mb-2">Personal Information</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="font-medium">Full Name:</div>
              <div>{formData.fullName}</div>
              <div className="font-medium">Email:</div>
              <div>{formData.email}</div>
              <div className="font-medium">Phone Number:</div>
              <div>{formData.phoneNumber}</div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Billing Address</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="font-medium">Address:</div>
              <div>{formData.address}</div>
              <div className="font-medium">City:</div>
              <div>{formData.city}</div>
              <div className="font-medium">State/Province:</div>
              <div>{formData.state}</div>
              <div className="font-medium">Zip/Postal Code:</div>
              <div>{formData.zipCode}</div>
              <div className="font-medium">Country:</div>
              <div>{formData.country}</div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Card Details</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="font-medium">Card Provider:</div>
              <div className="flex items-center">
                <CreditCard className="h-4 w-4 mr-2" />
                {getCardProviderName(formData.cardProvider)}
              </div>
              <div className="font-medium">Card Type:</div>
              <div>Virtual Debit Card</div>
              <div className="font-medium">Initial Balance:</div>
              <div>$0.00 (You can add funds after card creation)</div>
            </div>
          </div>

          <div className="bg-muted p-4 rounded-lg">
            <h3 className="text-md font-medium mb-2 flex items-center">
              <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
              Identity Verification
            </h3>
            <p className="text-sm">
              Your identity has been successfully verified. Your virtual card will be issued to the name and address provided above.
            </p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleEdit} disabled={isLoading}>
          Edit Information
        </Button>
        <Button onClick={handleConfirm} disabled={isLoading} className="gap-2">
          {isLoading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-b-transparent rounded-full"></div>
              Creating Card...
            </>
          ) : (
            <>
              Create Virtual Card
              <ArrowRight className="h-4 w-4" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
