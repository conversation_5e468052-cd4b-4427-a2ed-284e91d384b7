/**
 * Merchant API Key Management Service
 * 
 * Handles creation, management, and authentication of merchant API keys
 * for the crypto payment gateway
 */

import { supabase } from '@/integrations/supabase/client';
import crypto from 'crypto';

export interface MerchantApiKey {
  id: string;
  merchant_id: string;
  key_id: string;
  key_type: 'live' | 'test';
  permissions: string[];
  is_active: boolean;
  last_used_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateApiKeyRequest {
  merchant_id: string;
  key_type: 'live' | 'test';
  permissions?: string[];
  description?: string;
}

export interface ApiKeyPair {
  publishable_key: string;
  secret_key: string;
  key_id: string;
}

class MerchantApiKeyService {
  
  /**
   * Generate a new API key pair for a merchant
   */
  static async createApiKey(request: CreateApiKeyRequest): Promise<{
    success: boolean;
    api_key_pair?: ApiKeyPair;
    error?: string;
  }> {
    try {
      // Check if merchant exists
      const { data: merchant, error: merchantError } = await supabase
        .from('merchant_accounts')
        .select('id, user_id')
        .eq('id', request.merchant_id)
        .single();

      if (merchantError || !merchant) {
        return { success: false, error: 'Merchant not found' };
      }

      // Check if API key of this type already exists
      const { data: existingKey } = await supabase
        .from('merchant_api_keys')
        .select('id')
        .eq('merchant_id', request.merchant_id)
        .eq('key_type', request.key_type)
        .eq('is_active', true)
        .single();

      if (existingKey) {
        return { 
          success: false, 
          error: `${request.key_type} API key already exists. Deactivate the existing key first.` 
        };
      }

      // Generate key pair
      const keyPair = this.generateKeyPair(request.key_type);
      
      // Hash the secret key for storage
      const hashedSecret = crypto.createHash('sha256').update(keyPair.secret_key).digest('hex');

      // Insert into database
      const { data, error } = await supabase
        .from('merchant_api_keys')
        .insert({
          merchant_id: request.merchant_id,
          key_id: keyPair.key_id,
          key_secret: hashedSecret,
          key_type: request.key_type,
          permissions: request.permissions || ['read', 'write'],
          is_active: true
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating API key:', error);
        return { success: false, error: 'Failed to create API key' };
      }

      return {
        success: true,
        api_key_pair: keyPair
      };

    } catch (error) {
      console.error('API key creation error:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * List all API keys for a merchant
   */
  static async listApiKeys(merchantId: string): Promise<{
    success: boolean;
    api_keys?: MerchantApiKey[];
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('merchant_api_keys')
        .select('*')
        .eq('merchant_id', merchantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error listing API keys:', error);
        return { success: false, error: 'Failed to retrieve API keys' };
      }

      // Don't return the actual secret keys
      const apiKeys = data.map(key => ({
        id: key.id,
        merchant_id: key.merchant_id,
        key_id: key.key_id,
        key_type: key.key_type,
        permissions: key.permissions,
        is_active: key.is_active,
        last_used_at: key.last_used_at,
        created_at: key.created_at,
        updated_at: key.updated_at
      }));

      return { success: true, api_keys: apiKeys };

    } catch (error) {
      console.error('Error listing API keys:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Deactivate an API key
   */
  static async deactivateApiKey(keyId: string, merchantId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const { error } = await supabase
        .from('merchant_api_keys')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('key_id', keyId)
        .eq('merchant_id', merchantId);

      if (error) {
        console.error('Error deactivating API key:', error);
        return { success: false, error: 'Failed to deactivate API key' };
      }

      return { success: true };

    } catch (error) {
      console.error('Error deactivating API key:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Regenerate an API key
   */
  static async regenerateApiKey(keyId: string, merchantId: string): Promise<{
    success: boolean;
    api_key_pair?: ApiKeyPair;
    error?: string;
  }> {
    try {
      // Get existing key info
      const { data: existingKey, error: fetchError } = await supabase
        .from('merchant_api_keys')
        .select('key_type')
        .eq('key_id', keyId)
        .eq('merchant_id', merchantId)
        .single();

      if (fetchError || !existingKey) {
        return { success: false, error: 'API key not found' };
      }

      // Generate new key pair
      const keyPair = this.generateKeyPair(existingKey.key_type);
      const hashedSecret = crypto.createHash('sha256').update(keyPair.secret_key).digest('hex');

      // Update the existing record
      const { error } = await supabase
        .from('merchant_api_keys')
        .update({
          key_id: keyPair.key_id,
          key_secret: hashedSecret,
          updated_at: new Date().toISOString()
        })
        .eq('key_id', keyId)
        .eq('merchant_id', merchantId);

      if (error) {
        console.error('Error regenerating API key:', error);
        return { success: false, error: 'Failed to regenerate API key' };
      }

      return {
        success: true,
        api_key_pair: keyPair
      };

    } catch (error) {
      console.error('Error regenerating API key:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Verify an API key for authentication
   */
  static async verifyApiKey(apiKey: string): Promise<{
    success: boolean;
    merchant?: any;
    key_data?: MerchantApiKey;
    error?: string;
  }> {
    try {
      // Extract key ID from the API key
      const keyId = apiKey.substring(0, apiKey.lastIndexOf('_'));
      
      // Hash the provided key
      const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

      // Verify against database
      const { data: keyData, error } = await supabase
        .from('merchant_api_keys')
        .select(`
          *,
          merchant:merchant_accounts(*)
        `)
        .eq('key_id', keyId)
        .eq('key_secret', hashedKey)
        .eq('is_active', true)
        .single();

      if (error || !keyData) {
        return { success: false, error: 'Invalid API key' };
      }

      // Update last used timestamp
      await supabase
        .from('merchant_api_keys')
        .update({ last_used_at: new Date().toISOString() })
        .eq('id', keyData.id);

      return {
        success: true,
        merchant: keyData.merchant,
        key_data: {
          id: keyData.id,
          merchant_id: keyData.merchant_id,
          key_id: keyData.key_id,
          key_type: keyData.key_type,
          permissions: keyData.permissions,
          is_active: keyData.is_active,
          last_used_at: keyData.last_used_at,
          created_at: keyData.created_at,
          updated_at: keyData.updated_at
        }
      };

    } catch (error) {
      console.error('Error verifying API key:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Generate a new API key pair
   */
  private static generateKeyPair(keyType: 'live' | 'test'): ApiKeyPair {
    const prefix = keyType === 'live' ? 'pk_live' : 'pk_test';
    const secretPrefix = keyType === 'live' ? 'sk_live' : 'sk_test';
    
    // Generate random strings
    const keyId = `${prefix}_${crypto.randomBytes(16).toString('hex')}`;
    const secretSuffix = crypto.randomBytes(24).toString('hex');
    
    return {
      publishable_key: keyId,
      secret_key: `${secretPrefix}_${secretSuffix}`,
      key_id: keyId
    };
  }

  /**
   * Get API key usage statistics
   */
  static async getApiKeyStats(merchantId: string, days: number = 30): Promise<{
    success: boolean;
    stats?: any;
    error?: string;
  }> {
    try {
      // This would integrate with your analytics/logging system
      // For now, return mock data
      const stats = {
        total_requests: 1250,
        successful_requests: 1180,
        failed_requests: 70,
        success_rate: 94.4,
        avg_response_time: 245,
        requests_by_day: [
          { date: '2024-01-01', requests: 45 },
          { date: '2024-01-02', requests: 52 },
          // ... more data
        ]
      };

      return { success: true, stats };

    } catch (error) {
      console.error('Error getting API key stats:', error);
      return { success: false, error: 'Internal server error' };
    }
  }
}

export { MerchantApiKeyService };
