/**
 * Payment Link Management Component
 * 
 * Interface for merchants to create and manage shareable payment links
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  Copy,
  ExternalLink,
  Edit,
  Trash2,
  QrCode,
  Share2,
  Eye,
  EyeOff,
  Calendar,
  Users,
  DollarSign,
  Link
} from 'lucide-react';

interface PaymentLinkManagementProps {
  merchantId: string;
}

interface PaymentLink {
  id: string;
  title: string;
  description?: string;
  amount?: number;
  currency: string;
  is_amount_fixed: boolean;
  min_amount?: number;
  max_amount?: number;
  accepted_cryptocurrencies: string[];
  is_active: boolean;
  expires_at?: string;
  max_uses?: number;
  current_uses: number;
  url_slug: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

const PaymentLinkManagement: React.FC<PaymentLinkManagementProps> = ({ merchantId }) => {
  const [paymentLinks, setPaymentLinks] = useState<PaymentLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newPaymentLink, setNewPaymentLink] = useState<PaymentLink | null>(null);
  const { toast } = useToast();

  // Form state for creating payment link
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: 0,
    currency: 'USD',
    is_amount_fixed: true,
    min_amount: 1,
    max_amount: 10000,
    accepted_cryptocurrencies: ['SOL', 'USDC'],
    expires_at: '',
    max_uses: 0,
    metadata: {}
  });

  useEffect(() => {
    loadPaymentLinks();
  }, [merchantId]);

  const loadPaymentLinks = async () => {
    setLoading(true);
    try {
      // This would call an API to list payment links
      // For now, we'll simulate with mock data
      const mockLinks: PaymentLink[] = [
        {
          id: 'plink_1234567890',
          title: 'Premium Subscription',
          description: 'Monthly premium subscription',
          amount: 29.99,
          currency: 'USD',
          is_amount_fixed: true,
          accepted_cryptocurrencies: ['SOL', 'USDC'],
          is_active: true,
          max_uses: 100,
          current_uses: 23,
          url_slug: 'premium-subscription-abc123',
          metadata: {},
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'plink_0987654321',
          title: 'Donation',
          description: 'Support our cause',
          currency: 'USD',
          is_amount_fixed: false,
          min_amount: 5,
          max_amount: 1000,
          accepted_cryptocurrencies: ['SOL', 'USDC', 'ETH'],
          is_active: true,
          current_uses: 45,
          url_slug: 'donation-def456',
          metadata: {},
          created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      setPaymentLinks(mockLinks);
    } catch (error) {
      console.error('Error loading payment links:', error);
      toast({
        title: "Error",
        description: "Failed to load payment links",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createPaymentLink = async () => {
    if (!formData.title.trim()) {
      toast({
        title: "Invalid Data",
        description: "Please provide a title for the payment link",
        variant: "destructive",
      });
      return;
    }

    if (formData.is_amount_fixed && (!formData.amount || formData.amount <= 0)) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid amount",
        variant: "destructive",
      });
      return;
    }

    setCreating(true);
    try {
      // This would call the payment gateway API to create a payment link
      // For now, we'll simulate the creation
      const newLink: PaymentLink = {
        id: `plink_${Date.now()}`,
        title: formData.title,
        description: formData.description,
        amount: formData.is_amount_fixed ? formData.amount : undefined,
        currency: formData.currency,
        is_amount_fixed: formData.is_amount_fixed,
        min_amount: !formData.is_amount_fixed ? formData.min_amount : undefined,
        max_amount: !formData.is_amount_fixed ? formData.max_amount : undefined,
        accepted_cryptocurrencies: formData.accepted_cryptocurrencies,
        is_active: true,
        expires_at: formData.expires_at || undefined,
        max_uses: formData.max_uses || undefined,
        current_uses: 0,
        url_slug: `${formData.title.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
        metadata: formData.metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      setNewPaymentLink(newLink);
      setPaymentLinks(prev => [newLink, ...prev]);
      setShowCreateDialog(false);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        amount: 0,
        currency: 'USD',
        is_amount_fixed: true,
        min_amount: 1,
        max_amount: 10000,
        accepted_cryptocurrencies: ['SOL', 'USDC'],
        expires_at: '',
        max_uses: 0,
        metadata: {}
      });

      toast({
        title: "Payment Link Created! 🎉",
        description: "Your payment link has been created successfully",
      });
    } catch (error) {
      console.error('Error creating payment link:', error);
      toast({
        title: "Error",
        description: "Failed to create payment link",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const togglePaymentLink = async (linkId: string, isActive: boolean) => {
    try {
      setPaymentLinks(prev => 
        prev.map(link => 
          link.id === linkId 
            ? { ...link, is_active: !isActive, updated_at: new Date().toISOString() }
            : link
        )
      );

      toast({
        title: "Payment Link Updated",
        description: `Payment link ${!isActive ? 'activated' : 'deactivated'} successfully`,
      });
    } catch (error) {
      console.error('Error updating payment link:', error);
      toast({
        title: "Error",
        description: "Failed to update payment link",
        variant: "destructive",
      });
    }
  };

  const deletePaymentLink = async (linkId: string) => {
    try {
      setPaymentLinks(prev => prev.filter(link => link.id !== linkId));
      toast({
        title: "Payment Link Deleted",
        description: "The payment link has been deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting payment link:', error);
      toast({
        title: "Error",
        description: "Failed to delete payment link",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied! 📋",
      description: `${label} copied to clipboard`,
    });
  };

  const getPaymentLinkUrl = (urlSlug: string) => {
    const baseUrl = process.env.VITE_APP_URL || 'https://yourplatform.com';
    return `${baseUrl}/link/${urlSlug}`;
  };

  const formatAmount = (amount: number | undefined, currency: string) => {
    if (!amount) return 'Variable';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Payment Links</h2>
          <p className="text-gray-600">Create shareable payment links for your customers</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Payment Link
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Payment Link</DialogTitle>
              <DialogDescription>
                Create a shareable link that customers can use to pay you with cryptocurrency
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Premium Subscription"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Monthly premium subscription with all features"
                    rows={3}
                  />
                </div>
              </div>

              {/* Amount Settings */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="fixed-amount"
                    checked={formData.is_amount_fixed}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_amount_fixed: checked }))}
                  />
                  <Label htmlFor="fixed-amount">Fixed amount</Label>
                </div>

                {formData.is_amount_fixed ? (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="amount">Amount *</Label>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.amount || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                        placeholder="29.99"
                      />
                    </div>
                    <div>
                      <Label htmlFor="currency">Currency</Label>
                      <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="NGN">NGN</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="min_amount">Min Amount</Label>
                      <Input
                        id="min_amount"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.min_amount || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, min_amount: parseFloat(e.target.value) || 0 }))}
                        placeholder="1.00"
                      />
                    </div>
                    <div>
                      <Label htmlFor="max_amount">Max Amount</Label>
                      <Input
                        id="max_amount"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.max_amount || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, max_amount: parseFloat(e.target.value) || 0 }))}
                        placeholder="1000.00"
                      />
                    </div>
                    <div>
                      <Label htmlFor="currency">Currency</Label>
                      <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="NGN">NGN</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>

              {/* Accepted Cryptocurrencies */}
              <div>
                <Label>Accepted Cryptocurrencies</Label>
                <div className="flex flex-wrap gap-3 mt-2">
                  {['SOL', 'USDC', 'ETH', 'BTC'].map((crypto) => (
                    <div key={crypto} className="flex items-center space-x-2">
                      <Checkbox
                        id={crypto}
                        checked={formData.accepted_cryptocurrencies.includes(crypto)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setFormData(prev => ({
                              ...prev,
                              accepted_cryptocurrencies: [...prev.accepted_cryptocurrencies, crypto]
                            }));
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              accepted_cryptocurrencies: prev.accepted_cryptocurrencies.filter(c => c !== crypto)
                            }));
                          }
                        }}
                      />
                      <Label htmlFor={crypto} className="text-sm font-medium">
                        {crypto}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Advanced Settings */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="expires_at">Expiry Date (Optional)</Label>
                  <Input
                    id="expires_at"
                    type="date"
                    value={formData.expires_at}
                    onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="max_uses">Max Uses (Optional)</Label>
                  <Input
                    id="max_uses"
                    type="number"
                    min="0"
                    value={formData.max_uses || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, max_uses: parseInt(e.target.value) || 0 }))}
                    placeholder="Leave empty for unlimited"
                  />
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={createPaymentLink} disabled={creating}>
                  {creating ? 'Creating...' : 'Create Payment Link'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* New Payment Link Display Dialog */}
      {newPaymentLink && (
        <Dialog open={!!newPaymentLink} onOpenChange={() => setNewPaymentLink(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Payment Link Created
              </DialogTitle>
              <DialogDescription>
                Your payment link is ready! Share it with your customers.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>Payment Link URL</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={getPaymentLinkUrl(newPaymentLink.url_slug)}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(getPaymentLinkUrl(newPaymentLink.url_slug), 'Payment link')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(getPaymentLinkUrl(newPaymentLink.url_slug), '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Title:</span> {newPaymentLink.title}
                </div>
                <div>
                  <span className="font-medium">Amount:</span> {formatAmount(newPaymentLink.amount, newPaymentLink.currency)}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Payment Links List */}
      {paymentLinks.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Link className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Links</h3>
            <p className="text-gray-600 text-center mb-4">
              Create your first payment link to start accepting payments
            </p>
            <Button onClick={() => setShowCreateDialog(true)}>
              Create Payment Link
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {paymentLinks.map((link) => (
            <Card key={link.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{link.title}</CardTitle>
                    <CardDescription>
                      {formatAmount(link.amount, link.currency)} • Created {formatDate(link.created_at)}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={link.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {link.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {link.description && (
                  <p className="text-sm text-gray-600">{link.description}</p>
                )}

                <div className="flex flex-wrap gap-2">
                  {link.accepted_cryptocurrencies.map((crypto) => (
                    <Badge key={crypto} variant="outline" className="text-xs">
                      {crypto}
                    </Badge>
                  ))}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Uses:</span> {link.current_uses}{link.max_uses ? `/${link.max_uses}` : ''}
                  </div>
                  {link.expires_at && (
                    <div>
                      <span className="font-medium">Expires:</span> {formatDate(link.expires_at)}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(getPaymentLinkUrl(link.url_slug), 'Payment link')}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(getPaymentLinkUrl(link.url_slug), '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => togglePaymentLink(link.id, link.is_active)}
                  >
                    {link.is_active ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                    {link.is_active ? 'Disable' : 'Enable'}
                  </Button>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Payment Link</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will permanently delete the payment link. This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={() => deletePaymentLink(link.id)}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default PaymentLinkManagement;
