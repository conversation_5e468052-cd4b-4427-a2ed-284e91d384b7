/**
 * Reliable Exchange Rate Service
 * 
 * Uses multiple APIs with proper error handling and caching
 * Ensures accurate pricing for merchant payments and off-ramp
 */

interface ExchangeRates {
  USDC: number;
  SOL: number;
  lastUpdated: number;
}

interface PriceAPI {
  name: string;
  url: string;
  parser: (data: any) => { USDC: number; SOL: number };
}

class ReliableExchangeRateService {
  private cachedRates: ExchangeRates | null = null;
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes
  private isUpdating = false;

  // Multiple reliable APIs
  private priceAPIs: PriceAPI[] = [
    {
      name: 'CoinGecko',
      url: 'https://api.coingecko.com/api/v3/simple/price?ids=usd-coin,solana&vs_currencies=ngn',
      parser: (data) => ({
        USDC: data['usd-coin']?.ngn || 0,
        SOL: data['solana']?.ngn || 0
      })
    },
    {
      name: 'CryptoCompare',
      url: 'https://min-api.cryptocompare.com/data/pricemulti?fsyms=USDC,SOL&tsyms=NGN',
      parser: (data) => ({
        USDC: data.USDC?.NGN || 0,
        SOL: data.SOL?.NGN || 0
      })
    },
    {
      name: 'Binance',
      url: 'https://api.binance.com/api/v3/ticker/price',
      parser: (data) => {
        // Binance returns array, need to find USDT and SOL prices
        const usdtNgn = 1540; // Approximate USDT to NGN (you can get this from another API)
        const usdcPrice = data.find((item: any) => item.symbol === 'USDCUSDT')?.price || 1;
        const solPrice = data.find((item: any) => item.symbol === 'SOLUSDT')?.price || 150;
        
        return {
          USDC: parseFloat(usdcPrice) * usdtNgn,
          SOL: parseFloat(solPrice) * usdtNgn
        };
      }
    }
  ];

  // Fallback rates (updated manually when needed)
  private fallbackRates = {
    USDC: 1540, // 1 USDC = 1540 NGN
    SOL: 230000, // 1 SOL = 230,000 NGN
    lastUpdated: Date.now()
  };

  /**
   * Get current exchange rates with caching
   */
  async getCurrentRates(): Promise<ExchangeRates> {
    // Return cached rates if still valid
    if (this.cachedRates && this.isCacheValid()) {
      console.log('📊 Using cached exchange rates');
      return this.cachedRates;
    }

    // If already updating, wait for it to complete
    if (this.isUpdating) {
      await this.waitForUpdate();
      return this.cachedRates || this.fallbackRates;
    }

    // Update rates
    return await this.updateRates();
  }

  /**
   * Force update rates from APIs
   */
  async updateRates(): Promise<ExchangeRates> {
    this.isUpdating = true;
    console.log('🔄 Updating exchange rates from APIs...');

    try {
      // Try each API until one works
      for (const api of this.priceAPIs) {
        try {
          console.log(`📡 Trying ${api.name} API...`);
          
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
          
          const response = await fetch(api.url, {
            signal: controller.signal,
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'SolPay/1.0'
            }
          });
          
          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          const rates = api.parser(data);

          // Validate rates
          if (rates.USDC > 0 && rates.SOL > 0) {
            this.cachedRates = {
              ...rates,
              lastUpdated: Date.now()
            };

            console.log(`✅ Successfully fetched rates from ${api.name}:`, {
              USDC: rates.USDC,
              SOL: rates.SOL
            });

            this.isUpdating = false;
            return this.cachedRates;
          } else {
            throw new Error('Invalid rates received');
          }

        } catch (error) {
          console.warn(`⚠️ ${api.name} API failed:`, error);
          continue; // Try next API
        }
      }

      // All APIs failed, use fallback
      console.warn('⚠️ All APIs failed, using fallback rates');
      this.cachedRates = { ...this.fallbackRates };
      this.isUpdating = false;
      return this.cachedRates;

    } catch (error) {
      console.error('❌ Critical error updating rates:', error);
      this.cachedRates = { ...this.fallbackRates };
      this.isUpdating = false;
      return this.cachedRates;
    }
  }

  /**
   * Get specific token rate
   */
  async getTokenRate(symbol: 'USDC' | 'SOL'): Promise<number> {
    const rates = await this.getCurrentRates();
    return rates[symbol];
  }

  /**
   * Convert NGN to crypto
   */
  async convertNgnToCrypto(ngnAmount: number, symbol: 'USDC' | 'SOL'): Promise<number> {
    const rate = await this.getTokenRate(symbol);
    return ngnAmount / rate;
  }

  /**
   * Convert crypto to NGN
   */
  async convertCryptoToNgn(cryptoAmount: number, symbol: 'USDC' | 'SOL'): Promise<number> {
    const rate = await this.getTokenRate(symbol);
    return cryptoAmount * rate;
  }

  /**
   * Get rate with age information
   */
  async getRateWithAge(symbol: 'USDC' | 'SOL'): Promise<{ rate: number; ageMinutes: number; isStale: boolean }> {
    const rates = await this.getCurrentRates();
    const ageMs = Date.now() - rates.lastUpdated;
    const ageMinutes = Math.floor(ageMs / (1000 * 60));
    const isStale = ageMs > this.cacheExpiry;

    return {
      rate: rates[symbol],
      ageMinutes,
      isStale
    };
  }

  /**
   * Check if cached rates are still valid
   */
  private isCacheValid(): boolean {
    if (!this.cachedRates) return false;
    return (Date.now() - this.cachedRates.lastUpdated) < this.cacheExpiry;
  }

  /**
   * Wait for ongoing update to complete
   */
  private async waitForUpdate(): Promise<void> {
    let attempts = 0;
    while (this.isUpdating && attempts < 50) { // Max 5 seconds
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
  }

  /**
   * Update fallback rates (for manual updates)
   */
  updateFallbackRates(rates: { USDC?: number; SOL?: number }): void {
    if (rates.USDC) this.fallbackRates.USDC = rates.USDC;
    if (rates.SOL) this.fallbackRates.SOL = rates.SOL;
    this.fallbackRates.lastUpdated = Date.now();
    
    console.log('📝 Updated fallback rates:', this.fallbackRates);
  }

  /**
   * Get health status
   */
  getHealthStatus(): {
    hasCachedRates: boolean;
    cacheAge: number;
    isStale: boolean;
    lastAPI: string;
  } {
    return {
      hasCachedRates: !!this.cachedRates,
      cacheAge: this.cachedRates ? Date.now() - this.cachedRates.lastUpdated : 0,
      isStale: !this.isCacheValid(),
      lastAPI: 'Multiple APIs'
    };
  }
}

// Export singleton instance
export const reliableExchangeRateService = new ReliableExchangeRateService();

// Initialize rates on startup
reliableExchangeRateService.getCurrentRates()
  .then(rates => {
    console.log('🚀 ReliableExchangeRateService initialized with rates:', rates);
  })
  .catch(error => {
    console.error('❌ Failed to initialize ReliableExchangeRateService:', error);
  });
