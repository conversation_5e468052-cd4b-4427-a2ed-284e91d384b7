/**
 * Payment Page
 * 
 * Customer-facing payment page for crypto payment intents
 * Accessible via /pay/:payment_intent_id
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import {
  CreditCard,
  Wallet,
  QrCode,
  Copy,
  ExternalLink,
  CheckCircle,
  Clock,
  Shield,
  Zap,
  ArrowRight,
  AlertCircle
} from 'lucide-react';
import { PaymentGatewayService, PaymentIntent } from '@/services/paymentGatewayService';

const PaymentPage: React.FC = () => {
  const { payment_intent_id } = useParams<{ payment_intent_id: string }>();
  const navigate = useNavigate();
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');
  const [walletAddress, setWalletAddress] = useState('');
  const [transactionHash, setTransactionHash] = useState('');
  const [paymentStep, setPaymentStep] = useState<'select' | 'pay' | 'confirm' | 'success'>('select');
  const { toast } = useToast();

  useEffect(() => {
    if (payment_intent_id) {
      loadPaymentIntent();
    }
  }, [payment_intent_id]);

  const loadPaymentIntent = async () => {
    if (!payment_intent_id) return;

    setLoading(true);
    try {
      const result = await PaymentGatewayService.retrievePaymentIntent(payment_intent_id);
      
      if (result.success && result.payment_intent) {
        setPaymentIntent(result.payment_intent);
        
        // Check if already paid
        if (result.payment_intent.status === 'succeeded') {
          setPaymentStep('success');
        }
      } else {
        toast({
          title: "Payment Not Found",
          description: "The payment link you're looking for doesn't exist or has expired.",
          variant: "destructive",
        });
        navigate('/');
      }
    } catch (error) {
      console.error('Error loading payment intent:', error);
      toast({
        title: "Error",
        description: "Failed to load payment information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCryptoSelection = (crypto: string) => {
    setSelectedCrypto(crypto);
    setPaymentStep('pay');
  };

  const handlePaymentConfirmation = async () => {
    if (!paymentIntent || !selectedCrypto || !walletAddress || !transactionHash) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setProcessing(true);
    try {
      const result = await PaymentGatewayService.confirmPaymentIntent(
        paymentIntent.id,
        selectedCrypto,
        walletAddress,
        transactionHash
      );

      if (result.success) {
        setPaymentStep('success');
        toast({
          title: "Payment Submitted! 🎉",
          description: "Your payment is being processed. You'll receive a confirmation shortly.",
        });

        // Redirect to success URL if provided
        if (paymentIntent.success_url) {
          setTimeout(() => {
            window.location.href = paymentIntent.success_url!;
          }, 3000);
        }
      } else {
        toast({
          title: "Payment Failed",
          description: result.error || "Failed to process payment",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error confirming payment:', error);
      toast({
        title: "Error",
        description: "Failed to process payment",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied! 📋",
      description: `${label} copied to clipboard`,
    });
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getCryptoIcon = (crypto: string) => {
    const icons = {
      'SOL': '◎',
      'USDC': '💵',
      'ETH': 'Ξ',
      'BTC': '₿'
    };
    return icons[crypto as keyof typeof icons] || '🪙';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!paymentIntent) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Payment Not Found</h2>
            <p className="text-gray-600">
              The payment link you're looking for doesn't exist or has expired.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Crypto Payment</h1>
            <p className="text-gray-600">Secure cryptocurrency payment powered by your platform</p>
          </div>

          {/* Payment Card */}
          <Card className="shadow-xl">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl">
                    {formatAmount(paymentIntent.amount, paymentIntent.currency)}
                  </CardTitle>
                  <CardDescription className="text-blue-100">
                    Payment ID: {paymentIntent.id}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-6 w-6" />
                  <span className="text-sm">Secure</span>
                </div>
              </div>
            </CardHeader>

            <CardContent className="p-6">
              {paymentStep === 'select' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Choose Payment Method</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {paymentIntent.accepted_cryptocurrencies.map((crypto) => (
                        <Button
                          key={crypto}
                          variant="outline"
                          className="h-20 flex flex-col items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                          onClick={() => handleCryptoSelection(crypto)}
                        >
                          <span className="text-2xl">{getCryptoIcon(crypto)}</span>
                          <span className="font-semibold">{crypto}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Zap className="h-4 w-4" />
                      <span>Fast and secure blockchain payments</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                      <Shield className="h-4 w-4" />
                      <span>Protected by advanced encryption</span>
                    </div>
                  </div>
                </div>
              )}

              {paymentStep === 'pay' && (
                <div className="space-y-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setPaymentStep('select')}
                    >
                      ← Back
                    </Button>
                    <h3 className="text-lg font-semibold">
                      Pay with {selectedCrypto} {getCryptoIcon(selectedCrypto)}
                    </h3>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-2">Payment Instructions</h4>
                    <ol className="text-sm text-blue-800 space-y-1">
                      <li>1. Send the exact amount to the wallet address below</li>
                      <li>2. Copy the transaction hash from your wallet</li>
                      <li>3. Paste it in the form and confirm payment</li>
                    </ol>
                  </div>

                  <div>
                    <Label>Wallet Address</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        value="RECIPIENT_WALLET_ADDRESS_PLACEHOLDER"
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard("RECIPIENT_WALLET_ADDRESS_PLACEHOLDER", 'Wallet address')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label>Amount to Send</Label>
                    <Input
                      value={`${paymentIntent.amount} ${selectedCrypto}`}
                      readOnly
                      className="font-mono text-sm bg-gray-50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="wallet_address">Your Wallet Address</Label>
                    <Input
                      id="wallet_address"
                      value={walletAddress}
                      onChange={(e) => setWalletAddress(e.target.value)}
                      placeholder="Enter your wallet address"
                      className="font-mono text-sm"
                    />
                  </div>

                  <div>
                    <Label htmlFor="tx_hash">Transaction Hash</Label>
                    <Input
                      id="tx_hash"
                      value={transactionHash}
                      onChange={(e) => setTransactionHash(e.target.value)}
                      placeholder="Paste transaction hash here"
                      className="font-mono text-sm"
                    />
                  </div>

                  <Button
                    onClick={handlePaymentConfirmation}
                    disabled={!walletAddress || !transactionHash || processing}
                    className="w-full"
                  >
                    {processing ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Processing Payment...
                      </>
                    ) : (
                      <>
                        Confirm Payment
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                </div>
              )}

              {paymentStep === 'success' && (
                <div className="text-center space-y-6">
                  <div className="flex justify-center">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h3>
                    <p className="text-gray-600">
                      Your payment has been received and is being processed. 
                      You'll receive a confirmation email shortly.
                    </p>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div className="text-sm text-green-800">
                      <div><strong>Amount:</strong> {formatAmount(paymentIntent.amount, paymentIntent.currency)}</div>
                      <div><strong>Payment ID:</strong> {paymentIntent.id}</div>
                      <div><strong>Status:</strong> Confirmed</div>
                    </div>
                  </div>

                  {paymentIntent.success_url && (
                    <Button
                      onClick={() => window.location.href = paymentIntent.success_url!}
                      className="w-full"
                    >
                      Continue
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-gray-500">
            <p>Powered by Your Crypto Payment Platform</p>
            <p>Secure • Fast • Global</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentPage;
