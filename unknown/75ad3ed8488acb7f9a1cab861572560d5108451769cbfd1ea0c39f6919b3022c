
/**
 * Subscription related types
 */

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELED = 'canceled',
  PAUSED = 'paused',
  PENDING = 'pending',
  FAILED = 'failed',
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  description?: string;
  features?: string[];
  isPopular?: boolean;
  billingCycle: 'monthly' | 'yearly' | 'quarterly';
}

export interface Subscription {
  id: string;
  name: string;
  logo: string;
  description?: string;
  category?: string;
  plans?: SubscriptionPlan[];
}

export interface PaymentMethod {
  type: 'card' | 'wallet' | 'bank';
  lastFour?: string;
  cardId?: string;
  walletId?: string;
  bankId?: string;
}

export interface UserSubscription {
  id: string;
  userId: string;
  subscription: Subscription;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  startDate: Date;
  nextPayment: Date;
  paymentMethod: PaymentMethod;
  isRecurring: boolean;
  canceledAt?: Date;
  externalId?: string; // ID from the external subscription service
  createdAt: Date;
  updatedAt: Date;
}

export interface SubscriptionCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
}

export interface SupportedService {
  id: string;
  name: string;
  logo: string;
  category: string;
  compatibilityStatus: 'full' | 'manual' | 'coming-soon';
  description?: string;
}
