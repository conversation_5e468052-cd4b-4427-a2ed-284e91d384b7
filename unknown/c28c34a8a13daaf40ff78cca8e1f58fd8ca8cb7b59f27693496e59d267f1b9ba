import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Wallet, Transaction, CryptoType, TransactionType, TokenBalance } from '../types/wallet';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';
import { getExchangeRate, CryptoCurrency } from '@/services/cryptoFiatService';

interface WalletContextType {
  wallets: Wallet[];
  transactions: Transaction[];
  isLoading: boolean;
  error: Error | null;
  refreshWallets: () => Promise<void>;
  depositFunds: (walletId: string, amount: number, source: string) => Promise<boolean>;
  getTotalBalance: () => number;
  reconcileWalletBalance: (walletId: string, tokenType: CryptoType, amountToDeduct: number) => Promise<boolean>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export function useWallet() {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
}

interface WalletProviderProps {
  children: ReactNode;
}

/**
 * Map our CryptoType to CryptoCurrency for exchange rate lookups
 */
function mapCryptoTypeToCurrency(type: CryptoType): CryptoCurrency {
  switch (type) {
    case CryptoType.SOL:
      return CryptoCurrency.SOL;
    case CryptoType.USDC:
      return CryptoCurrency.USDC;
    case CryptoType.BTC:
      return CryptoCurrency.BTC;
    case CryptoType.ETH:
      return CryptoCurrency.ETH;
    default:
      return CryptoCurrency.USDC;
  }
}

export function WalletProvider({ children }: WalletProviderProps) {
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();

  // Load wallets when user changes
  useEffect(() => {
    if (user) {
      refreshWallets();
    } else {
      setWallets([]);
      setTransactions([]);
    }
  }, [user]);

  // Function to check if a wallet address is a valid Solana address
  const isValidSolanaAddress = (address: string): boolean => {
    // Solana addresses are base58-encoded public keys
    // They are typically 32-44 characters long and don't contain special characters
    const solanaAddressRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
    return solanaAddressRegex.test(address);
  };

  // Function to refresh wallet data - ensures only one wallet is returned
  const refreshWallets = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      // Fetch wallets from Supabase
      const { data: walletsData, error: walletsError } = await supabase
        .from('wallets')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (walletsError) throw walletsError;

      // Create a default wallet if none exists
      if (!walletsData || walletsData.length === 0) {
        console.log("No wallet found, creating default wallet");
        const defaultWallet = await createDefaultWallet();
        if (defaultWallet) {
          // Fetch wallets again after creating default
          const { data: updatedWallets, error: updateError } = await supabase
            .from('wallets')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });

          if (updateError) throw updateError;

          if (updatedWallets && updatedWallets.length > 0) {
            // Process the updated wallet (just the first one)
            await processFetchedWallets([updatedWallets[0]]);
          }
        }
      } else {
        // Process only the first wallet to avoid duplicates
        console.log(`Found ${walletsData.length} wallets, using only the most recent one`);

        // If we have more than one wallet, cleanup duplicates
        if (walletsData.length > 1) {
          await cleanupDuplicateWallets(walletsData);
        }

        // Process only the primary wallet
        await processFetchedWallets([walletsData[0]]);
      }

      // Fetch transactions
      await fetchTransactions();

    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch wallet data'));
      toast({
        title: 'Error',
        description: 'Failed to load wallet data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to clean up duplicate wallets
  const cleanupDuplicateWallets = async (walletsData: any[]) => {
    if (!user || walletsData.length <= 1) return;

    console.log(`Cleaning up ${walletsData.length - 1} duplicate wallets for user ${user.id}`);

    // Keep only the most recent wallet
    const primaryWallet = walletsData[0];
    const duplicateWallets = walletsData.slice(1);

    // Delete duplicate wallets
    for (const wallet of duplicateWallets) {
      console.log(`Deleting duplicate wallet ${wallet.id}`);

      // First delete associated tokens
      const { error: tokensError } = await supabase
        .from('wallet_tokens')
        .delete()
        .eq('wallet_id', wallet.id);

      if (tokensError) {
        console.error(`Failed to delete tokens for wallet ${wallet.id}:`, tokensError);
        continue;
      }

      // Then delete transactions
      const { error: transactionsError } = await supabase
        .from('transactions')
        .delete()
        .eq('wallet_id', wallet.id);

      if (transactionsError) {
        console.error(`Failed to delete transactions for wallet ${wallet.id}:`, transactionsError);
        continue;
      }

      // Finally delete the wallet
      const { error: walletError } = await supabase
        .from('wallets')
        .delete()
        .eq('id', wallet.id);

      if (walletError) {
        console.error(`Failed to delete wallet ${wallet.id}:`, walletError);
      } else {
        console.log(`Successfully deleted duplicate wallet ${wallet.id}`);
      }
    }
  };

  // Helper function to process fetched wallets
  const processFetchedWallets = async (walletsData: any[]) => {
    const fetchedWallets: Wallet[] = [];

    for (const walletData of walletsData) {
      // Check if the wallet has a valid Solana address
      if (!isValidSolanaAddress(walletData.address)) {
        // Generate a new Solana address
        const { Keypair } = await import('@solana/web3.js');
        const keypair = Keypair.generate();
        const solanaAddress = keypair.publicKey.toString();

        // Update the wallet in the database
        await supabase
          .from('wallets')
          .update({ address: solanaAddress })
          .eq('id', walletData.id);

        // Update the local data
        walletData.address = solanaAddress;
      }

      // Fetch tokens for this wallet
      const { data: tokensData, error: tokensError } = await supabase
        .from('wallet_tokens')
        .select('*')
        .eq('wallet_id', walletData.id);

      if (tokensError) throw tokensError;

      // Try to fetch real balances from Helius API
      let realBalances = null;
      try {
        // Dynamically import to avoid issues if the file doesn't exist
        const { fetchWalletBalance } = await import('../services/heliusService');
        realBalances = await fetchWalletBalance(walletData.address);
        console.log('Real balances from Helius:', realBalances);

        // Update the database with real balances
        if (realBalances) {
          // Update SOL balance
          const solToken = tokensData.find(t => t.type === 'SOL');
          if (solToken) {
            await supabase
              .from('wallet_tokens')
              .update({ balance: realBalances.sol })
              .eq('id', solToken.id);

            // Update the local data
            solToken.balance = realBalances.sol;
          }

          // Update USDC balance
          const usdcToken = tokensData.find(t => t.type === 'USDC');
          if (usdcToken) {
            await supabase
              .from('wallet_tokens')
              .update({ balance: realBalances.usdc })
              .eq('id', usdcToken.id);

            // Update the local data
            usdcToken.balance = realBalances.usdc;
          }
        }
      } catch (error) {
        console.error('Error fetching real balances from Helius:', error);
        // Continue with database values if Helius fails
      }

      // Convert tokens data
      const tokens: TokenBalance[] = tokensData.map(token => {
        // Get dollar value based on exchange rate
        const exchangeRate = token.type === 'USDC' ? 1 :
          token.type === 'SOL' ? 60.25 :
          token.type === 'BTC' ? 63000 :
          token.type === 'ETH' ? 3500 : 1;

        const dollarValue = Number(token.balance) * exchangeRate;

        return {
          type: token.type as CryptoType,
          balance: Number(token.balance),
          dollarValue
        };
      });

      // Calculate total dollar value
      const totalDollarValue = tokens.reduce((sum, token) => sum + token.dollarValue, 0);

      fetchedWallets.push({
        id: walletData.id,
        name: walletData.name,
        address: walletData.address,
        network: walletData.network as 'solana' | 'ethereum' | 'bitcoin',
        tokens,
        totalDollarValue,
        isDefault: walletData.is_default,
        createdAt: new Date(walletData.created_at),
        updatedAt: walletData.updated_at ? new Date(walletData.updated_at) : undefined
      });
    }

    setWallets(fetchedWallets);
  };

  // Function to create a default wallet for new users
  const createDefaultWallet = async () => {
    try {
      if (!user) return null;

      // Import the Solana web3.js library
      const { Keypair } = await import('@solana/web3.js');

      // Check if a wallet already exists for this user
      const { data: existingWallets, error: checkError } = await supabase
        .from('wallets')
        .select('*')
        .eq('user_id', user.id);

      if (checkError) throw checkError;

      // Only create a wallet if none exists
      if (!existingWallets || existingWallets.length === 0) {
        // Generate a new Solana keypair
        const keypair = Keypair.generate();

        // Get the public key (wallet address)
        const solanaAddress = keypair.publicKey.toString();

        // In a real application, you would securely store the private key
        // For this demo, we're only storing the public key (wallet address)
        // The private key would be needed to sign transactions

        // Insert the wallet
        const { data: wallet, error: walletError } = await supabase
          .from('wallets')
          .insert({
            name: 'Main Wallet',
            address: solanaAddress,
            network: 'solana',
            is_default: true,
            user_id: user.id
          })
          .select()
          .single();

        if (walletError) throw walletError;

        // Add default tokens to the wallet
        const tokenPromises = [
          supabase.from('wallet_tokens').insert({
            wallet_id: wallet.id,
            type: 'SOL',
            balance: 0
          }),
          supabase.from('wallet_tokens').insert({
            wallet_id: wallet.id,
            type: 'USDC',
            balance: 0
          })
        ];

        await Promise.all(tokenPromises);

        toast({
          title: 'Wallet Created',
          description: 'Your Solana wallet has been created successfully.',
        });

        return wallet;
      }

      return existingWallets[0];
    } catch (err) {
      console.error('Failed to create default wallet:', err);
      toast({
        title: 'Error',
        description: 'Failed to create default wallet. Please try again.',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Function to fetch transactions
  const fetchTransactions = async () => {
    if (!user) return;

    // Get the wallet IDs for the current user
    const { data: userWallets, error: walletsError } = await supabase
      .from('wallets')
      .select('id')
      .eq('user_id', user.id);

    if (walletsError) throw walletsError;

    if (!userWallets || userWallets.length === 0) {
      setTransactions([]);
      return;
    }

    // Get the wallet IDs
    const walletIds = userWallets.map(wallet => wallet.id);

    // Fetch transactions only for the user's wallets
    const { data: transactionsData, error: transactionsError } = await supabase
      .from('transactions')
      .select('*')
      .in('wallet_id', walletIds)
      .order('date', { ascending: false });

    if (transactionsError) throw transactionsError;

    // Convert to our Transaction type
    const allTransactions: Transaction[] = transactionsData.map(tx => ({
      id: tx.id,
      type: tx.type as TransactionType,
      amount: Number(tx.amount),
      walletId: tx.wallet_id,
      tokenType: tx.token_type as CryptoType,
      date: new Date(tx.date),
      status: tx.status as 'pending' | 'completed' | 'failed' | 'refunded',
      source: tx.source,
      destination: tx.destination,
      fee: tx.fee ? Number(tx.fee) : undefined,
      description: tx.description,
      dollarValue: tx.dollar_value ? Number(tx.dollar_value) : undefined,
      hash: tx.hash,
      cardId: tx.card_id
    }));

    setTransactions(allTransactions);
  };

  // Function to deposit funds
  const depositFunds = async (walletId: string, amount: number, source: string): Promise<boolean> => {
    if (!user) return false;

    try {
      setIsLoading(true);

      // Find the wallet
      const wallet = wallets.find(w => w.id === walletId);
      if (!wallet || wallet.tokens.length === 0) {
        throw new Error('Wallet not found or has no tokens');
      }

      // Use the first token type in the wallet for deposit
      const tokenType = wallet.tokens[0].type;

      // Get the current dollar value of the deposit
      const cryptoCurrency = mapCryptoTypeToCurrency(tokenType);
      const exchangeRate = await getExchangeRate(cryptoCurrency);
      const dollarValue = tokenType === CryptoType.USDC ? amount : amount * exchangeRate;

      // Insert transaction
      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .insert({
          wallet_id: walletId,
          type: 'deposit',
          amount,
          token_type: tokenType,
          date: new Date().toISOString(),
          status: 'completed',
          source,
          dollar_value: dollarValue,
          hash: `0x${Math.random().toString(16).substring(2, 42)}`
        })
        .select()
        .single();

      if (transactionError) throw transactionError;

      // Update token balance
      const { data: token, error: tokenError } = await supabase
        .from('wallet_tokens')
        .select('*')
        .eq('wallet_id', walletId)
        .eq('type', tokenType)
        .single();

      if (tokenError) throw tokenError;

      const newBalance = Number(token.balance) + amount;

      const { error: updateError } = await supabase
        .from('wallet_tokens')
        .update({ balance: newBalance, updated_at: new Date().toISOString() })
        .eq('id', token.id);

      if (updateError) throw updateError;

      // Update local state
      setWallets(prevWallets =>
        prevWallets.map(wallet =>
          wallet.id === walletId
            ? {
                ...wallet,
                tokens: wallet.tokens.map(token =>
                  token.type === tokenType
                    ? { ...token, balance: token.balance + amount, dollarValue: token.dollarValue + dollarValue }
                    : token
                ),
                totalDollarValue: wallet.totalDollarValue + dollarValue
              }
            : wallet
        )
      );

      // Add new transaction to the list
      const newTransaction: Transaction = {
        id: transaction.id,
        type: TransactionType.DEPOSIT,
        amount,
        tokenType,
        walletId,
        date: new Date(transaction.date),
        status: 'completed',
        source,
        dollarValue,
        hash: transaction.hash
      };

      setTransactions(prevTransactions => [newTransaction, ...prevTransactions]);

      toast({
        title: 'Deposit Successful',
        description: `Successfully deposited ${amount} to your wallet.`,
      });

      return true;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to deposit funds'));
      toast({
        title: 'Deposit Failed',
        description: 'Failed to deposit funds. Please try again.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate total balance across all wallets
  const getTotalBalance = (): number => {
    return wallets.reduce((total, wallet) => total + wallet.totalDollarValue, 0);
  };

  // Function to reconcile wallet balance with card balance
  // This is used to fix the issue where wallet balance wasn't deducted when funding a card
  const reconcileWalletBalance = async (walletId: string, tokenType: CryptoType, amountToDeduct: number): Promise<boolean> => {
    if (!user) return false;

    try {
      setIsLoading(true);

      // Get the current token balance
      const { data: tokenData, error: tokenError } = await supabase
        .from('wallet_tokens')
        .select('*')
        .eq('wallet_id', walletId)
        .eq('type', tokenType)
        .single();

      if (tokenError) {
        console.error('Error fetching token balance:', tokenError);
        throw new Error('Failed to fetch token balance');
      }

      // Calculate new balance
      const newBalance = Math.max(0, parseFloat(tokenData.balance) - amountToDeduct);

      // Update the wallet token balance
      const { error: updateError } = await supabase
        .from('wallet_tokens')
        .update({ balance: newBalance })
        .eq('id', tokenData.id);

      if (updateError) {
        console.error('Error updating wallet balance:', updateError);
        throw new Error('Failed to update wallet balance');
      }

      // Create a transaction record for the wallet
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          wallet_id: walletId,
          type: 'transfer',
          amount: amountToDeduct,
          token_type: tokenType,
          date: new Date().toISOString(),
          status: 'completed',
          destination: 'Virtual Card',
          description: 'Balance reconciliation - Transfer to virtual card'
        });

      if (transactionError) {
        console.error('Error creating wallet transaction:', transactionError);
        // Don't throw here, we've already updated the balance
      }

      // Refresh wallets to update UI
      await refreshWallets();

      toast({
        title: 'Balance Reconciled',
        description: `Your wallet balance has been updated to reflect previous card funding.`,
      });

      return true;
    } catch (err) {
      console.error('Error reconciling wallet balance:', err);
      toast({
        title: 'Error',
        description: 'Failed to reconcile wallet balance. Please try again.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    wallets,
    transactions,
    isLoading,
    error,
    refreshWallets,
    depositFunds,
    getTotalBalance,
    reconcileWalletBalance,
  };

  return <WalletContext.Provider value={value}>{children}</WalletContext.Provider>;
}
