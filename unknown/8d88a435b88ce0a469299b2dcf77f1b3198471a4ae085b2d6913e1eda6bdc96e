/**
 * Telegram Bot Setup API
 * Sets up webhook and bot commands
 */

// This is a simple client-side setup function
// In production, this should be done server-side

async function setupTelegramBot(botToken, webhookUrl) {
  try {
    console.log('Setting up Telegram bot...');
    
    // Set webhook
    const webhookResponse = await fetch(`https://api.telegram.org/bot${botToken}/setWebhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message']
      }),
    });

    const webhookResult = await webhookResponse.json();
    
    if (!webhookResult.ok) {
      throw new Error(`Failed to set webhook: ${webhookResult.description}`);
    }

    // Set bot commands
    const commands = [
      { command: 'start', description: 'Start using the bot and link your account' },
      { command: 'balance', description: 'Check your crypto balances' },
      { command: 'deposit', description: 'Get deposit addresses' },
      { command: 'withdraw', description: 'Withdraw crypto to bank' },
      { command: 'rates', description: 'Get current exchange rates' },
      { command: 'pay', description: 'Pay merchant via QR' },
      { command: 'history', description: 'View transaction history' },
      { command: 'help', description: 'Show all commands' }
    ];

    const commandsResponse = await fetch(`https://api.telegram.org/bot${botToken}/setMyCommands`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        commands: commands
      }),
    });

    const commandsResult = await commandsResponse.json();
    
    if (!commandsResult.ok) {
      throw new Error(`Failed to set commands: ${commandsResult.description}`);
    }

    return { 
      success: true, 
      webhook: webhookResult,
      commands: commandsResult
    };
  } catch (error) {
    console.error('Bot setup error:', error);
    throw error;
  }
}

// Export for use in the component
window.setupTelegramBot = setupTelegramBot;
