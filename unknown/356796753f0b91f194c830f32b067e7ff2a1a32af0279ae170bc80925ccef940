/**
 * Webhook Management Component
 * 
 * Interface for merchants to manage their webhook endpoints
 * and view delivery logs
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  Copy,
  Edit,
  Trash2,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  Shield,
  Activity
} from 'lucide-react';
import { WebhookService, WebhookEndpoint, WebhookDelivery } from '@/services/webhookService';

interface WebhookManagementProps {
  merchantId: string;
}

const WebhookManagement: React.FC<WebhookManagementProps> = ({ merchantId }) => {
  const [webhookEndpoints, setWebhookEndpoints] = useState<WebhookEndpoint[]>([]);
  const [webhookDeliveries, setWebhookDeliveries] = useState<WebhookDelivery[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const { toast } = useToast();

  // Form state for creating webhook endpoint
  const [formData, setFormData] = useState({
    url: '',
    events: ['payment_intent.succeeded', 'payment_intent.payment_failed']
  });

  const availableEvents = [
    { id: 'payment_intent.succeeded', label: 'Payment Intent Succeeded', description: 'Sent when a payment is completed successfully' },
    { id: 'payment_intent.payment_failed', label: 'Payment Intent Failed', description: 'Sent when a payment fails' },
    { id: 'payment_intent.created', label: 'Payment Intent Created', description: 'Sent when a new payment intent is created' },
    { id: 'payment_intent.canceled', label: 'Payment Intent Canceled', description: 'Sent when a payment intent is canceled' }
  ];

  useEffect(() => {
    loadWebhookData();
  }, [merchantId]);

  const loadWebhookData = async () => {
    setLoading(true);
    try {
      const [endpointsResult, deliveriesResult] = await Promise.all([
        WebhookService.listWebhookEndpoints(merchantId),
        WebhookService.getWebhookDeliveries(merchantId, 50)
      ]);

      if (endpointsResult.success && endpointsResult.webhook_endpoints) {
        setWebhookEndpoints(endpointsResult.webhook_endpoints);
      }

      if (deliveriesResult.success && deliveriesResult.deliveries) {
        setWebhookDeliveries(deliveriesResult.deliveries);
      }
    } catch (error) {
      console.error('Error loading webhook data:', error);
      toast({
        title: "Error",
        description: "Failed to load webhook data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createWebhookEndpoint = async () => {
    if (!formData.url || formData.events.length === 0) {
      toast({
        title: "Invalid Data",
        description: "Please provide a URL and select at least one event",
        variant: "destructive",
      });
      return;
    }

    setCreating(true);
    try {
      const result = await WebhookService.createWebhookEndpoint(
        merchantId,
        formData.url,
        formData.events
      );

      if (result.success) {
        await loadWebhookData();
        setShowCreateDialog(false);
        setFormData({ url: '', events: ['payment_intent.succeeded', 'payment_intent.payment_failed'] });
        
        toast({
          title: "Webhook Created! 🎉",
          description: "Your webhook endpoint has been created successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create webhook endpoint",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating webhook endpoint:', error);
      toast({
        title: "Error",
        description: "Failed to create webhook endpoint",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const deleteWebhookEndpoint = async (endpointId: string) => {
    try {
      const result = await WebhookService.deleteWebhookEndpoint(endpointId, merchantId);
      if (result.success) {
        await loadWebhookData();
        toast({
          title: "Webhook Deleted",
          description: "The webhook endpoint has been deleted successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete webhook endpoint",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error deleting webhook endpoint:', error);
      toast({
        title: "Error",
        description: "Failed to delete webhook endpoint",
        variant: "destructive",
      });
    }
  };

  const toggleWebhookEndpoint = async (endpointId: string, isActive: boolean) => {
    try {
      const result = await WebhookService.updateWebhookEndpoint(
        endpointId,
        merchantId,
        { is_active: !isActive }
      );

      if (result.success) {
        await loadWebhookData();
        toast({
          title: "Webhook Updated",
          description: `Webhook endpoint ${!isActive ? 'enabled' : 'disabled'} successfully`,
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update webhook endpoint",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error updating webhook endpoint:', error);
      toast({
        title: "Error",
        description: "Failed to update webhook endpoint",
        variant: "destructive",
      });
    }
  };

  const retryWebhookDelivery = async (deliveryId: string) => {
    try {
      const result = await WebhookService.retryWebhookDelivery(deliveryId, merchantId);
      if (result.success) {
        await loadWebhookData();
        toast({
          title: "Webhook Retried",
          description: "The webhook delivery has been queued for retry",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to retry webhook delivery",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error retrying webhook delivery:', error);
      toast({
        title: "Error",
        description: "Failed to retry webhook delivery",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied! 📋",
      description: `${label} copied to clipboard`,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Webhooks</h2>
          <p className="text-gray-600">Receive real-time notifications about payment events</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Webhook
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Webhook Endpoint</DialogTitle>
              <DialogDescription>
                Add a webhook endpoint to receive real-time notifications about payment events
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div>
                <Label htmlFor="webhook_url">Endpoint URL</Label>
                <Input
                  id="webhook_url"
                  type="url"
                  value={formData.url}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="https://yoursite.com/webhooks"
                />
              </div>

              <div>
                <Label>Events to Listen For</Label>
                <div className="space-y-3 mt-2">
                  {availableEvents.map((event) => (
                    <div key={event.id} className="flex items-start space-x-3">
                      <Checkbox
                        id={event.id}
                        checked={formData.events.includes(event.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setFormData(prev => ({
                              ...prev,
                              events: [...prev.events, event.id]
                            }));
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              events: prev.events.filter(e => e !== event.id)
                            }));
                          }
                        }}
                      />
                      <div className="grid gap-1.5 leading-none">
                        <Label htmlFor={event.id} className="text-sm font-medium">
                          {event.label}
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          {event.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={createWebhookEndpoint} disabled={creating}>
                  {creating ? 'Creating...' : 'Create Webhook'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="endpoints" className="w-full">
        <TabsList>
          <TabsTrigger value="endpoints" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Endpoints
          </TabsTrigger>
          <TabsTrigger value="deliveries" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Delivery Logs
          </TabsTrigger>
        </TabsList>

        <TabsContent value="endpoints" className="space-y-4">
          {webhookEndpoints.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Globe className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Webhook Endpoints</h3>
                <p className="text-gray-600 text-center mb-4">
                  Create your first webhook endpoint to receive real-time payment notifications
                </p>
                <Button onClick={() => setShowCreateDialog(true)}>
                  Add Webhook Endpoint
                </Button>
              </CardContent>
            </Card>
          ) : (
            webhookEndpoints.map((endpoint) => (
              <Card key={endpoint.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{endpoint.url}</CardTitle>
                      <CardDescription>
                        Created {formatDate(endpoint.created_at)} • {endpoint.events.length} events
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={endpoint.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {endpoint.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Events</Label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {endpoint.events.map((event) => (
                        <Badge key={event} variant="outline" className="text-xs">
                          {event}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Webhook Secret</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        value={`whsec_${endpoint.secret.substring(0, 20)}...`}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(endpoint.secret, 'Webhook secret')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 pt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => toggleWebhookEndpoint(endpoint.id, endpoint.is_active)}
                    >
                      {endpoint.is_active ? 'Disable' : 'Enable'}
                    </Button>
                    
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Webhook Endpoint</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will permanently delete the webhook endpoint. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction 
                            onClick={() => deleteWebhookEndpoint(endpoint.id)}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="deliveries" className="space-y-4">
          {webhookDeliveries.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Activity className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Webhook Deliveries</h3>
                <p className="text-gray-600 text-center">
                  Webhook delivery logs will appear here once you start receiving events
                </p>
              </CardContent>
            </Card>
          ) : (
            webhookDeliveries.map((delivery) => (
              <Card key={delivery.id}>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(delivery.status)}>
                          {getStatusIcon(delivery.status)}
                          <span className="ml-1">{delivery.status}</span>
                        </Badge>
                        <span className="text-sm font-medium">{delivery.event_type}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {formatDate(delivery.created_at)} • Attempt {delivery.attempt_count}/{delivery.max_attempts}
                      </p>
                      {delivery.http_status_code && (
                        <p className="text-xs text-gray-500 mt-1">
                          HTTP {delivery.http_status_code}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {delivery.status === 'failed' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => retryWebhookDelivery(delivery.id)}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Retry
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WebhookManagement;
