/**
 * Features Showcase Page
 * Displays all the new revolutionary features
 */

import React from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';
import { 
  QrCode, 
  Store, 
  MessageCircle, 
  Mic, 
  Zap,
  ArrowRight,
  CheckCircle,
  Star,
  Sparkles
} from 'lucide-react';

const Features: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      id: 'qr-scanner',
      icon: QrCode,
      title: 'QR Scanner',
      subtitle: 'Pay merchants instantly',
      description: 'Scan QR codes in stores to pay with crypto and merchants receive Naira instantly. No cards needed!',
      color: 'from-purple-500 to-indigo-600',
      benefits: [
        'Instant payments to merchants',
        'No physical cards required',
        'Works in any store with QR code',
        'Lower fees than traditional POS'
      ],
      status: 'NEW',
      route: '/qr-scanner'
    },
    {
      id: 'merchant-register',
      icon: Store,
      title: 'Merchant Registration',
      subtitle: 'Accept crypto payments',
      description: 'Register your business to accept crypto payments. Get a QR code and start receiving payments instantly.',
      color: 'from-orange-500 to-yellow-600',
      benefits: [
        'Accept SOL and USDC payments',
        'Instant Naira settlement',
        'Only 1.5% transaction fee',
        'No hardware required'
      ],
      status: 'NEW',
      route: '/merchant-register'
    },
    {
      id: 'telegram-bot',
      icon: MessageCircle,
      title: 'Telegram Bot',
      subtitle: 'Control via chat',
      description: 'Link your Telegram account to control your crypto operations through simple chat commands.',
      color: 'from-blue-500 to-cyan-600',
      benefits: [
        'Check balances via chat',
        'Withdraw with text commands',
        'Pay merchants remotely',
        'Get real-time notifications'
      ],
      status: 'NEW',
      route: '/telegram'
    },
    {
      id: 'voice-commands',
      icon: Mic,
      title: 'Voice Commands',
      subtitle: 'Speak to control',
      description: 'Control your crypto with voice commands in English, Yoruba, Hausa, and Igbo languages.',
      color: 'from-pink-500 to-red-600',
      benefits: [
        'Multi-language support',
        'Hands-free operation',
        'Natural language processing',
        'Voice feedback responses'
      ],
      status: 'NEW',
      route: '/voice-commands'
    }
  ];

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-8 w-8 text-yellow-500" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Revolutionary Features
            </h1>
            <Sparkles className="h-8 w-8 text-yellow-500" />
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the future of crypto payments with our groundbreaking features that make crypto accessible to everyone in Nigeria.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {features.map((feature) => {
            const IconComponent = feature.icon;
            
            return (
              <Card key={feature.id} className="relative overflow-hidden group hover:shadow-2xl transition-all duration-300 border-2 hover:border-purple-200">
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
                
                {/* Status Badge */}
                <div className="absolute top-4 right-4">
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    <Star className="h-3 w-3 mr-1" />
                    {feature.status}
                  </Badge>
                </div>

                <CardHeader className="relative">
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${feature.color} text-white`}>
                      <IconComponent className="h-8 w-8" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl font-bold">{feature.title}</CardTitle>
                      <p className="text-gray-600 font-medium">{feature.subtitle}</p>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 text-lg leading-relaxed">
                    {feature.description}
                  </p>
                </CardHeader>

                <CardContent className="relative space-y-6">
                  {/* Benefits */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Zap className="h-4 w-4 text-yellow-500" />
                      Key Benefits
                    </h4>
                    <ul className="space-y-2">
                      {feature.benefits.map((benefit, index) => (
                        <li key={index} className="flex items-center gap-3 text-gray-700">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Button */}
                  <Button 
                    onClick={() => navigate(feature.route)}
                    className={`w-full bg-gradient-to-r ${feature.color} hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-white font-semibold py-3`}
                  >
                    Try {feature.title}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0">
          <CardContent className="text-center py-12">
            <h2 className="text-3xl font-bold mb-4">Ready to Experience the Future?</h2>
            <p className="text-xl mb-8 text-purple-100">
              Join thousands of Nigerians already using these revolutionary crypto features
            </p>
            
            <div className="flex flex-wrap justify-center gap-4">
              <Button 
                onClick={() => navigate('/qr-scanner')}
                variant="secondary"
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-100 font-semibold"
              >
                <QrCode className="h-5 w-5 mr-2" />
                Start Scanning QR Codes
              </Button>
              
              <Button 
                onClick={() => navigate('/merchant-register')}
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-purple-600 font-semibold"
              >
                <Store className="h-5 w-5 mr-2" />
                Register as Merchant
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <Card className="text-center p-6">
            <div className="text-3xl font-bold text-purple-600 mb-2">4</div>
            <div className="text-gray-600">New Features</div>
          </Card>
          
          <Card className="text-center p-6">
            <div className="text-3xl font-bold text-blue-600 mb-2">7</div>
            <div className="text-gray-600">Supported Chains</div>
          </Card>
          
          <Card className="text-center p-6">
            <div className="text-3xl font-bold text-green-600 mb-2">4</div>
            <div className="text-gray-600">Languages</div>
          </Card>
          
          <Card className="text-center p-6">
            <div className="text-3xl font-bold text-orange-600 mb-2">1.5%</div>
            <div className="text-gray-600">Transaction Fee</div>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Features;
