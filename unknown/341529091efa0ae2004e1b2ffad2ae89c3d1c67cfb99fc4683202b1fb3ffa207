/**
 * Vercel API endpoint to proxy CoinGecko requests and avoid CORS
 */

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { ids } = req.query;
    
    if (!ids) {
      res.status(400).json({ error: 'Missing ids parameter' });
      return;
    }

    // Fetch from CoinGecko API
    const response = await fetch(
      `https://api.coingecko.com/api/v3/simple/price?ids=${ids}&vs_currencies=usd`,
      {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'SolPay-App/1.0'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Cache for 5 minutes
    res.setHeader('Cache-Control', 's-maxage=300, stale-while-revalidate');
    res.status(200).json(data);
    
  } catch (error) {
    console.error('Crypto prices API error:', error);
    
    // Return fallback prices if API fails
    const fallbackPrices = {
      'usd-coin': { usd: 1.00 },
      'solana': { usd: 180.50 },
      'ethereum': { usd: 3200.00 },
      'bitcoin': { usd: 65000.00 },
      'binancecoin': { usd: 580.00 },
      'matic-network': { usd: 0.85 },
      'avalanche-2': { usd: 35.00 },
      'tether': { usd: 1.00 }
    };
    
    const { ids } = req.query;
    const requestedIds = ids.split(',');
    const result = {};
    
    requestedIds.forEach(id => {
      if (fallbackPrices[id]) {
        result[id] = fallbackPrices[id];
      }
    });
    
    res.status(200).json(result);
  }
}
