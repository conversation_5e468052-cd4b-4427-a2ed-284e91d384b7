
-- Virtual card table for storing card details
CREATE TABLE IF NOT EXISTS public.virtual_cards (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  number TEXT NOT NULL,
  masked_number TEXT NOT NULL,
  expiry TEXT NOT NULL,
  cvv TEXT NOT NULL,
  provider TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  balance NUMERIC NOT NULL DEFAULT 0,
  spent_this_month NUMERIC NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  billing_street TEXT,
  billing_city TEXT,
  billing_state TEXT,
  billing_zip_code TEXT,
  billing_country TEXT
);

-- Row Level Security for virtual_cards table
ALTER TABLE public.virtual_cards ENABLE ROW LEVEL SECURITY;

-- Policies for virtual_cards table
CREATE POLICY "Users can view their own cards" 
ON public.virtual_cards FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own cards" 
ON public.virtual_cards FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own cards" 
ON public.virtual_cards FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Extension to transactions table to link with virtual_cards
ALTER TABLE public.transactions
ADD CONSTRAINT fk_card_id
FOREIGN KEY (card_id) 
REFERENCES public.virtual_cards(id)
ON DELETE SET NULL;
