
import { useState } from "react";
import Layout from "@/components/Layout";
import CardRegistrationForm from "@/components/card/CardRegistrationForm";
import CardConfirmation from "@/components/card/CardConfirmation";
import CardCreationSuccess from "@/components/card/CardCreationSuccess";
import { useCard } from "@/contexts/CardContext";
import { CardProvider, VirtualCard } from "@/types/card";
import { toast } from "@/hooks/use-toast";
import { getWaitlistStats, WaitlistEntry } from "@/services/waitlistService";
import WaitlistWelcome from "@/components/waitlist/WaitlistWelcome";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { sendPhoneOTP, verifyPhoneOTP, verifyBVN } from "@/services/verificationService";

enum RegistrationStep {
  WELCOME,
  FORM,
  VERIFICATION,
  CUSTOMIZATION,
  CONFIRMATION,
  WAITLIST_WELCOME,
  SUCCESS,
  ERROR
}

export default function CardRegistration() {
  const [currentStep, setCurrentStep] = useState<RegistrationStep>(RegistrationStep.FORM);
  const [formData, setFormData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createdCard, setCreatedCard] = useState<VirtualCard | null>(null);
  const [waitlistEntry, setWaitlistEntry] = useState<WaitlistEntry | null>(null);
  const [waitlistStats, setWaitlistStats] = useState<any>(null);

  const { generateCard, applyForCard } = useCard();

  const handleFormSubmit = async (data: any) => {
    console.log('Form data received:', data);
    setFormData(data);
    setCurrentStep(RegistrationStep.VERIFICATION);
  };

  const handleVerificationComplete = (verificationData: any) => {
    console.log('Verification data received:', verificationData);
    // Merge verification data with form data
    setFormData(prev => ({ ...prev, ...verificationData }));
    setCurrentStep(RegistrationStep.CUSTOMIZATION);
  };

  const handleCustomizationComplete = (customizationData: any) => {
    console.log('Customization data received:', customizationData);
    // Merge customization data with form data
    setFormData(prev => ({ ...prev, ...customizationData }));
    setCurrentStep(RegistrationStep.CONFIRMATION);
  };

  const handleConfirmation = async () => {
    if (!formData) {
      console.error('No form data available for card application');
      setError('Missing form data. Please start over.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🎯 Processing card application for waitlist:', formData);

      // Simulate processing time for better UX
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Add user to waitlist instead of creating card immediately
      const entry = await applyForCard(formData);

      // Get waitlist statistics
      const stats = await getWaitlistStats();

      setWaitlistEntry(entry);
      setWaitlistStats(stats);
      setCurrentStep(RegistrationStep.WAITLIST_WELCOME);

      console.log('🎉 Successfully added to waitlist at position:', entry.position);
    } catch (error) {
      console.error('❌ Card application failed:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
      setCurrentStep(RegistrationStep.ERROR);

      toast({
        title: "Application Failed",
        description: "There was an error processing your application. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditInformation = () => {
    setCurrentStep(RegistrationStep.FORM);
  };

  const handleRetry = () => {
    setError(null);
    setCurrentStep(RegistrationStep.CONFIRMATION);
  };

  const handleStartApplication = () => {
    setCurrentStep(RegistrationStep.FORM);
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-4 sm:py-8 max-w-4xl">
        <h1 className="text-2xl sm:text-3xl font-bold mb-6">Virtual Card Registration</h1>

        {currentStep === RegistrationStep.WELCOME && (
          <WelcomeScreen onStart={handleStartApplication} />
        )}

        {currentStep === RegistrationStep.FORM && (
          <CardRegistrationForm
            onSubmit={handleFormSubmit}
            isLoading={isLoading}
          />
        )}

        {currentStep === RegistrationStep.VERIFICATION && (
          <IdentityVerification
            onComplete={handleVerificationComplete}
            onBack={() => setCurrentStep(RegistrationStep.FORM)}
          />
        )}

        {currentStep === RegistrationStep.CUSTOMIZATION && (
          <CardCustomization
            onComplete={handleCustomizationComplete}
            onBack={() => setCurrentStep(RegistrationStep.VERIFICATION)}
          />
        )}

        {currentStep === RegistrationStep.CONFIRMATION && (
          <CardConfirmation
            formData={formData}
            onConfirm={handleConfirmation}
            onEdit={handleEditInformation}
            isLoading={isLoading}
          />
        )}

        {currentStep === RegistrationStep.WAITLIST_WELCOME && waitlistEntry && waitlistStats && (
          <WaitlistWelcome
            waitlistEntry={waitlistEntry}
            totalWaiting={waitlistStats.waiting_count}
            onContinue={() => window.location.href = '/dashboard'}
          />
        )}

        {currentStep === RegistrationStep.SUCCESS && createdCard && (
          <CardCreationSuccess card={createdCard} />
        )}

        {currentStep === RegistrationStep.ERROR && (
          <div className="w-full max-w-2xl mx-auto px-4">
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                {error || "There was an error creating your virtual card. Please try again."}
              </AlertDescription>
            </Alert>

            <CardConfirmation
              formData={formData}
              onConfirm={handleRetry}
              onEdit={handleEditInformation}
              isLoading={isLoading}
            />
          </div>
        )}
      </div>
    </Layout>
  );
}

// Welcome Screen Component - This is the screen with 3 points that was missing!
const WelcomeScreen = ({ onStart }: { onStart: () => void }) => {
  return (
    <div className="w-full max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div className="p-4 sm:p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="relative h-20 w-20">
            <div className="absolute inset-0 bg-primary/10 rounded-full flex items-center justify-center">
              <div className="h-16 w-16 bg-primary/20 rounded-full flex items-center justify-center">
                <div className="h-12 w-12 bg-primary rounded-full flex items-center justify-center text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h2 className="text-xl sm:text-2xl font-bold text-center mb-2">Apply for Your Virtual Payment Card</h2>
        <p className="text-center text-muted-foreground mb-6 sm:mb-8 text-sm sm:text-base">This process takes less than 2 minutes. Your data is encrypted and secured.</p>

        <div className="space-y-4 sm:space-y-6">
          <div className="flex items-start space-x-3 sm:space-x-4">
            <div className="bg-primary/10 rounded-full p-2 mt-1 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div className="min-w-0">
              <h3 className="font-medium text-sm sm:text-base">Bank-Level Security</h3>
              <p className="text-xs sm:text-sm text-muted-foreground">Your personal information is protected with advanced encryption.</p>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="bg-primary/10 rounded-full p-2 mt-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium">Quick Process</h3>
              <p className="text-sm text-muted-foreground">Complete your application in just a few steps with our streamlined process.</p>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="bg-primary/10 rounded-full p-2 mt-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium">Use Anywhere</h3>
              <p className="text-sm text-muted-foreground">Your virtual card works with online retailers and subscription services worldwide.</p>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <button
            onClick={onStart}
            className="w-full bg-primary text-white py-3 px-4 rounded-lg font-medium hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
          >
            Start Application
          </button>
          <p className="text-xs text-center mt-4 text-muted-foreground">
            By continuing, you agree to our <a href="#" className="underline hover:text-primary">Terms of Service</a> and <a href="#" className="underline hover:text-primary">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  );
};

// Identity Verification Component
const IdentityVerification = ({
  onComplete,
  onBack
}: {
  onComplete: (data: any) => void;
  onBack: () => void;
}) => {
  const [verificationMethod, setVerificationMethod] = useState<'basic' | 'verified' | 'premium'>('verified');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [bvn, setBvn] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [verificationOption, setVerificationOption] = useState<'phone' | 'bvn'>('phone');
  const [step, setStep] = useState<'select' | 'method' | 'phone' | 'bvn' | 'otp' | 'complete'>('select');

  const handleVerificationMethodSelect = (method: 'basic' | 'verified' | 'premium') => {
    setVerificationMethod(method);
    if (method === 'basic') {
      // Basic tier requires no verification
      setIsVerified(true);
      setStep('complete');
    } else {
      setStep('method');
    }
  };

  const handlePhoneSubmit = async () => {
    if (!phoneNumber) return;

    setIsVerifying(true);
    try {
      console.log('Sending OTP to:', phoneNumber);
      const result = await sendPhoneOTP(phoneNumber);

      if (result.success) {
        toast({
          title: "OTP Sent",
          description: "Verification code has been sent to your phone number.",
        });
        setStep('otp');
      } else {
        toast({
          title: "Failed to Send OTP",
          description: result.message || "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      toast({
        title: "Error",
        description: "Failed to send verification code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleOTPSubmit = async () => {
    if (!otpCode) return;

    setIsVerifying(true);
    try {
      console.log('Verifying OTP:', otpCode, 'for phone:', phoneNumber);
      const result = await verifyPhoneOTP(phoneNumber, otpCode);

      if (result.success) {
        setIsVerified(true);
        setStep('complete');
        toast({
          title: "Phone Verified",
          description: "Your phone number has been successfully verified.",
        });
      } else {
        toast({
          title: "Invalid Code",
          description: result.message || "Please check your code and try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      toast({
        title: "Verification Failed",
        description: "Failed to verify code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleBVNSubmit = async () => {
    if (!bvn || !firstName || !lastName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all BVN verification fields.",
        variant: "destructive",
      });
      return;
    }

    setIsVerifying(true);
    try {
      console.log('Verifying BVN:', bvn);
      const result = await verifyBVN(bvn, firstName, lastName);

      if (result.success) {
        setIsVerified(true);
        setStep('complete');
        toast({
          title: "BVN Verified",
          description: "Your BVN has been successfully verified.",
        });
      } else {
        toast({
          title: "BVN Verification Failed",
          description: result.message || "Please check your BVN details and try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error verifying BVN:', error);
      toast({
        title: "Verification Failed",
        description: "Failed to verify BVN. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleContinue = () => {
    onComplete({
      verificationTier: verificationMethod,
      verifiedAt: new Date(),
      phoneNumber: phoneNumber,
      bvn: bvn,
      phoneVerified: isVerified && verificationMethod !== 'basic' && verificationOption === 'phone',
      bvnVerified: isVerified && verificationMethod !== 'basic' && verificationOption === 'bvn'
    });
  };

  return (
    <div className="w-full max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div className="p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="relative w-full max-w-md">
            <div className="flex items-center justify-between">
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">1</div>
                <div className="text-xs mt-1">Personal Info</div>
              </div>
              <div className="flex-1 h-1 bg-primary mx-2"></div>
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">2</div>
                <div className="text-xs mt-1">Verification</div>
              </div>
              <div className="flex-1 h-1 bg-muted mx-2"></div>
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">3</div>
                <div className="text-xs mt-1">Customization</div>
              </div>
              <div className="flex-1 h-1 bg-muted mx-2"></div>
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">4</div>
                <div className="text-xs mt-1">Confirm</div>
              </div>
            </div>
          </div>
        </div>

        <h2 className="text-2xl font-bold text-center mb-2">Identity Verification</h2>
        <p className="text-center text-muted-foreground mb-8">Choose your verification level to continue</p>

        <div className="grid gap-4 md:grid-cols-3 mb-6">
          {/* Basic Tier */}
          <div
            className={`border rounded-xl p-6 cursor-pointer transition-all ${verificationMethod === 'basic' ? 'border-primary ring-2 ring-primary/20' : 'hover:border-primary/50'}`}
            onClick={() => handleVerificationMethodSelect('basic')}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-lg">Basic</h3>
              <div className="text-2xl">🚀</div>
            </div>
            <div className="space-y-2 text-sm mb-4">
              <div className="flex justify-between">
                <span>Daily Limit:</span>
                <span className="font-medium">$500</span>
              </div>
              <div className="flex justify-between">
                <span>Monthly Limit:</span>
                <span className="font-medium">$2,000</span>
              </div>
              <div className="flex justify-between">
                <span>Per Transaction:</span>
                <span className="font-medium">$200</span>
              </div>
              <div className="flex justify-between">
                <span>Max Balance:</span>
                <span className="font-medium">$1,000</span>
              </div>
            </div>
            <div className="text-xs text-green-600 mb-2">✅ No verification required</div>
            <p className="text-xs text-muted-foreground">Perfect for testing and small purchases</p>
          </div>

          {/* Verified Tier */}
          <div
            className={`border rounded-xl p-6 cursor-pointer transition-all ${verificationMethod === 'verified' ? 'border-primary ring-2 ring-primary/20' : 'hover:border-primary/50'}`}
            onClick={() => handleVerificationMethodSelect('verified')}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-lg">Verified</h3>
              <div className="text-2xl">✅</div>
            </div>
            <div className="space-y-2 text-sm mb-4">
              <div className="flex justify-between">
                <span>Daily Limit:</span>
                <span className="font-medium">$1,500</span>
              </div>
              <div className="flex justify-between">
                <span>Monthly Limit:</span>
                <span className="font-medium">$10,000</span>
              </div>
              <div className="flex justify-between">
                <span>Per Transaction:</span>
                <span className="font-medium">$500</span>
              </div>
              <div className="flex justify-between">
                <span>Max Balance:</span>
                <span className="font-medium">$5,000</span>
              </div>
            </div>
            <div className="text-xs text-blue-600 mb-2">📱 Phone verification required</div>
            <p className="text-xs text-muted-foreground">Recommended for most users</p>
          </div>

          {/* Premium Tier */}
          <div
            className={`border rounded-xl p-6 cursor-pointer transition-all ${verificationMethod === 'premium' ? 'border-primary ring-2 ring-primary/20' : 'hover:border-primary/50'}`}
            onClick={() => handleVerificationMethodSelect('premium')}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-lg">Premium</h3>
              <div className="text-2xl">👑</div>
            </div>
            <div className="space-y-2 text-sm mb-4">
              <div className="flex justify-between">
                <span>Daily Limit:</span>
                <span className="font-medium">$5,000</span>
              </div>
              <div className="flex justify-between">
                <span>Monthly Limit:</span>
                <span className="font-medium">$50,000</span>
              </div>
              <div className="flex justify-between">
                <span>Per Transaction:</span>
                <span className="font-medium">$2,000</span>
              </div>
              <div className="flex justify-between">
                <span>Max Balance:</span>
                <span className="font-medium">$25,000</span>
              </div>
            </div>
            <div className="text-xs text-purple-600 mb-2">🆔 Full verification required</div>
            <p className="text-xs text-muted-foreground">For high-volume users</p>
          </div>
        </div>

        {/* Verification Process */}
        {step !== 'select' && (
          <div className="bg-muted/50 p-6 rounded-lg mb-6">
            <h4 className="font-medium mb-4">
              {step === 'method' && 'Choose Verification Method'}
              {step === 'phone' && 'Phone Verification'}
              {step === 'bvn' && 'BVN Verification'}
              {step === 'otp' && 'Enter Verification Code'}
              {step === 'complete' && 'Verification Complete'}
            </h4>

            {step === 'method' && (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Choose how you'd like to verify your identity
                </p>
                <div className="grid gap-3 md:grid-cols-2">
                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${verificationOption === 'phone' ? 'border-primary ring-2 ring-primary/20 bg-primary/5' : 'hover:border-primary/50'}`}
                    onClick={() => {
                      setVerificationOption('phone');
                      setStep('phone');
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">📱</div>
                      <div>
                        <h5 className="font-medium">Phone Verification</h5>
                        <p className="text-sm text-muted-foreground">Verify using SMS code</p>
                      </div>
                    </div>
                  </div>
                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${verificationOption === 'bvn' ? 'border-primary ring-2 ring-primary/20 bg-primary/5' : 'hover:border-primary/50'}`}
                    onClick={() => {
                      setVerificationOption('bvn');
                      setStep('bvn');
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">🆔</div>
                      <div>
                        <h5 className="font-medium">BVN Verification</h5>
                        <p className="text-sm text-muted-foreground">Verify using your BVN (Nigerian users)</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {step === 'phone' && (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  We'll send a verification code to your phone number
                </p>
                <div className="flex gap-3">
                  <input
                    type="tel"
                    placeholder="+234 ************"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
                  />
                  <button
                    onClick={handlePhoneSubmit}
                    disabled={!phoneNumber || isVerifying}
                    className="bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-primary/90 disabled:opacity-50"
                  >
                    {isVerifying ? "Sending..." : "Send Code"}
                  </button>
                </div>
              </div>
            )}

            {step === 'bvn' && (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Enter your BVN and personal details for verification
                </p>
                <div className="grid gap-3 md:grid-cols-2">
                  <input
                    type="text"
                    placeholder="First Name"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
                  />
                  <input
                    type="text"
                    placeholder="Last Name"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
                  />
                </div>
                <div className="flex gap-3">
                  <input
                    type="text"
                    placeholder="Enter your 11-digit BVN"
                    value={bvn}
                    onChange={(e) => setBvn(e.target.value)}
                    maxLength={11}
                    className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
                  />
                  <button
                    onClick={handleBVNSubmit}
                    disabled={!bvn || !firstName || !lastName || isVerifying}
                    className="bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-primary/90 disabled:opacity-50"
                  >
                    {isVerifying ? "Verifying..." : "Verify BVN"}
                  </button>
                </div>
                <button
                  onClick={() => setStep('method')}
                  className="text-sm text-primary hover:underline"
                >
                  Choose different verification method
                </button>
              </div>
            )}

            {step === 'otp' && (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Enter the 6-digit code sent to {phoneNumber}
                </p>
                <div className="flex gap-3">
                  <input
                    type="text"
                    placeholder="123456"
                    value={otpCode}
                    onChange={(e) => setOtpCode(e.target.value)}
                    maxLength={6}
                    className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
                  />
                  <button
                    onClick={handleOTPSubmit}
                    disabled={!otpCode || isVerifying}
                    className="bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-primary/90 disabled:opacity-50"
                  >
                    {isVerifying ? "Verifying..." : "Verify"}
                  </button>
                </div>
                <button
                  onClick={() => setStep('phone')}
                  className="text-sm text-primary hover:underline"
                >
                  Change phone number
                </button>
              </div>
            )}

            {step === 'complete' && (
              <div className="flex items-center text-green-500">
                <svg className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="font-medium">
                  {verificationMethod === 'basic' ? 'Ready to create your card!' : 'Verification complete! You\'re good to go.'}
                </span>
              </div>
            )}
          </div>
        )}

        <div className="flex justify-between">
          <button
            onClick={onBack}
            className="bg-transparent border border-gray-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
          >
            Back
          </button>
          <button
            onClick={handleContinue}
            disabled={!isVerified}
            className={`bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors ${!isVerified && 'opacity-50 cursor-not-allowed'}`}
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};

// Card Customization Component
const CardCustomization = ({
  onComplete,
  onBack
}: {
  onComplete: (data: any) => void;
  onBack: () => void;
}) => {
  const [cardTheme, setCardTheme] = useState('blue');
  const [cardNickname, setCardNickname] = useState('');
  const [defaultCurrency, setDefaultCurrency] = useState('usd');

  const handleContinue = () => {
    onComplete({
      cardTheme,
      cardNickname: cardNickname || "My Virtual Card",
      defaultCurrency
    });
  };

  return (
    <div className="w-full max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div className="p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="relative w-full max-w-md">
            <div className="flex items-center justify-between">
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">1</div>
                <div className="text-xs mt-1">Personal Info</div>
              </div>
              <div className="flex-1 h-1 bg-primary mx-2"></div>
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">2</div>
                <div className="text-xs mt-1">Verification</div>
              </div>
              <div className="flex-1 h-1 bg-primary mx-2"></div>
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">3</div>
                <div className="text-xs mt-1">Customization</div>
              </div>
              <div className="flex-1 h-1 bg-muted mx-2"></div>
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">4</div>
                <div className="text-xs mt-1">Confirm</div>
              </div>
            </div>
          </div>
        </div>

        <h2 className="text-2xl font-bold text-center mb-2">Card Customization</h2>
        <p className="text-center text-muted-foreground mb-8">Personalize your virtual card experience</p>

        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">Card Theme</label>
              <div className="grid grid-cols-4 gap-3">
                <div
                  className={`aspect-square rounded-md bg-gradient-to-br from-blue-600 to-blue-900 cursor-pointer transition-all ${cardTheme === 'blue' ? 'ring-2 ring-primary ring-offset-2' : ''}`}
                  onClick={() => setCardTheme('blue')}
                ></div>
                <div
                  className={`aspect-square rounded-md bg-gradient-to-br from-purple-600 to-indigo-900 cursor-pointer transition-all ${cardTheme === 'purple' ? 'ring-2 ring-primary ring-offset-2' : ''}`}
                  onClick={() => setCardTheme('purple')}
                ></div>
                <div
                  className={`aspect-square rounded-md bg-gradient-to-br from-teal-500 to-green-700 cursor-pointer transition-all ${cardTheme === 'green' ? 'ring-2 ring-primary ring-offset-2' : ''}`}
                  onClick={() => setCardTheme('green')}
                ></div>
                <div
                  className={`aspect-square rounded-md bg-gradient-to-br from-gray-700 to-gray-900 cursor-pointer transition-all ${cardTheme === 'black' ? 'ring-2 ring-primary ring-offset-2' : ''}`}
                  onClick={() => setCardTheme('black')}
                ></div>
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">Card Nickname (Optional)</label>
              <input
                type="text"
                value={cardNickname}
                onChange={(e) => setCardNickname(e.target.value)}
                placeholder="My Shopping Card"
                className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
              />
              <p className="text-xs text-muted-foreground mt-1">Give your card a name to easily identify its purpose</p>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium mb-2">Default Currency</label>
              <select
                value={defaultCurrency}
                onChange={(e) => setDefaultCurrency(e.target.value)}
                className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
              >
                <option value="usd">USD - US Dollar</option>
                <option value="eur">EUR - Euro</option>
                <option value="gbp">GBP - British Pound</option>
                <option value="jpy">JPY - Japanese Yen</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Card Preview</label>
            <div className={`w-full aspect-[16/9] rounded-xl p-6 flex flex-col justify-between bg-gradient-to-br
              ${cardTheme === 'blue' ? 'from-blue-600 to-blue-900' :
              cardTheme === 'purple' ? 'from-purple-600 to-indigo-900' :
              cardTheme === 'green' ? 'from-teal-500 to-green-700' :
              'from-gray-700 to-gray-900'}
              text-white relative overflow-hidden shadow-lg`}
            >
              <div className="absolute inset-0 opacity-10 pointer-events-none">
                <svg width="100%" height="100%" viewBox="0 0 400 255" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="380" cy="138" r="153" fill="white"/>
                  <circle cx="76" cy="213" r="140" stroke="white" strokeWidth="40"/>
                </svg>
              </div>

              <div className="flex justify-between items-start">
                <div className="flex items-center">
                  <img
                    src="/solana.png"
                    alt="solpay"
                    className="h-12 w-auto max-w-[180px] opacity-80 object-contain"
                    loading="eager"
                  />
                </div>
                <div>
                  <svg className="h-10 w-10" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M44 24C44 35.046 35.046 44 24 44C12.954 44 4 35.046 4 24C4 12.954 12.954 4 24 4C35.046 4 44 12.954 44 24Z" fill="white"/>
                    <path d="M19.5555 30.1667H15.6667L12.9444 18.75C12.871 18.4538 12.6938 18.1925 12.4465 18.0123C12.1991 17.832 11.8976 17.7441 11.5972 17.7639C10.3611 17.7639 9.16667 17.3889 8.05556 16.75V16.1944H14C14.9028 16.1944 15.5833 16.8889 15.75 17.7639L17.2083 25.5556L20.8056 16.1944H24.5L19.5555 30.1667ZM25.8888 30.1667H22.3333L25.2222 16.1944H28.7777L25.8888 30.1667ZM35.3888 16.5416C36.2639 16.5416 37.0555 16.7222 37.4583 16.9166L36.9444 20.1388C36.5416 19.9167 35.9722 19.7639 35.2639 19.7639C34.0833 19.7639 33.4444 20.2222 33.4444 20.75C33.4444 21.2361 34 21.6111 34.9166 22.0972C36.6944 22.9583 37.6111 24.0277 37.5833 25.4583C37.5833 28.0416 35.4722 30.3333 31.9722 30.3333C30.8333 30.3194 29.7096 30.0608 28.6666 29.5694L29.2083 26.2916C30.2222 26.7916 31.0833 27.0277 31.9722 27.0277C32.9166 27.0277 33.6944 26.5555 33.7222 25.8611C33.7222 25.3194 33.25 24.9166 32.2083 24.375C30.4305 23.5139 29.3888 22.4166 29.4166 20.9166C29.4166 18.5 31.5833 16.5416 35.3888 16.5416Z" fill="#1434CB"/>
                  </svg>
                </div>
              </div>

              <div className="flex flex-col space-y-4">
                <div className="font-mono text-xl">
                  4000 •••• •••• 1234
                </div>
                <div className="flex justify-between items-end">
                  <div>
                    <div className="text-xs opacity-70">Card Holder</div>
                    <div>JOHN DOE</div>
                  </div>
                  <div>
                    <div className="text-xs opacity-70">Expires</div>
                    <div>12/28</div>
                  </div>
                </div>
              </div>

              <div className="absolute bottom-6 left-6">
                <div className="text-xs opacity-70">
                  {cardNickname || 'My Virtual Card'}
                </div>
                <div className="text-xs opacity-70 uppercase">
                  {defaultCurrency === 'usd' ? 'US Dollar' :
                   defaultCurrency === 'eur' ? 'Euro' :
                   defaultCurrency === 'gbp' ? 'British Pound' : 'Japanese Yen'}
                </div>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-2">This is a preview of your virtual card. The actual card will have enhanced security features.</p>
          </div>
        </div>

        <div className="flex justify-between mt-8">
          <button
            onClick={onBack}
            className="bg-transparent border border-gray-300 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
          >
            Back
          </button>
          <button
            onClick={handleContinue}
            className="bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};
