
/**
 * Group Card Overview Component
 */
import React, { useState } from 'react';
import { Wallet, CreditCard, ArrowUpRight, ArrowDownRight, DollarSign, Users, Receipt } from 'lucide-react';
import { useGroupCard } from '@/contexts/GroupCardContext';
import { GroupCardTransactionStatus } from '@/types/groupCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function GroupCardOverview() {
  const { selectedCard, transactions, members, isLoading, fundCard } = useGroupCard();
  const [fundAmount, setFundAmount] = useState('');
  const [fundDialogOpen, setFundDialogOpen] = useState(false);
  const [isFunding, setIsFunding] = useState(false);
  const [selectedWallet, setSelectedWallet] = useState('');
  const [selectedToken, setSelectedToken] = useState('USDC');

  // Calculate statistics
  const totalMembers = members.length;
  const pendingTransactions = transactions.filter(t => t.status === GroupCardTransactionStatus.PENDING).length;
  const recentTransactions = transactions.slice(0, 5);

  // Handle funding the card
  const handleFundCard = async () => {
    const amount = parseFloat(fundAmount);
    if (isNaN(amount) || amount <= 0) return;

    setIsFunding(true);
    try {
      await fundCard(amount, selectedWallet || undefined, selectedToken);
      setFundAmount('');
      setFundDialogOpen(false);
    } finally {
      setIsFunding(false);
    }
  };

  // Render loading state
  if (isLoading || !selectedCard) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-32 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Card Balance */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-primary/10 rounded-full">
                <CreditCard className="h-8 w-8 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Current Balance</p>
                <p className="text-3xl font-bold">${selectedCard.balance.toFixed(2)}</p>
              </div>
            </div>
            <Dialog open={fundDialogOpen} onOpenChange={setFundDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Fund Card
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Fund Group Card</DialogTitle>
                  <DialogDescription>
                    Add funds to your group card from your wallet.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount</Label>
                    <div className="flex items-center">
                      <span className="absolute ml-3 text-muted-foreground">$</span>
                      <Input
                        id="amount"
                        type="number"
                        min="0.01"
                        step="0.01"
                        className="pl-7"
                        value={fundAmount}
                        onChange={(e) => setFundAmount(e.target.value)}
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wallet">From Wallet</Label>
                    <Select value={selectedWallet} onValueChange={setSelectedWallet}>
                      <SelectTrigger id="wallet">
                        <SelectValue placeholder="Select wallet" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Default Wallet</SelectItem>
                        <SelectItem value="wallet1">Personal Wallet</SelectItem>
                        <SelectItem value="wallet2">Savings Wallet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="token">Token</Label>
                    <Select value={selectedToken} onValueChange={setSelectedToken}>
                      <SelectTrigger id="token">
                        <SelectValue placeholder="Select token" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USDC">USDC</SelectItem>
                        <SelectItem value="SOL">SOL</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    onClick={handleFundCard}
                    disabled={isFunding || !fundAmount || parseFloat(fundAmount) <= 0}
                  >
                    {isFunding ? 'Processing...' : 'Fund Card'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-2xl font-bold">{totalMembers}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Receipt className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-2xl font-bold">{pendingTransactions}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Card Created</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <span className="text-lg font-medium">
                {new Date(selectedCard.createdAt).toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            The latest transactions on this card
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentTransactions.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">
              No transactions yet
            </p>
          ) : (
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`p-2 rounded-full ${
                      transaction.amount < 0 ? 'bg-destructive/10' : 'bg-green-500/10'
                    }`}>
                      {transaction.amount < 0 ? (
                        <ArrowUpRight className={`h-4 w-4 ${
                          transaction.amount < 0 ? 'text-destructive' : 'text-green-500'
                        }`} />
                      ) : (
                        <ArrowDownRight className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">
                        {transaction.merchant || 'Unknown Merchant'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {transaction.category || 'Uncategorized'} •
                        {new Date(transaction.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-medium ${
                      transaction.amount < 0 ? 'text-destructive' : 'text-green-500'
                    }`}>
                      {transaction.amount < 0 ? '-' : '+'}${Math.abs(transaction.amount).toFixed(2)}
                    </p>
                    <p className="text-xs text-muted-foreground capitalize">
                      {transaction.status.toLowerCase()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full">View All Transactions</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
