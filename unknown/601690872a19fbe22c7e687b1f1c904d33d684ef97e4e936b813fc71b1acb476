# Business Integration Examples

## How Businesses Can Integrate Your Crypto Payment Gateway

### 1. **E-commerce Website Integration**

#### **Node.js/Express Example**
```javascript
const express = require('express');
const app = express();

// Your payment gateway client
const CryptoPayments = require('@yourplatform/crypto-payments');
const crypto = new CryptoPayments('pk_live_your_api_key_here');

// Checkout endpoint
app.post('/create-payment', async (req, res) => {
  try {
    const { amount, currency, items } = req.body;
    
    // Create payment intent
    const paymentIntent = await crypto.paymentIntents.create({
      amount: amount * 100, // Amount in cents
      currency: currency,
      accepted_cryptocurrencies: ['SOL', 'USDC', 'ETH'],
      settlement_currency: 'NGN', // Settle in Nigerian Naira
      metadata: {
        order_id: req.body.order_id,
        customer_email: req.body.customer_email
      },
      success_url: 'https://yourstore.com/success',
      cancel_url: 'https://yourstore.com/cancel'
    });

    res.json({
      client_secret: paymentIntent.client_secret,
      payment_url: paymentIntent.payment_url,
      qr_code: paymentIntent.qr_code
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Webhook endpoint to handle payment confirmations
app.post('/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['x-signature'];
  
  try {
    const event = crypto.webhooks.constructEvent(req.body, sig, 'whsec_your_webhook_secret');
    
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        console.log('Payment succeeded:', paymentIntent.id);
        
        // Fulfill the order
        fulfillOrder(paymentIntent.metadata.order_id);
        break;
        
      case 'payment_intent.payment_failed':
        console.log('Payment failed:', event.data.object.id);
        break;
    }
    
    res.json({received: true});
  } catch (err) {
    console.log('Webhook signature verification failed.', err.message);
    res.status(400).send(`Webhook Error: ${err.message}`);
  }
});
```

#### **Frontend Integration (React)**
```jsx
import React, { useState } from 'react';

function CheckoutForm({ amount, currency }) {
  const [paymentUrl, setPaymentUrl] = useState('');
  const [qrCode, setQrCode] = useState('');
  const [loading, setLoading] = useState(false);

  const handlePayment = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          order_id: 'order_123',
          customer_email: '<EMAIL>'
        }),
      });

      const { payment_url, qr_code } = await response.json();
      
      setPaymentUrl(payment_url);
      setQrCode(qr_code);
      
      // Redirect to payment page or show QR code
      window.open(payment_url, '_blank');
      
    } catch (error) {
      console.error('Payment error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="checkout-form">
      <h3>Pay with Crypto</h3>
      <p>Amount: {amount} {currency}</p>
      
      <button onClick={handlePayment} disabled={loading}>
        {loading ? 'Creating Payment...' : 'Pay with Crypto'}
      </button>
      
      {qrCode && (
        <div className="qr-code">
          <h4>Scan to Pay</h4>
          <img src={qrCode} alt="Payment QR Code" />
        </div>
      )}
    </div>
  );
}
```

### 2. **WordPress Plugin Integration**

```php
<?php
/**
 * WordPress Plugin for Crypto Payments
 */

class CryptoPaymentGateway extends WC_Payment_Gateway {
    
    public function __construct() {
        $this->id = 'crypto_payments';
        $this->method_title = 'Crypto Payments';
        $this->method_description = 'Accept cryptocurrency payments';
        
        $this->init_form_fields();
        $this->init_settings();
        
        $this->title = $this->get_option('title');
        $this->description = $this->get_option('description');
        $this->api_key = $this->get_option('api_key');
    }
    
    public function process_payment($order_id) {
        $order = wc_get_order($order_id);
        
        // Create payment intent
        $response = wp_remote_post('https://api.yourplatform.com/v1/payment_intents', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json'
            ],
            'body' => json_encode([
                'amount' => $order->get_total() * 100,
                'currency' => $order->get_currency(),
                'metadata' => [
                    'order_id' => $order_id,
                    'customer_email' => $order->get_billing_email()
                ],
                'success_url' => $this->get_return_url($order),
                'cancel_url' => wc_get_checkout_url()
            ])
        ]);
        
        $payment_intent = json_decode(wp_remote_retrieve_body($response), true);
        
        if ($payment_intent && $payment_intent['payment_url']) {
            return [
                'result' => 'success',
                'redirect' => $payment_intent['payment_url']
            ];
        }
        
        return [
            'result' => 'failure',
            'messages' => 'Payment failed to initialize'
        ];
    }
}
```

### 3. **Shopify App Integration**

```javascript
// Shopify App Backend
const express = require('express');
const { Shopify } = require('@shopify/shopify-api');
const CryptoPayments = require('@yourplatform/crypto-payments');

const app = express();

// Payment endpoint for Shopify checkout
app.post('/api/payment', async (req, res) => {
  try {
    const { shop, amount, currency, order_id } = req.body;
    
    const crypto = new CryptoPayments(process.env.CRYPTO_PAYMENTS_API_KEY);
    
    const paymentIntent = await crypto.paymentIntents.create({
      amount: amount,
      currency: currency,
      metadata: {
        shop: shop,
        order_id: order_id
      },
      success_url: `https://${shop}/orders/${order_id}`,
      cancel_url: `https://${shop}/cart`
    });
    
    res.json({
      payment_url: paymentIntent.payment_url,
      qr_code: paymentIntent.qr_code
    });
    
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Webhook handler
app.post('/webhook/payment-confirmed', (req, res) => {
  const event = req.body;
  
  if (event.type === 'payment_intent.succeeded') {
    const { shop, order_id } = event.data.object.metadata;
    
    // Mark Shopify order as paid
    markShopifyOrderAsPaid(shop, order_id);
  }
  
  res.json({ received: true });
});
```

### 4. **Mobile App Integration (React Native)**

```jsx
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, Linking } from 'react-native';
import QRCode from 'react-native-qrcode-svg';

const CryptoPaymentScreen = ({ amount, currency, onSuccess }) => {
  const [paymentData, setPaymentData] = useState(null);
  const [loading, setLoading] = useState(false);

  const createPayment = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('https://api.yourplatform.com/v1/payment_intents', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer pk_live_your_api_key',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount * 100,
          currency: currency,
          accepted_cryptocurrencies: ['SOL', 'USDC'],
          settlement_currency: 'NGN'
        }),
      });

      const paymentIntent = await response.json();
      setPaymentData(paymentIntent);
      
    } catch (error) {
      console.error('Payment creation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const openPaymentPage = () => {
    if (paymentData?.payment_url) {
      Linking.openURL(paymentData.payment_url);
    }
  };

  return (
    <View style={{ padding: 20, alignItems: 'center' }}>
      <Text style={{ fontSize: 24, marginBottom: 20 }}>
        Pay {amount} {currency}
      </Text>
      
      {!paymentData ? (
        <TouchableOpacity 
          onPress={createPayment} 
          disabled={loading}
          style={{ 
            backgroundColor: '#007AFF', 
            padding: 15, 
            borderRadius: 8 
          }}
        >
          <Text style={{ color: 'white', fontSize: 16 }}>
            {loading ? 'Creating Payment...' : 'Pay with Crypto'}
          </Text>
        </TouchableOpacity>
      ) : (
        <View style={{ alignItems: 'center' }}>
          <Text style={{ marginBottom: 20 }}>Scan QR Code to Pay</Text>
          
          <QRCode
            value={paymentData.payment_url}
            size={200}
            backgroundColor="white"
            color="black"
          />
          
          <TouchableOpacity 
            onPress={openPaymentPage}
            style={{ 
              backgroundColor: '#34C759', 
              padding: 15, 
              borderRadius: 8,
              marginTop: 20 
            }}
          >
            <Text style={{ color: 'white', fontSize: 16 }}>
              Open Payment Page
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
```

### 5. **Simple Payment Links (No Code Required)**

For businesses that don't want to integrate APIs:

```html
<!-- Simple HTML form -->
<form action="https://api.yourplatform.com/v1/payment_links" method="POST">
  <input type="hidden" name="api_key" value="pk_live_your_api_key">
  <input type="hidden" name="amount" value="5000"> <!-- $50.00 -->
  <input type="hidden" name="currency" value="USD">
  <input type="hidden" name="title" value="Premium Subscription">
  <input type="hidden" name="success_url" value="https://yoursite.com/success">
  
  <button type="submit">Create Payment Link</button>
</form>
```

### 6. **Subscription/Recurring Payments**

```javascript
// Create recurring payment
const subscription = await crypto.subscriptions.create({
  customer: 'cus_customer_id',
  items: [{
    price: 'price_monthly_subscription'
  }],
  payment_behavior: 'default_incomplete',
  expand: ['latest_invoice.payment_intent'],
  accepted_cryptocurrencies: ['USDC'], // Stable coin for subscriptions
  settlement_currency: 'NGN'
});
```

## Key Advantages Over Competitors

1. **Multi-Chain Support**: Accept SOL, USDC, ETH, and more
2. **Local Settlement**: Direct NGN settlement to Nigerian banks
3. **Global Reach**: Customers worldwide can pay with crypto
4. **Real-time Rates**: Dynamic exchange rates
5. **Simple Integration**: Copy-paste like Stripe
6. **No Volatility Risk**: Instant conversion to stable currency
7. **Lower Fees**: Crypto transactions cost less than traditional payments
8. **24/7 Processing**: No banking hours limitations

## Getting Started

1. **Sign up** for merchant account
2. **Get API keys** from dashboard
3. **Install SDK** or use direct API calls
4. **Test integration** with test keys
5. **Go live** with production keys

Your platform makes crypto payments as easy as traditional payments!
