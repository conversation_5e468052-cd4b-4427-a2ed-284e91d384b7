/**
 * Telegram Bot Webhook API
 * Handles incoming messages from Telegram
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { TelegramBotService } from '@/services/telegramBotService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const update = req.body;
    
    // Check if it's a message update
    if (update.message && update.message.text) {
      const message = {
        telegramId: update.message.from.id,
        text: update.message.text,
        messageId: update.message.message_id,
        chatId: update.message.chat.id,
        username: update.message.from.username,
        firstName: update.message.from.first_name,
        lastName: update.message.from.last_name
      };

      // Process the message
      const response = await TelegramBotService.processMessage(message);
      
      // Send response back to user
      await sendTelegramMessage(message.chatId, response);
    }

    res.status(200).json({ ok: true });
  } catch (error) {
    console.error('Telegram webhook error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Send message to Telegram user
 */
async function sendTelegramMessage(chatId: number, text: string) {
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  
  if (!botToken) {
    console.error('TELEGRAM_BOT_TOKEN not configured');
    return;
  }

  try {
    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: text,
        parse_mode: 'Markdown'
      }),
    });

    if (!response.ok) {
      console.error('Failed to send Telegram message:', await response.text());
    }
  } catch (error) {
    console.error('Error sending Telegram message:', error);
  }
}
