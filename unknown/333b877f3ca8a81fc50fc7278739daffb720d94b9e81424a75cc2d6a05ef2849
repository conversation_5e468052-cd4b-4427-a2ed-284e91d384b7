import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Wallet,
  Copy,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useWallet } from '@/contexts/WalletContext';
import { CrossChainWalletService } from '@/services/crossChainWalletService';
import { 
  SupportedChain, 
  CrossChainWallet, 
  CHAIN_CONFIGS 
} from '@/types/crossChain';
import { toast } from '@/hooks/use-toast';

export const SimpleCrossChainDeposit: React.FC = () => {
  const { user } = useAuth();
  const { wallets: existingSolanaWallets } = useWallet(); // Get existing Solana wallets
  const [crossChainWallets, setCrossChainWallets] = useState<CrossChainWallet[]>([]);
  const [allWallets, setAllWallets] = useState<any[]>([]); // Combined wallets
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [copiedAddress, setCopiedAddress] = useState<string>('');

  useEffect(() => {
    if (user) {
      loadWallets();
    }
  }, [user, existingSolanaWallets]);

  const loadWallets = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Get cross-chain wallets (excluding Solana since we use existing ones)
      let userCrossChainWallets = await CrossChainWalletService.getUserWallets(user.id);

      // Filter out Solana wallets from cross-chain (we'll use existing ones)
      userCrossChainWallets = userCrossChainWallets.filter(w => w.chain !== SupportedChain.SOLANA);

      // If no cross-chain wallets exist, create them
      if (userCrossChainWallets.length === 0) {
        await createDefaultWallets();
        userCrossChainWallets = await CrossChainWalletService.getUserWallets(user.id);
        userCrossChainWallets = userCrossChainWallets.filter(w => w.chain !== SupportedChain.SOLANA);
      }

      setCrossChainWallets(userCrossChainWallets);

      // Combine existing Solana wallets with cross-chain wallets
      const combined = [
        // Convert existing Solana wallets to display format (only first one to avoid duplicates)
        ...(existingSolanaWallets.length > 0 ? [{
          id: existingSolanaWallets[0].id,
          chain: SupportedChain.SOLANA, // Use enum value
          address: existingSolanaWallets[0].address,
          balances: existingSolanaWallets[0].tokens.map(token => ({
            tokenSymbol: token.type,
            balance: token.balance.toString(),
            balanceUSD: token.type === 'USDC' ? token.balance : token.balance * 100 // Rough SOL price
          })),
          isExisting: true // Mark as existing Solana wallet
        }] : []),
        // Add cross-chain wallets (remove any duplicates by chain)
        ...userCrossChainWallets.filter((wallet, index, self) =>
          index === self.findIndex(w => w.chain === wallet.chain)
        ).map(wallet => ({
          ...wallet,
          balances: [
            { tokenSymbol: 'USDC', balance: '0.00', balanceUSD: 0 },
            { tokenSymbol: 'ETH', balance: '0.00', balanceUSD: 0 }
          ]
        }))
      ];

      console.log('🌐 Combined wallets:', combined.map(w => w.chain));
      setAllWallets(combined);

    } catch (error) {
      console.error('Error loading wallets:', error);
      toast({
        title: "Error",
        description: "Failed to load wallets. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const createDefaultWallets = async () => {
    if (!user) return;

    try {
      // Create wallets for ALL supported chains EXCEPT Solana (we use existing Solana wallets)
      const defaultChains = [
        SupportedChain.ETHEREUM,
        SupportedChain.POLYGON,
        SupportedChain.BSC,
        SupportedChain.ARBITRUM,
        SupportedChain.AVALANCHE,
        SupportedChain.BASE
      ];

      console.log('🌐 Creating cross-chain wallets for:', defaultChains);

      for (const chain of defaultChains) {
        console.log(`🔗 Creating wallet for ${chain}...`);
        await CrossChainWalletService.createWallet(user.id, chain);
      }

      toast({
        title: "Cross-Chain Wallets Created! 🌐",
        description: `Created wallets for ${defaultChains.length} additional blockchain networks.`,
      });
    } catch (error) {
      console.error('Error creating default wallets:', error);
      toast({
        title: "Error",
        description: "Failed to create some wallets. Please try again.",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = async (address: string, chain: string) => {
    try {
      await navigator.clipboard.writeText(address);
      setCopiedAddress(address);
      setTimeout(() => setCopiedAddress(''), 2000);
      
      toast({
        title: "Address Copied! 📋",
        description: `${chain} address copied to clipboard`,
      });
    } catch (error) {
      console.error('Failed to copy address:', error);
      toast({
        title: "Copy Failed",
        description: "Failed to copy address to clipboard",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading wallets...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Wallet className="h-5 w-5 text-blue-500" />
          <h2 className="text-lg font-semibold">Cross-Chain Deposit</h2>
        </div>
        <p className="text-muted-foreground mb-4">
          Deposit crypto from multiple blockchain networks. Your funds will be available for off-ramp conversion.
        </p>

        {/* Supported Tokens Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 mb-1">Supported Deposits</h3>
              <p className="text-sm text-blue-700">
                • <strong>Solana:</strong> SOL and USDC supported<br/>
                • <strong>Other chains:</strong> Only USDC supported (Ethereum, Polygon, BSC, Arbitrum, Avalanche, Base)
              </p>
            </div>
          </div>
        </div>

        {/* How It Works */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">How Deposits Work:</h3>
          <div className="space-y-2 text-sm text-gray-700">
            <div className="flex items-start gap-2">
              <span className="font-medium">1.</span>
              <span><strong>Solana (Your Wallet):</strong> Deposits detected automatically within 30 seconds. Shows in dashboard immediately.</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium">2.</span>
              <span><strong>Other Chains:</strong> Deposits require manual refresh. Balance updates when you click refresh button.</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium">3.</span>
              <span><strong>Off-Ramp:</strong> All deposits (any chain) can be converted to NGN in the withdraw section.</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium">4.</span>
              <span><strong>Dashboard:</strong> Currently shows Solana balances only. Cross-chain integration coming soon.</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Wallets */}
      <div className="grid gap-4">
        {allWallets.map((wallet) => {
          // Normalize chain value and get config
          const normalizedChain = typeof wallet.chain === 'string'
            ? wallet.chain.toLowerCase() as SupportedChain
            : wallet.chain;

          const chainConfig = CHAIN_CONFIGS[normalizedChain];

          // Skip if chain config not found
          if (!chainConfig) {
            console.warn('Chain config not found for:', wallet.chain);
            return null;
          }

          const totalValue = wallet.balances.reduce((sum, balance) => sum + balance.balanceUSD, 0);
          
          return (
            <Card key={wallet.id} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <img 
                    src={chainConfig.logo} 
                    alt={chainConfig.name}
                    className="w-8 h-8"
                  />
                  <div>
                    <h3 className="font-medium">
                      {chainConfig.name}
                      {wallet.isExisting && (
                        <Badge variant="default" className="ml-2 text-xs">
                          Your Wallet
                        </Badge>
                      )}
                    </h3>
                    <Badge variant="outline" className="text-xs">
                      {chainConfig.symbol}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-muted-foreground">Portfolio Value</p>
                  <p className="font-medium">${totalValue.toFixed(2)}</p>
                </div>
              </div>

              {/* Deposit Address */}
              <div className="bg-gray-50 border rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <span className="text-sm font-medium">Deposit Address</span>
                    <p className="text-xs text-blue-600 font-medium">
                      {wallet.chain === SupportedChain.SOLANA ? 'Supports: SOL & USDC' : 'Supports: USDC only'}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(wallet.address, chainConfig.name)}
                    >
                      {copiedAddress === wallet.address ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        if (!user) return;

                        try {
                          setRefreshing(true);
                          toast({
                            title: "Refreshing Balance",
                            description: "Checking for new deposits...",
                          });

                          // Force refresh balances for this specific wallet
                          await CrossChainWalletService.updateAllWalletBalances(user.id);

                          // Reload wallets to get updated balances
                          await loadWallets();

                          toast({
                            title: "Balance Updated! ✅",
                            description: "Wallet balances have been refreshed.",
                          });
                        } catch (error) {
                          console.error('Error refreshing balance:', error);
                          toast({
                            title: "Refresh Failed",
                            description: "Failed to refresh balance. Please try again.",
                            variant: "destructive",
                          });
                        } finally {
                          setRefreshing(false);
                        }
                      }}
                      disabled={refreshing}
                    >
                      <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>
                <p className="font-mono text-sm break-all bg-white p-2 rounded border">
                  {wallet.address}
                </p>
              </div>

              {/* Supported Tokens */}
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">You can deposit:</p>
                <div className="flex flex-wrap gap-2">
                  {chainConfig.supportedTokens?.map((token) => (
                    <Badge key={token.symbol} variant="outline" className="text-xs">
                      <img
                        src={token.logo}
                        alt={token.symbol}
                        className="w-3 h-3 mr-1"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/crypto-logos/default.svg';
                        }}
                      />
                      {token.symbol}
                      {token.isNative && (
                        <span className="ml-1 text-blue-600">(Native)</span>
                      )}
                    </Badge>
                  )) || (
                    <Badge variant="outline" className="text-xs">
                      USDC, Native Token
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  All tokens on {chainConfig.name} network can be converted to NGN in the off-ramp.
                </p>
              </div>

              {/* Deposit Detection Status */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium mb-1">Deposit Detection:</p>
                    {wallet.isExisting ? (
                      <p>✅ <strong>Automatic detection enabled</strong> - Deposits will appear in your dashboard within 30 seconds</p>
                    ) : (
                      <p>⚠️ <strong>Manual refresh required</strong> - After depositing, click "Refresh" to update your balance</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Warning */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-700">
                    <p className="font-medium mb-1">Important:</p>
                    <p>Only send tokens on the {chainConfig.name} network to this address. Sending tokens from other networks will result in permanent loss.</p>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {allWallets.length === 0 && (
        <Card className="p-6">
          <div className="text-center">
            <Wallet className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">No wallets found</p>
            <Button onClick={loadWallets}>
              Create Wallets
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};
