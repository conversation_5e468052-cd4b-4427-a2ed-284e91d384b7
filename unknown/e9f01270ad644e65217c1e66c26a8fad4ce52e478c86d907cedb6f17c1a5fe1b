/**
 * Types for the Smart Group Cards feature
 */

export enum GroupCardMemberRole {
  OWNER = 'owner',
  MANAGER = 'manager',
  SPENDER = 'spender',
  VIEWER = 'viewer',
}

export enum GroupCardMemberStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  DECLINED = 'declined',
  REMOVED = 'removed',
}

export enum GroupCardStatus {
  ACTIVE = 'active',
  FROZEN = 'frozen',
  CLOSED = 'closed',
}

export enum GroupCardTransactionStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  DECLINED = 'declined',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DISPUTED = 'disputed',
}

export enum GroupCardSpendingRuleType {
  CATEGORY = 'category',
  MERCHANT = 'merchant',
  TIME = 'time',
  GEO = 'geo',
}

export enum GroupCardFundingStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum GroupCardExpenseSplitStatus {
  PENDING = 'pending',
  PAID = 'paid',
  DECLINED = 'declined',
}

export interface GroupCard {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  virtualCardId?: string; // Reference to the shared virtual card
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  balance: number;
  currency: string;
  status: GroupCardStatus;
  emoji?: string;
  createdAt: Date;
  updatedAt: Date;
  isEmergencyLocked: boolean;
  emergencyLockedBy?: string;
  emergencyLockedAt?: Date;
}

export interface GroupCardMember {
  id: string;
  groupCardId: string;
  userId: string;
  role: GroupCardMemberRole;
  status: GroupCardMemberStatus;
  dailyLimit?: number;
  weeklyLimit?: number;
  monthlyLimit?: number;
  requiresApprovalAbove?: number;
  alwaysRequiresApproval: boolean;
  canFundCard: boolean;
  createdAt: Date;
  updatedAt: Date;

  // UI helpers
  user?: {
    id: string;
    email?: string;
    fullName?: string;
    avatarUrl?: string;
  };
  groupCard?: {
    id: string;
    name: string;
    description?: string;
    emoji?: string;
    ownerId: string;
    ownerName?: string;
  };

  // Invitation-specific fields (when used for invitations)
  invitedEmail?: string;
  inviterUserId?: string;
}

export interface CategoryRule {
  categories: string[];
}

export interface MerchantRule {
  merchants: string[];
}

export interface TimeRule {
  daysOfWeek?: number[]; // 0-6, 0 is Sunday
  startTime?: string; // HH:MM format
  endTime?: string; // HH:MM format
}

export interface GeoRule {
  countries?: string[]; // ISO country codes
  regions?: string[]; // Region/state codes
  cities?: string[]; // City names
  postalCodes?: string[]; // Postal/ZIP codes
}

export type SpendingRuleValue = CategoryRule | MerchantRule | TimeRule | GeoRule;

export interface GroupCardSpendingRule {
  id: string;
  groupCardId: string;
  memberId?: string; // If null, applies to all members
  ruleType: GroupCardSpendingRuleType;
  ruleValue: SpendingRuleValue;
  isWhitelist: boolean; // TRUE = allow only these, FALSE = block these
  createdAt: Date;
  updatedAt: Date;
}

export interface GroupCardTransaction {
  id: string;
  groupCardId: string;
  memberId?: string;
  amount: number;
  merchant?: string;
  category?: string;
  description?: string;
  status: GroupCardTransactionStatus;
  requiresApproval: boolean;
  approvedBy?: string;
  approvedAt?: Date;
  declinedBy?: string;
  declinedAt?: Date;
  location?: {
    latitude?: number;
    longitude?: number;
    country?: string;
    city?: string;
    postalCode?: string;
  };
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;

  // UI helpers
  member?: GroupCardMember;
}

export interface GroupCardFunding {
  id: string;
  groupCardId: string;
  userId: string;
  amount: number;
  walletId?: string;
  tokenType?: string;
  status: GroupCardFundingStatus;
  transactionHash?: string;
  createdAt: Date;
  updatedAt: Date;

  // UI helpers
  user?: {
    id: string;
    email?: string;
    fullName?: string;
    avatarUrl?: string;
  };
}

export interface GroupCardMessage {
  id: string;
  groupCardId: string;
  userId: string;
  message: string;
  transactionId?: string;
  isSystemMessage: boolean;
  createdAt: Date;
  updatedAt: Date;

  // UI helpers
  user?: {
    id: string;
    email?: string;
    fullName?: string;
    avatarUrl?: string;
  };
  transaction?: GroupCardTransaction;
}

export interface GroupCardExpenseSplit {
  id: string;
  transactionId: string;
  memberId: string;
  amount: number;
  status: GroupCardExpenseSplitStatus;
  paidAt?: Date;
  paymentMethod?: string;
  paymentReference?: string;
  createdAt: Date;
  updatedAt: Date;

  // UI helpers
  member?: GroupCardMember;
  transaction?: GroupCardTransaction;
}
