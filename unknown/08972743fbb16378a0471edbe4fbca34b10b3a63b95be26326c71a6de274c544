
import { supabase } from '@/integrations/supabase/client';

/**
 * Upload a file to the user_uploads bucket
 * @param file The file to upload
 * @param folder Optional folder path (will be prefixed with user ID)
 * @returns URL of the uploaded file
 */
export async function uploadUserFile(file: File, folder: string = 'files'): Promise<string | null> {
  try {
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError || !userData.user) {
      console.error('User not authenticated for file upload');
      throw new Error('Authentication required for file upload');
    }
    
    const userId = userData.user.id;
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/${folder}/${Date.now()}.${fileExt}`;
    
    const { data, error } = await supabase
      .storage
      .from('user_uploads')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });
      
    if (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
    
    // Get public URL if needed
    const { data: publicUrlData } = supabase
      .storage
      .from('user_uploads')
      .getPublicUrl(data.path);
      
    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('File upload failed:', error);
    return null;
  }
}

/**
 * Upload a file to the app_assets bucket (requires admin privileges)
 * @param file The file to upload
 * @param path The path where the file should be stored
 * @returns URL of the uploaded file
 */
export async function uploadAppAsset(file: File, path: string): Promise<string | null> {
  try {
    const fileName = `${path}/${file.name}`;
    
    const { data, error } = await supabase
      .storage
      .from('app_assets')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: true
      });
      
    if (error) {
      console.error('Error uploading app asset:', error);
      throw error;
    }
    
    // Get public URL
    const { data: publicUrlData } = supabase
      .storage
      .from('app_assets')
      .getPublicUrl(data.path);
      
    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('App asset upload failed:', error);
    return null;
  }
}

/**
 * Get a list of files in a user's folder
 * @param folder The folder to list
 * @returns Array of files
 */
export async function listUserFiles(folder: string = 'files') {
  try {
    const { data: userData } = await supabase.auth.getUser();
    
    if (!userData.user) {
      throw new Error('Authentication required to list files');
    }
    
    const userId = userData.user.id;
    const path = `${userId}/${folder}`;
    
    const { data, error } = await supabase
      .storage
      .from('user_uploads')
      .list(path);
      
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error listing user files:', error);
    return [];
  }
}

/**
 * Delete a file from the user_uploads bucket
 * @param path Full path of the file to delete
 * @returns Success status
 */
export async function deleteUserFile(path: string): Promise<boolean> {
  try {
    const { data: userData } = await supabase.auth.getUser();
    
    if (!userData.user) {
      throw new Error('Authentication required to delete files');
    }
    
    // Verify the file belongs to the user
    const userId = userData.user.id;
    if (!path.startsWith(`${userId}/`)) {
      throw new Error('Not authorized to delete this file');
    }
    
    const { error } = await supabase
      .storage
      .from('user_uploads')
      .remove([path]);
      
    if (error) {
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
}
