// Netlify Function to proxy CoinGecko API and avoid CORS issues
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const coinIds = [
      'solana',
      'usd-coin',
      'bitcoin',
      'ethereum',
      'matic-network',
      'binancecoin',
      'avalanche-2',
      'arbitrum'
    ];

    const response = await fetch(
      `https://api.coingecko.com/api/v3/simple/price?ids=${coinIds.join(',')}&vs_currencies=usd&include_24hr_change=true`,
      {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'solpay-app/1.0'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Add timestamp for cache control
    const result = {
      data,
      timestamp: new Date().toISOString(),
      source: 'coingecko'
    };

    res.status(200).json(result);
  } catch (error) {
    console.error('Error fetching crypto prices:', error);
    res.status(500).json({ 
      error: 'Failed to fetch crypto prices',
      message: error.message 
    });
  }
}
