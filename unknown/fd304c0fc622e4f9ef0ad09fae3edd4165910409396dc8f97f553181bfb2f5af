/**
 * Off-Ramp Notification Service
 * 
 * Handles all notifications for crypto-to-fiat conversion process
 * Integrates with existing notification infrastructure
 */

import { notificationService, NotificationData } from './notificationService';

export interface OffRampTransaction {
  id: string;
  user_id: string;
  crypto_amount: number;
  crypto_symbol: 'USDC' | 'SOL';
  ngn_amount: number;
  exchange_rate: number;
  bank_name: string;
  account_number: string;
  account_name: string;
  status: 'initiated' | 'processing' | 'completed' | 'failed';
  transaction_reference: string;
  created_at: string;
  completed_at?: string;
  failure_reason?: string;
  // Merchant payment fields
  is_merchant_payment?: boolean;
  merchant_name?: string;
  merchant_id?: string;
  payment_type?: 'merchant_payment' | 'regular_offramp';
}

class OffRampNotificationService {
  /**
   * Send notification when user initiates off-ramp conversion
   */
  async notifyOffRampInitiated(transaction: OffRampTransaction): Promise<void> {
    console.log('📧 OffRampNotificationService: Creating initiation notification for user:', transaction.user_id);

    const isMerchantPayment = transaction.is_merchant_payment;
    const merchantName = transaction.merchant_name;

    const title = isMerchantPayment
      ? '🏪 Merchant Payment Initiated'
      : '🚀 Conversion Request Received';

    const message = isMerchantPayment
      ? `Your payment of ₦${transaction.ngn_amount.toLocaleString()} to ${merchantName} has been initiated and is being processed.`
      : `Your request to convert ${transaction.crypto_amount} ${transaction.crypto_symbol} to ₦${transaction.ngn_amount.toLocaleString()} has been received and is being processed.`;

    const notification: NotificationData = {
      user_id: transaction.user_id,
      type: isMerchantPayment ? 'merchant_payment' : 'off_ramp_initiated',
      title,
      message,
      data: {
        transaction_id: transaction.id,
        crypto_amount: transaction.crypto_amount,
        crypto_symbol: transaction.crypto_symbol,
        ngn_amount: transaction.ngn_amount,
        bank_name: transaction.bank_name,
        account_number: transaction.account_number,
        reference: transaction.transaction_reference,
        exchange_rate: transaction.exchange_rate,
        is_merchant_payment: isMerchantPayment,
        merchant_name: merchantName,
        merchant_id: transaction.merchant_id,
        payment_type: transaction.payment_type
      },
      priority: 'medium',
      channels: ['in_app', 'email']
    };

    console.log('📧 OffRampNotificationService: Sending notification via notificationService');
    await notificationService.sendNotification(notification);
    console.log('✅ OffRampNotificationService: Initiation notification sent successfully');
  }

  /**
   * Send notification when conversion starts processing
   */
  async notifyOffRampProcessing(transaction: OffRampTransaction): Promise<void> {
    const isMerchantPayment = transaction.is_merchant_payment;
    const merchantName = transaction.merchant_name;

    const title = isMerchantPayment
      ? '⚡ Processing Merchant Payment'
      : '⚡ Processing Your Conversion';

    const message = isMerchantPayment
      ? `Your payment to ${merchantName} is now being processed. Funds will be sent to ${transaction.bank_name} account ending in ${transaction.account_number.slice(-4)}.`
      : `Your ${transaction.crypto_symbol} to Naira conversion is now being processed. Funds will be sent to ${transaction.bank_name} account ending in ${transaction.account_number.slice(-4)}.`;

    const notification: NotificationData = {
      user_id: transaction.user_id,
      type: isMerchantPayment ? 'merchant_payment' : 'off_ramp_processing',
      title,
      message,
      data: {
        transaction_id: transaction.id,
        crypto_amount: transaction.crypto_amount,
        crypto_symbol: transaction.crypto_symbol,
        ngn_amount: transaction.ngn_amount,
        bank_name: transaction.bank_name,
        account_number: transaction.account_number,
        reference: transaction.transaction_reference,
        estimated_completion: this.getEstimatedCompletion(),
        is_merchant_payment: isMerchantPayment,
        merchant_name: merchantName,
        merchant_id: transaction.merchant_id,
        payment_type: transaction.payment_type
      },
      priority: 'medium',
      channels: ['in_app', 'email']
    };

    await notificationService.sendNotification(notification);
  }

  /**
   * Send notification when conversion is completed successfully
   */
  async notifyOffRampCompleted(transaction: OffRampTransaction): Promise<void> {
    const isMerchantPayment = transaction.is_merchant_payment;
    const merchantName = transaction.merchant_name;

    const title = isMerchantPayment
      ? '✅ Merchant Payment Completed!'
      : '✅ Conversion Completed Successfully!';

    const message = isMerchantPayment
      ? `Your payment of ₦${transaction.ngn_amount.toLocaleString()} to ${merchantName} has been completed successfully. Transaction completed in ${this.getTransactionDuration(transaction)}.`
      : `₦${transaction.ngn_amount.toLocaleString()} has been successfully sent to your ${transaction.bank_name} account. Transaction completed in ${this.getTransactionDuration(transaction)}.`;

    const notification: NotificationData = {
      user_id: transaction.user_id,
      type: isMerchantPayment ? 'merchant_payment' : 'off_ramp_completed',
      title,
      message,
      data: {
        transaction_id: transaction.id,
        crypto_amount: transaction.crypto_amount,
        crypto_symbol: transaction.crypto_symbol,
        ngn_amount: transaction.ngn_amount,
        bank_name: transaction.bank_name,
        account_number: transaction.account_number,
        reference: transaction.transaction_reference,
        completed_at: transaction.completed_at,
        duration: this.getTransactionDuration(transaction),
        is_merchant_payment: isMerchantPayment,
        merchant_name: merchantName,
        merchant_id: transaction.merchant_id,
        payment_type: transaction.payment_type
      },
      priority: 'high',
      channels: ['in_app', 'email', 'push']
    };

    await notificationService.sendNotification(notification);
  }

  /**
   * Send notification when conversion fails
   */
  async notifyOffRampFailed(transaction: OffRampTransaction): Promise<void> {
    const notification: NotificationData = {
      user_id: transaction.user_id,
      type: 'off_ramp_failed',
      title: '❌ Conversion Failed',
      message: `Your ${transaction.crypto_symbol} to Naira conversion failed. ${transaction.failure_reason || 'Please contact support for assistance.'}`,
      data: {
        transaction_id: transaction.id,
        crypto_amount: transaction.crypto_amount,
        crypto_symbol: transaction.crypto_symbol,
        ngn_amount: transaction.ngn_amount,
        bank_name: transaction.bank_name,
        reference: transaction.transaction_reference,
        failure_reason: transaction.failure_reason,
        support_contact: '<EMAIL>'
      },
      priority: 'urgent',
      channels: ['in_app', 'email', 'push']
    };

    await notificationService.sendNotification(notification);
  }

  /**
   * Get estimated completion time (typically 30-60 seconds)
   */
  private getEstimatedCompletion(): string {
    const now = new Date();
    const estimated = new Date(now.getTime() + 45 * 1000); // 45 seconds from now
    return estimated.toISOString();
  }

  /**
   * Calculate transaction duration
   */
  private getTransactionDuration(transaction: OffRampTransaction): string {
    if (!transaction.completed_at) return 'Unknown';
    
    const start = new Date(transaction.created_at);
    const end = new Date(transaction.completed_at);
    const durationMs = end.getTime() - start.getTime();
    const durationSeconds = Math.floor(durationMs / 1000);
    
    if (durationSeconds < 60) {
      return `${durationSeconds} seconds`;
    } else {
      const minutes = Math.floor(durationSeconds / 60);
      const seconds = durationSeconds % 60;
      return `${minutes}m ${seconds}s`;
    }
  }

  /**
   * Send batch notification for multiple transactions (admin use)
   */
  async notifyBatchProcessing(transactions: OffRampTransaction[]): Promise<void> {
    const promises = transactions.map(transaction => {
      switch (transaction.status) {
        case 'processing':
          return this.notifyOffRampProcessing(transaction);
        case 'completed':
          return this.notifyOffRampCompleted(transaction);
        case 'failed':
          return this.notifyOffRampFailed(transaction);
        default:
          return Promise.resolve();
      }
    });

    await Promise.allSettled(promises);
  }
}

export const offRampNotificationService = new OffRampNotificationService();
