import React, { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { SupportedChain, CHAIN_CONFIGS, CrossChainWallet } from '@/types/crossChain';
import { crossChainPricingService, TokenPrice } from '@/services/crossChainPricingService';

interface CrossChainTokenSelectorProps {
  selectedChain: SupportedChain;
  selectedToken: string;
  onChainChange: (chain: SupportedChain) => void;
  onTokenChange: (token: string) => void;
  availableChains: SupportedChain[];
  label?: string;
  showBalances?: boolean;
  crossChainWallets?: CrossChainWallet[];
}

export const CrossChainTokenSelector: React.FC<CrossChainTokenSelectorProps> = ({
  selectedChain,
  selectedToken,
  onChainChange,
  onTokenChange,
  availableChains,
  label = "Select Network & Token",
  showBalances = false,
  crossChainWallets = []
}) => {
  const chainConfig = CHAIN_CONFIGS[selectedChain];
  const availableTokens = chainConfig?.supportedTokens || [];
  const [tokenPrices, setTokenPrices] = useState<Map<string, TokenPrice>>(new Map());
  const [loading, setLoading] = useState(false);

  // Get wallet balance for selected chain and token
  const getWalletBalance = (chain: SupportedChain, token: string): string => {
    const wallet = crossChainWallets.find(w => w.chain === chain);
    if (!wallet) return '0';

    const balance = wallet.balances.find(b => b.tokenSymbol === token);
    return balance ? parseFloat(balance.balance).toFixed(6) : '0';
  };

  // Load prices for a specific chain
  const loadPricesForChain = async (chain: SupportedChain) => {
    const chainConfig = CHAIN_CONFIGS[chain];
    if (!chainConfig) return;

    setLoading(true);
    try {
      const pricePromises = chainConfig.supportedTokens.map(token =>
        crossChainPricingService.getTokenPrice(token.symbol, chain)
      );

      const prices = await Promise.all(pricePromises);
      const priceMap = new Map<string, TokenPrice>();

      prices.forEach((price, index) => {
        if (price) {
          priceMap.set(chainConfig.supportedTokens[index].symbol, price);
        }
      });

      setTokenPrices(priceMap);
      console.log(`✅ Loaded ${priceMap.size} token prices for ${chain}`);
    } catch (error) {
      console.error('❌ Error loading token prices:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load token prices when chain changes and subscribe to real-time updates
  useEffect(() => {
    if (!chainConfig) return;

    loadPricesForChain(selectedChain);

    // Subscribe to real-time price updates for all tokens
    const unsubscribeFunctions = availableTokens.map(token =>
      crossChainPricingService.subscribeToPrice(token.symbol, (updatedPrice) => {
        console.log(`📈 Real-time price update: ${token.symbol} = $${updatedPrice.priceUSD}`);
        setTokenPrices(prev => {
          const newMap = new Map(prev);
          newMap.set(token.symbol, updatedPrice);
          return newMap;
        });
      })
    );

    // Cleanup subscriptions
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }, [selectedChain, chainConfig, availableTokens]);



  // When chain changes, auto-select appropriate token and load prices immediately
  const handleChainChange = (newChain: SupportedChain) => {
    onChainChange(newChain);

    const newChainConfig = CHAIN_CONFIGS[newChain];
    const newTokens = newChainConfig?.supportedTokens || [];

    // Auto-select USDC if available, otherwise select first token
    const usdcToken = newTokens.find(t => t.symbol === 'USDC');
    const defaultToken = usdcToken || newTokens[0];

    if (defaultToken) {
      onTokenChange(defaultToken.symbol);

      // Immediately load prices for the new chain
      loadPricesForChain(newChain);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>{label}</Label>
      </div>
      
      {/* Chain Selection */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label className="text-sm text-muted-foreground">Network</Label>
          <Select value={selectedChain} onValueChange={handleChainChange}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableChains.map((chain) => {
                const config = CHAIN_CONFIGS[chain];
                if (!config) return null;
                
                return (
                  <SelectItem key={chain} value={chain}>
                    <div className="flex items-center gap-2">
                      <img 
                        src={config.logo} 
                        alt={config.name}
                        className="w-4 h-4" 
                      />
                      <span>{config.name}</span>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {/* Token Selection */}
        <div className="space-y-2">
          <Label className="text-sm text-muted-foreground">Token</Label>
          <Select value={selectedToken} onValueChange={onTokenChange}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availableTokens.map((token) => {
                const price = tokenPrices.get(token.symbol);
                return (
                  <SelectItem key={token.symbol} value={token.symbol}>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <img
                          src={token.logo}
                          alt={token.symbol}
                          className="w-4 h-4"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/crypto-logos/default.svg';
                          }}
                        />
                        <span>{token.symbol}</span>
                        {token.isNative && (
                          <Badge variant="outline" className="text-xs">
                            Native
                          </Badge>
                        )}
                      </div>
                      <div className="text-right">
                        {price && !loading ? (
                          <div className="text-xs">
                            <div className="font-medium">
                              {crossChainPricingService.formatPrice(price)}
                            </div>
                            {price.change24h !== undefined && (
                              <div className={crossChainPricingService.getPriceChangeColor(price.change24h)}>
                                {crossChainPricingService.formatPriceChange(price.change24h)}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-xs text-muted-foreground">Loading...</div>
                        )}
                      </div>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Token Info with Pricing */}
      {chainConfig && selectedToken && (
        <div className="bg-gray-50 border rounded-lg p-4">
          <div className="flex items-center justify-between text-sm mb-3">
            <div className="flex items-center gap-2">
              <img
                src={chainConfig.logo}
                alt={chainConfig.name}
                className="w-4 h-4"
              />
              <span className="font-medium">{chainConfig.name}</span>
            </div>
            <div className="flex items-center gap-2">
              {availableTokens.find(t => t.symbol === selectedToken) && (
                <>
                  <img
                    src={availableTokens.find(t => t.symbol === selectedToken)?.logo}
                    alt={selectedToken}
                    className="w-4 h-4"
                  />
                  <span className="font-medium">{selectedToken}</span>
                </>
              )}
            </div>
          </div>

          {/* Current Price Display */}
          {tokenPrices.get(selectedToken) && (
            <div className="bg-white border rounded-lg p-3 mb-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-muted-foreground">Current Price</p>
                  <p className="text-lg font-semibold">
                    {crossChainPricingService.formatPrice(tokenPrices.get(selectedToken)!)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-muted-foreground">24h Change</p>
                  <p className={`text-sm font-medium ${crossChainPricingService.getPriceChangeColor(tokenPrices.get(selectedToken)!.change24h)}`}>
                    {crossChainPricingService.formatPriceChange(tokenPrices.get(selectedToken)!.change24h)}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Wallet Balance Display */}
          {showBalances && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
              <p className="text-xs text-green-600 font-medium mb-1">Available Balance:</p>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {getWalletBalance(selectedChain, selectedToken)} {selectedToken}
                </span>
                {tokenPrices.get(selectedToken) && (
                  <span className="text-xs text-muted-foreground">
                    ≈ ${(parseFloat(getWalletBalance(selectedChain, selectedToken)) * tokenPrices.get(selectedToken)!.priceUSD).toFixed(2)}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Example Conversion */}
          {tokenPrices.get(selectedToken) && selectedToken !== 'USDC' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
              <p className="text-xs text-blue-600 font-medium mb-1">Example Conversion:</p>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>0.5 {selectedToken}</span>
                  <span className="font-medium">
                    ${(0.5 * tokenPrices.get(selectedToken)!.priceUSD).toFixed(2)} USDC
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>1.0 {selectedToken}</span>
                  <span className="font-medium">
                    ${tokenPrices.get(selectedToken)!.priceUSD.toFixed(2)} USDC
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            {selectedToken === 'USDC' ? (
              <span>✅ USDC is a stablecoin pegged to $1.00 USD</span>
            ) : (
              <span>✅ You can deposit {selectedToken} on {chainConfig.name} network and convert to USDC/NGN</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
