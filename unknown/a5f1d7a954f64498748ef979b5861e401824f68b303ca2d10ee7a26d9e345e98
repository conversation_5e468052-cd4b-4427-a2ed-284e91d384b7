
/**
 * Off-Ramp Widget - Convert Crypto to NGN
 * 
 * Beautiful interface for users to withdraw SOL/USDC to Nigerian bank accounts
 */

import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowDownUp,
  Banknote,
  Clock,
  Shield,
  Zap,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Building,
  CreditCard,
  Lock,
  Star,
  Sparkles,
  Info,
  RefreshCw
} from 'lucide-react';
import { useWallet } from '@/contexts/WalletContext';
import { CryptoType } from '@/types/wallet';
import { toast } from '@/hooks/use-toast';
import { exchangeRateService, ExchangeRate, RateLock, formatRate } from '@/services/exchangeRateService';
import { offRampProcessingService } from '@/services/offRampProcessingService';
import { paystackService } from '@/services/paystackService';
import RateLockWidget from './RateLockWidget';
import { useAuth } from '@/contexts/AuthContext';
import { bankAccountService } from '@/services/bankAccountService';
import { FEATURE_FLAGS } from '@/config/features';
import { offRampNotificationService } from '@/services/offRampNotificationService';
import { CrossChainWalletService } from '@/services/crossChainWalletService';
import { CrossChainOffRampService } from '@/services/crossChainOffRampService';
import { SupportedChain, CHAIN_CONFIGS, CrossChainWallet } from '@/types/crossChain';
import { CrossChainTokenSelector } from '@/components/cross-chain/CrossChainTokenSelector';

interface ProcessingStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  duration?: number;
}

interface BankAccount {
  bankName: string;
  accountNumber: string;
  accountName: string;
}

export default function OffRampWidget() {
  const { wallets, isLoading } = useWallet();
  const { user } = useAuth();
  const location = useLocation();

  // Feature flag for real transactions - controlled by config file
  const REAL_TRANSACTIONS_ENABLED = FEATURE_FLAGS.REAL_TRANSACTIONS_ENABLED;
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoType>(CryptoType.USDC);
  const [cryptoAmount, setCryptoAmount] = useState('');
  const [ngnAmount, setNgnAmount] = useState('');
  const [bankAccount, setBankAccount] = useState<BankAccount>({
    bankName: '',
    accountNumber: '',
    accountName: ''
  });
  const [currentRate, setCurrentRate] = useState<ExchangeRate | null>(null);
  const [rateLock, setRateLock] = useState<RateLock | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [step, setStep] = useState<'amount' | 'rate' | 'bank' | 'confirm' | 'processing'>('amount');
  const [isMerchantPayment, setIsMerchantPayment] = useState(false);
  const [merchantData, setMerchantData] = useState<any>(null);
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([]);
  const [showRateLock, setShowRateLock] = useState(false);
  const [nigerianBanks, setNigerianBanks] = useState<Array<{ code: string; name: string }>>([]);
  const [savedAccounts, setSavedAccounts] = useState<any[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string>('');

  // Cross-chain state
  const [crossChainWallets, setCrossChainWallets] = useState<any[]>([]);
  const [selectedChain, setSelectedChain] = useState<SupportedChain>(SupportedChain.SOLANA);
  const [showCrossChainOptions, setShowCrossChainOptions] = useState(false); // Disabled
  const [isRefreshingBalances, setIsRefreshingBalances] = useState(false);

  // Handle merchant payment initialization
  useEffect(() => {
    const locationState = location.state as any;
    const storedMerchantData = localStorage.getItem('merchantPaymentData');

    if (locationState?.merchantPayment || storedMerchantData) {
      console.log('🏪 Merchant payment detected, initializing off-ramp...');
      setIsMerchantPayment(true);

      let merchantInfo = null;
      if (storedMerchantData) {
        merchantInfo = JSON.parse(storedMerchantData);
        setMerchantData(merchantInfo);
      } else if (locationState?.merchantData) {
        merchantInfo = locationState.merchantData;
        setMerchantData(merchantInfo);
      }

      // Pre-fill form with merchant payment data
      if (locationState?.prefilledAmount) {
        setNgnAmount(locationState.prefilledAmount);
      }

      if (locationState?.prefilledCrypto) {
        setSelectedCrypto(locationState.prefilledCrypto === 'SOL' ? CryptoType.SOL : CryptoType.USDC);
      }

      if (locationState?.prefilledBankAccount) {
        setBankAccount(locationState.prefilledBankAccount);
      }

      // Clear stored data after use
      if (storedMerchantData) {
        localStorage.removeItem('merchantPaymentData');
      }
    }
  }, [location]);



  // Get available tokens for selected chain
  const getAvailableTokensForChain = (chain: SupportedChain): string[] => {
    const chainConfig = CHAIN_CONFIGS[chain];
    return chainConfig?.supportedTokens?.map(token => token.symbol) || [];
  };

  // Update selected token when chain changes
  const handleChainChange = (newChain: SupportedChain) => {
    setSelectedChain(newChain);

    // Auto-select appropriate token for the new chain
    const availableTokens = getAvailableTokensForChain(newChain);
    if (availableTokens.length > 0) {
      // Prefer USDC if available, otherwise use first token
      const preferredToken = availableTokens.includes('USDC') ? 'USDC' : availableTokens[0];
      setSelectedCrypto(preferredToken as CryptoType);
    }
  };

  // Get current wallet and available balance based on selected chain
  const getAvailableBalance = (): number => {
    if (selectedChain === SupportedChain.SOLANA) {
      // For Solana, use the existing wallet system
      const currentWallet = wallets.find(w => w.isDefault) || wallets[0];
      const selectedToken = currentWallet?.tokens.find(t => t.type === selectedCrypto);
      return selectedToken?.balance || 0;
    } else {
      // For other chains, use cross-chain wallet data
      const wallet = crossChainWallets.find(w => w.chain === selectedChain);
      if (!wallet) return 0;

      const balance = wallet.balances.find(b => b.tokenSymbol === selectedCrypto);
      return balance ? parseFloat(balance.balance) : 0;
    }
  };

  const availableBalance = getAvailableBalance();

  // Manual balance refresh function
  const refreshBalances = async () => {
    if (!user) return;

    setIsRefreshingBalances(true);
    try {
      console.log('🔄 Manually refreshing all wallet balances...');
      await CrossChainWalletService.updateAllWalletBalances(user.id);

      // Reload wallets to get updated balances
      const updatedWallets = await CrossChainWalletService.getUserWallets(user.id);
      const updatedCombinedWallets = [
        // Add existing Solana wallet
        ...(wallets.length > 0 ? [{
          id: wallets[0].id,
          chain: SupportedChain.SOLANA,
          address: wallets[0].address,
          balances: wallets[0].tokens.map(token => ({
            tokenSymbol: token.type,
            balance: token.balance.toString(),
            balanceUSD: token.type === 'USDC' ? token.balance : token.balance * 100
          })),
          isExisting: true
        }] : []),
        // Add updated cross-chain wallets
        ...updatedWallets.filter(w => w.chain !== SupportedChain.SOLANA)
      ];
      setCrossChainWallets(updatedCombinedWallets);

      toast({
        title: "Balances Updated! 🔄",
        description: "All wallet balances have been refreshed.",
      });
      console.log('✅ Manual balance refresh completed successfully');
    } catch (error) {
      console.error('❌ Error during manual balance refresh:', error);
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh balances. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshingBalances(false);
    }
  };

  // Load cross-chain wallets (excluding Solana since we use existing ones)
  useEffect(() => {
    const loadCrossChainWallets = async () => {
      if (user) {
        try {
          console.log('🔄 Loading cross-chain wallets (non-blocking)...');
          let crossChainWallets = await CrossChainWalletService.getUserWallets(user.id);

          // Filter out Solana wallets (we use existing ones)
          crossChainWallets = crossChainWallets.filter(w => w.chain !== SupportedChain.SOLANA);

          // If no cross-chain wallets exist, create them
          if (crossChainWallets.length === 0) {
            const defaultChains = [
              SupportedChain.ETHEREUM,
              SupportedChain.POLYGON,
              SupportedChain.BSC,
              SupportedChain.ARBITRUM,
              SupportedChain.AVALANCHE,
              SupportedChain.BASE
            ];

            for (const chain of defaultChains) {
              await CrossChainWalletService.createWallet(user.id, chain);
            }

            crossChainWallets = await CrossChainWalletService.getUserWallets(user.id);
            crossChainWallets = crossChainWallets.filter(w => w.chain !== SupportedChain.SOLANA);
          }

          // Add existing Solana wallet to the list for display
          const combinedWallets = [
            // Add existing Solana wallet
            ...(wallets.length > 0 ? [{
              id: wallets[0].id,
              chain: SupportedChain.SOLANA,
              address: wallets[0].address,
              balances: wallets[0].tokens.map(token => ({
                tokenSymbol: token.type,
                balance: token.balance.toString(),
                balanceUSD: token.type === 'USDC' ? token.balance : token.balance * 100
              })),
              isExisting: true
            }] : []),
            // Add cross-chain wallets
            ...crossChainWallets
          ];

          setCrossChainWallets(combinedWallets);

          // Update balances for all cross-chain wallets
          console.log('🔄 Updating cross-chain wallet balances...');
          try {
            await CrossChainWalletService.updateAllWalletBalances(user.id);
            // Reload wallets to get updated balances
            const updatedWallets = await CrossChainWalletService.getUserWallets(user.id);
            const updatedCombinedWallets = [
              // Add existing Solana wallet
              ...(wallets.length > 0 ? [{
                id: wallets[0].id,
                chain: SupportedChain.SOLANA,
                address: wallets[0].address,
                balances: wallets[0].tokens.map(token => ({
                  tokenSymbol: token.type,
                  balance: token.balance.toString(),
                  balanceUSD: token.type === 'USDC' ? token.balance : token.balance * 100
                })),
                isExisting: true
              }] : []),
              // Add updated cross-chain wallets
              ...updatedWallets.filter(w => w.chain !== SupportedChain.SOLANA)
            ];
            setCrossChainWallets(updatedCombinedWallets);
            console.log('✅ Cross-chain wallet balances updated successfully');
          } catch (balanceError) {
            console.error('⚠️ Error updating balances, using cached data:', balanceError);
          }
        } catch (error) {
          console.error('⚠️ Cross-chain wallet loading failed (non-critical):', error);
          // Set empty wallets so the rest of the app continues working
          setCrossChainWallets([]);
        }
      } else {
        // No user, set empty wallets
        setCrossChainWallets([]);
      }
    };

    // Run cross-chain loading in background without blocking main functionality
    setTimeout(() => {
      loadCrossChainWallets().catch(error => {
        console.warn('⚠️ Background cross-chain loading failed:', error);
        setCrossChainWallets([]);
      });
    }, 1000); // Delay to let main functionality load first
  }, [user, wallets]);

  // Load exchange rates
  useEffect(() => {
    const loadRate = async () => {
      const [ngnRate, usdRate] = await Promise.all([
        exchangeRateService.getExchangeRate(selectedCrypto, 'NGN'),
        exchangeRateService.getExchangeRate(selectedCrypto, 'USD')
      ]);
      setCurrentRate(ngnRate);
      // Store USD rate for display purposes
      if (usdRate) {
        (ngnRate as any).usdRate = usdRate.rate;
      }
    };

    loadRate();

    // Subscribe to rate updates
    const unsubscribe = exchangeRateService.subscribeToRateUpdates(
      selectedCrypto,
      'NGN',
      (rate) => {
        if (!rateLock) { // Only update if no rate is locked
          setCurrentRate(rate);
        }
      }
    );

    return unsubscribe;
  }, [selectedCrypto, rateLock]);

  // Load Nigerian banks
  useEffect(() => {
    const loadBanks = async () => {
      const banks = await paystackService.getNigerianBanks();
      setNigerianBanks(banks);
    };
    loadBanks();
  }, []);

  // Load saved bank accounts
  useEffect(() => {
    const loadSavedAccounts = async () => {
      if (user) {
        const accounts = await bankAccountService.getUserBankAccounts(user.id);
        setSavedAccounts(accounts);

        // Auto-select default account if available
        const defaultAccount = accounts.find(acc => acc.isDefault);
        if (defaultAccount) {
          setSelectedAccountId(defaultAccount.id);
          setBankAccount({
            bankName: defaultAccount.bankName,
            accountNumber: defaultAccount.accountNumber,
            accountName: defaultAccount.accountName
          });
        }
      }
    };
    loadSavedAccounts();
  }, [user]);

  const effectiveRate = rateLock ? rateLock.lockedRate : (currentRate?.rate || 0);

  // Calculate NGN amount when crypto amount changes
  useEffect(() => {
    if (cryptoAmount && effectiveRate) {
      const ngnValue = parseFloat(cryptoAmount) * effectiveRate;
      setNgnAmount(ngnValue.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }));
    } else {
      setNgnAmount('');
    }
  }, [cryptoAmount, effectiveRate]);

  const handleCryptoAmountChange = (value: string) => {
    // Only allow numbers and decimal point
    if (/^\d*\.?\d*$/.test(value)) {
      setCryptoAmount(value);
    }
  };

  const handleMaxClick = () => {
    setCryptoAmount(availableBalance.toString());
  };

  const handleContinue = () => {
    if (step === 'amount') {
      if (!cryptoAmount || parseFloat(cryptoAmount) <= 0) {
        toast({
          title: "Invalid Amount",
          description: "Please enter a valid amount to withdraw",
          variant: "destructive",
        });
        return;
      }
      if (parseFloat(cryptoAmount) > availableBalance) {
        toast({
          title: "Insufficient Balance",
          description: "You don't have enough balance for this withdrawal",
          variant: "destructive",
        });
        return;
      }
      setStep('rate');
    } else if (step === 'rate') {
      setStep('bank');
    } else if (step === 'bank') {
      if (!selectedAccountId) {
        toast({
          title: "No Account Selected",
          description: "Please select a bank account or add one in the Accounts tab",
          variant: "destructive",
        });
        return;
      }
      setStep('confirm');
    } else if (step === 'confirm') {
      processWithdrawal();
    }
  };

  const processWithdrawal = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to process withdrawals",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setStep('processing');

    // Check if real transactions are enabled
    if (!REAL_TRANSACTIONS_ENABLED) {
      toast({
        title: "Demo Mode Only 🚧",
        description: "Real transactions are disabled until Paystack business registration is complete. This is a demo transaction.",
        variant: "default",
      });
      // Continue with demo transaction
    }

    // Debug validation data
    console.log('🔍 Validation Debug:', {
      bankAccount,
      ngnAmount,
      cryptoAmount,
      selectedCrypto,
      step,
      currentRate
    });

    // Convert ngnAmount from formatted string to number for validation
    const ngnAmountNumber = parseFloat(ngnAmount.replace(/,/g, ''));

    // Simple validation before processing
    if (!bankAccount.bankName || !ngnAmount || ngnAmountNumber < 1000) {
      console.log('❌ Validation failed:', {
        hasBankName: !!bankAccount.bankName,
        bankName: bankAccount.bankName,
        ngnAmount,
        ngnAmountNumber,
        isNgnAmountValid: ngnAmountNumber >= 1000
      });

      toast({
        title: "Invalid Transaction",
        description: `Validation failed: ${!bankAccount.bankName ? 'No bank selected' : ''} ${!ngnAmount ? 'No amount' : ''} ${ngnAmountNumber < 1000 ? 'Amount too low (min ₦1,000)' : ''}`,
        variant: "destructive",
      });
      setIsProcessing(false);
      setStep('amount');
      return;
    }

    console.log('✅ Validation passed, proceeding with transaction');

    // Initialize processing steps
    const steps: ProcessingStep[] = [
      {
        id: 'validate',
        title: 'Validating Transaction',
        description: 'Checking account details and balance',
        status: 'processing'
      },
      {
        id: 'convert',
        title: 'Converting Crypto',
        description: `Converting ${cryptoAmount} ${selectedCrypto} from ${CHAIN_CONFIGS[selectedChain]?.name} to NGN`,
        status: 'pending'
      },
      {
        id: 'transfer',
        title: 'Bank Transfer',
        description: `Sending ₦${ngnAmount} to ${bankAccount.bankName}`,
        status: 'pending'
      },
      {
        id: 'complete',
        title: 'Transaction Complete',
        description: 'Funds delivered to your account',
        status: 'pending'
      }
    ];

    setProcessingSteps(steps);

    // Generate transaction reference with chain prefix
    const chainPrefix = selectedChain === SupportedChain.SOLANA ? 'SOLPAY' :
                       selectedChain === SupportedChain.BASE ? 'BASEPAY' :
                       selectedChain === SupportedChain.ETHEREUM ? 'ETHPAY' :
                       selectedChain === SupportedChain.POLYGON ? 'MATPAY' :
                       selectedChain === SupportedChain.BSC ? 'BNBPAY' :
                       selectedChain === SupportedChain.ARBITRUM ? 'ARBPAY' :
                       selectedChain === SupportedChain.AVALANCHE ? 'AVAXPAY' : 'XPAY';

    const transactionReference = `${chainPrefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

    // Create transaction object for notifications
    console.log('👤 Current user for notifications:', user);
    console.log('🏦 Selected bank for notifications:', bankAccount);
    console.log('🔗 Selected chain for transaction:', selectedChain);
    console.log('🏪 Merchant payment context:', { isMerchantPayment, merchantData });

    const transaction = {
      id: transactionReference,
      user_id: user.id,
      crypto_amount: parseFloat(cryptoAmount),
      crypto_symbol: selectedCrypto,
      chain: selectedChain,
      ngn_amount: ngnAmountNumber,
      exchange_rate: currentRate?.rate || 0,
      bank_name: bankAccount.bankName,
      account_number: bankAccount.accountNumber,
      account_name: bankAccount.accountName,
      status: 'initiated' as const,
      transaction_reference: transactionReference,
      created_at: new Date().toISOString(),
      // Add merchant payment context
      is_merchant_payment: isMerchantPayment,
      merchant_name: merchantData?.merchantName || null,
      merchant_id: merchantData?.merchantId || null,
      payment_type: isMerchantPayment ? 'merchant_payment' : 'regular_offramp'
    };

    try {
      // Send initiation notification
      console.log('🔔 Sending initiation notification for transaction:', transaction);
      await offRampNotificationService.notifyOffRampInitiated(transaction);
      console.log('✅ Initiation notification sent successfully');

      // Step 1: Validation
      setProcessingSteps(prev => prev.map(step =>
        step.id === 'validate' ? { ...step, status: 'processing' } : step
      ));

      // Get bank code for the selected bank
      const banks = await paystackService.getNigerianBanks();
      const selectedBank = banks.find(bank => bank.name === bankAccount.bankName);

      if (!selectedBank) {
        throw new Error('Bank not found. Please select a valid bank.');
      }

      // Verify bank account using Paystack
      const verification = await paystackService.verifyBankAccount(
        bankAccount.accountNumber,
        selectedBank.code
      );

      if (verification.status !== 'success') {
        throw new Error(`Bank account verification failed: ${verification.message}`);
      }

      setProcessingSteps(prev => prev.map(step =>
        step.id === 'validate'
          ? { ...step, status: 'completed', duration: 2.0 }
          : step.id === 'convert'
          ? { ...step, status: 'processing' }
          : step
      ));

      // Wait a moment before sending processing notification
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Send processing notification
      console.log('🔔 Sending processing notification');
      const processingTransaction = { ...transaction, status: 'processing' as const };
      await offRampNotificationService.notifyOffRampProcessing(processingTransaction);
      console.log('✅ Processing notification sent successfully');

      // Step 2: Process off-ramp
      const offRampRequest = {
        userId: user.id,
        cryptoAmount: parseFloat(cryptoAmount),
        cryptoSymbol: selectedCrypto,
        chain: selectedChain,
        bankAccount: {
          bankName: bankAccount.bankName,
          bankCode: selectedBank.code,
          accountNumber: bankAccount.accountNumber,
          accountName: verification.data?.account_name || bankAccount.accountName
        },
        rateLockId: rateLock?.id
      };

      const result = await offRampProcessingService.processOffRamp(offRampRequest);

      if (!result.success) {
        throw new Error(result.message);
      }

      setProcessingSteps(prev => prev.map(step =>
        step.id === 'convert'
          ? { ...step, status: 'completed', duration: 3.0 }
          : step.id === 'transfer'
          ? { ...step, status: 'processing' }
          : step
      ));

      // Step 3: Bank Transfer (already initiated in processOffRamp)
      await new Promise(resolve => setTimeout(resolve, 2000));
      setProcessingSteps(prev => prev.map(step =>
        step.id === 'transfer'
          ? { ...step, status: 'completed', duration: 2.0 }
          : step.id === 'complete'
          ? { ...step, status: 'processing' }
          : step
      ));

      // Step 4: Complete
      await new Promise(resolve => setTimeout(resolve, 1000));
      setProcessingSteps(prev => prev.map(step =>
        step.id === 'complete'
          ? { ...step, status: 'completed', duration: 1.0 }
          : step
      ));

      // Wait for processing to complete before sending completion notification
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Send completion notification
      console.log('🔔 Sending completion notification');
      const completedTransaction = {
        ...transaction,
        status: 'completed' as const,
        completed_at: new Date().toISOString()
      };
      await offRampNotificationService.notifyOffRampCompleted(completedTransaction);
      console.log('✅ Completion notification sent successfully');

      // Show success message based on payment provider
      const provider = result.paymentResult?.provider || 'paystack';
      const estimatedArrival = result.paymentResult?.estimatedArrival || '1-2 minutes';

      toast({
        title: "Off-Ramp Successful! 🎉",
        description: `₦${result.ngnAmount?.toLocaleString()} processed via ${provider.charAt(0).toUpperCase() + provider.slice(1)}. Arriving in ${estimatedArrival} to ${bankAccount.bankName} (${bankAccount.accountNumber})`,
      });

      // Reset form after a delay
      setTimeout(() => {
        setCryptoAmount('');
        setNgnAmount('');
        setBankAccount({ bankName: '', accountNumber: '', accountName: '' });
        setRateLock(null);
        setStep('amount');
        setProcessingSteps([]);
      }, 3000);

    } catch (error) {
      console.error('Off-ramp processing error:', error);

      // Send failure notification
      const failedTransaction = {
        ...transaction,
        status: 'failed' as const,
        failure_reason: error instanceof Error ? error.message : "Technical error occurred during processing"
      };
      await offRampNotificationService.notifyOffRampFailed(failedTransaction);

      setProcessingSteps(prev => prev.map(step =>
        step.status === 'processing'
          ? { ...step, status: 'failed' }
          : step
      ));

      toast({
        title: "Off-Ramp Failed",
        description: error instanceof Error ? error.message : "There was an error processing your withdrawal. Please try again.",
        variant: "destructive",
      });

      setTimeout(() => {
        setStep('confirm');
      }, 2000);
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-4">
          <Banknote className="w-6 h-6 text-white" />
        </div>
        <CardTitle className="text-xl">Crypto to Naira</CardTitle>
        <CardDescription>
          Instantly convert your crypto to NGN in your bank account
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Demo Mode Banner */}
        {!REAL_TRANSACTIONS_ENABLED && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <Info className="w-5 h-5 text-blue-600" />
              <div>
                <h4 className="font-medium text-blue-900">Demo Mode</h4>
                <p className="text-sm text-blue-700">
                  Real transactions are disabled until Paystack business registration is complete.
                  You can test the interface, but no actual money will be transferred.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Step Indicator */}
        <div className="flex items-center justify-center space-x-1 mb-6 overflow-x-auto">
          {['amount', 'rate', 'bank', 'confirm'].map((stepName, index) => (
            <div key={stepName} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                step === stepName ? 'bg-primary text-primary-foreground' :
                ['amount', 'rate', 'bank', 'confirm'].indexOf(step) > index ? 'bg-green-500 text-white' :
                'bg-muted text-muted-foreground'
              }`}>
                {['amount', 'rate', 'bank', 'confirm'].indexOf(step) > index ? (
                  <CheckCircle className="w-4 h-4" />
                ) : stepName === 'rate' ? (
                  <TrendingUp className="w-4 h-4" />
                ) : (
                  index + 1
                )}
              </div>
              {index < 3 && (
                <div className={`w-6 h-0.5 ${
                  ['amount', 'rate', 'bank', 'confirm'].indexOf(step) > index ? 'bg-green-500' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Amount Step */}
        {step === 'amount' && (
          <div className="space-y-4">
            {/* Cross-Chain Token Selection */}
            <CrossChainTokenSelector
              selectedChain={selectedChain}
              selectedToken={selectedCrypto}
              onChainChange={handleChainChange}
              onTokenChange={(token) => setSelectedCrypto(token as CryptoType)}
              availableChains={Array.from(new Set(crossChainWallets.map(w => w.chain)))}
              label="Convert From"
              showBalances={true}
              crossChainWallets={crossChainWallets}
            />

            {/* Balance Refresh Button */}
            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshBalances}
                disabled={isRefreshingBalances}
                className="text-xs"
              >
                {isRefreshingBalances ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-2"></div>
                    Refreshing...
                  </>
                ) : (
                  <>
                    🔄 Refresh Balances
                  </>
                )}
              </Button>
            </div>

            {/* Network Selection Info */}
            {selectedChain && selectedChain !== SupportedChain.SOLANA && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <img
                    src={CHAIN_CONFIGS[selectedChain]?.logo}
                    alt={CHAIN_CONFIGS[selectedChain]?.name}
                    className="w-4 h-4"
                  />
                  <h4 className="font-medium text-blue-900">
                    Converting from {CHAIN_CONFIGS[selectedChain]?.name}
                  </h4>
                </div>
                <p className="text-sm text-blue-700">
                  {availableBalance > 0 ? (
                    <>You have {availableBalance.toFixed(selectedCrypto === CryptoType.SOL ? 4 : 2)} {selectedCrypto} available on {CHAIN_CONFIGS[selectedChain]?.name}.</>
                  ) : (
                    <>Make sure you have {selectedCrypto} deposited to your {CHAIN_CONFIGS[selectedChain]?.name} wallet first.</>
                  )}
                </p>
              </div>
            )}

            {/* Amount Input */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Amount</Label>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={handleMaxClick}
                  className="text-xs h-6 px-2"
                >
                  MAX
                </Button>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  placeholder="0.00"
                  value={cryptoAmount}
                  onChange={(e) => handleCryptoAmountChange(e.target.value)}
                  className="text-lg font-medium pr-16"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground font-medium">
                  {selectedCrypto}
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                Available: {availableBalance.toFixed(selectedCrypto === CryptoType.SOL ? 4 : 2)} {selectedCrypto}
              </div>
            </div>

            {/* Exchange Rate */}
            <div className="bg-muted/50 rounded-lg p-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Exchange Rate</span>
                <div className="flex items-center gap-1">
                  {rateLock ? (
                    <>
                      <Lock className="w-3 h-3 text-green-500" />
                      <span className="font-medium text-green-600">Locked: 1 {selectedCrypto} = ₦{effectiveRate.toLocaleString()}</span>
                    </>
                  ) : effectiveRate > 0 ? (
                    <>
                      <TrendingUp className="w-3 h-3 text-blue-500" />
                      <span className="font-medium">1 {selectedCrypto} = ₦{effectiveRate.toLocaleString()}</span>
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-3 h-3 text-gray-400 animate-spin" />
                      <span className="text-gray-500">Loading rate...</span>
                    </>
                  )}
                </div>
              </div>
              {selectedCrypto !== 'USDC' && currentRate && (currentRate as any).usdRate && (
                <div className="mt-2 pt-2 border-t border-muted text-xs text-muted-foreground">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <span>{selectedCrypto}</span>
                    <span>→</span>
                    <span className="font-medium">USD</span>
                    <span>→</span>
                    <span className="font-medium">USDC</span>
                    <span>→</span>
                    <span className="font-medium">NGN</span>
                  </div>
                  <div className="text-center">
                    1 {selectedCrypto} = ${(currentRate as any).usdRate.toFixed(2)} = {(currentRate as any).usdRate.toFixed(2)} USDC = ₦{currentRate.rate.toLocaleString()}
                  </div>
                </div>
              )}
              {selectedCrypto === 'USDC' && currentRate && (
                <div className="mt-2 pt-2 border-t border-muted text-xs text-muted-foreground text-center">
                  Direct conversion: 1 USDC = ₦{currentRate.rate.toLocaleString()}
                </div>
              )}
            </div>

            {/* NGN Amount */}
            {ngnAmount && (
              <div className="space-y-2">
                <Label>You'll Receive (NGN)</Label>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-700">
                    ₦{ngnAmount}
                  </div>
                  <div className="text-sm text-green-600 mt-1">
                    Estimated arrival: 30-60 seconds
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Rate Step */}
        {step === 'rate' && (
          <div className="space-y-4">
            <div className="text-center mb-4">
              <h3 className="font-medium">Exchange Rate & Protection</h3>
              <p className="text-sm text-muted-foreground">Lock your rate or use live market rate</p>
            </div>

            <RateLockWidget
              fromCurrency={selectedCrypto}
              toCurrency="NGN"
              amount={parseFloat(cryptoAmount) || 0}
              onRateLocked={(lock) => {
                setRateLock(lock);
                toast({
                  title: "Rate Locked! 🔒",
                  description: `Your rate is protected for 10 minutes`,
                });
              }}
              onRateExpired={() => {
                setRateLock(null);
                toast({
                  title: "Rate Lock Expired",
                  description: "Using current market rate",
                  variant: "destructive",
                });
              }}
            />

            {/* Rate Options */}
            <div className="grid grid-cols-2 gap-3">
              <div className={`p-3 border rounded-lg cursor-pointer transition-all ${
                !showRateLock ? 'border-primary bg-primary/5' : 'border-muted hover:border-primary/50'
              }`} onClick={() => setShowRateLock(false)}>
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="w-4 h-4 text-blue-500" />
                  <span className="font-medium text-sm">Live Rate</span>
                </div>
                <p className="text-xs text-muted-foreground">Use current market rate</p>
              </div>

              <div className={`p-3 border rounded-lg cursor-pointer transition-all ${
                showRateLock ? 'border-primary bg-primary/5' : 'border-muted hover:border-primary/50'
              }`} onClick={() => setShowRateLock(true)}>
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="w-4 h-4 text-green-500" />
                  <span className="font-medium text-sm">Protected Rate</span>
                </div>
                <p className="text-xs text-muted-foreground">Lock rate for 10 minutes</p>
              </div>
            </div>
          </div>
        )}

        {/* Bank Account Selection Step */}
        {step === 'bank' && (
          <div className="space-y-4">
            <div className="text-center mb-4">
              <h3 className="font-medium">Select Bank Account</h3>
              <p className="text-sm text-muted-foreground">Choose from your saved accounts</p>
            </div>

            {savedAccounts.length > 0 ? (
              <div className="space-y-3">
                {savedAccounts.map((account) => (
                  <div
                    key={account.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedAccountId === account.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => {
                      console.log('🏦 Bank account selected:', account);
                      setSelectedAccountId(account.id);
                      setBankAccount({
                        bankName: account.bankName,
                        accountNumber: account.accountNumber,
                        accountName: account.accountName
                      });
                      console.log('✅ Bank account set:', {
                        bankName: account.bankName,
                        accountNumber: account.accountNumber,
                        accountName: account.accountName
                      });
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{account.bankName}</div>
                        <div className="text-sm text-muted-foreground">
                          {account.accountNumber} • {account.accountName}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {account.isDefault && (
                          <Badge variant="secondary" className="text-xs">Default</Badge>
                        )}
                        {selectedAccountId === account.id && (
                          <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Building className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-medium mb-2">No Saved Accounts</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  You need to add a bank account first
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    toast({
                      title: "Add Bank Account",
                      description: "Please go to the Accounts tab to add your bank account first",
                    });
                  }}
                >
                  Add Bank Account
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Confirmation Step */}
        {step === 'confirm' && (
          <div className="space-y-4">
            <div className="text-center mb-4">
              <h3 className="font-medium">Confirm Withdrawal</h3>
              <p className="text-sm text-muted-foreground">Please review your transaction details</p>
            </div>

            <div className="bg-muted/50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">You're sending</span>
                <span className="font-medium">{cryptoAmount} {selectedCrypto}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">You'll receive</span>
                <span className="font-medium text-green-600">₦{ngnAmount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">To account</span>
                <span className="font-medium">{bankAccount.accountNumber} ({bankAccount.bankName})</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Account name</span>
                <span className="font-medium">{bankAccount.accountName}</span>
              </div>
            </div>

            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                This transaction is secured and will be processed instantly via NIBSS.
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Processing Step */}
        {step === 'processing' && (
          <div className="space-y-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-white animate-pulse" />
              </div>
              <h3 className="font-medium mb-2">Processing Your Withdrawal</h3>
              <p className="text-sm text-muted-foreground">
                Converting {cryptoAmount} {selectedCrypto} to ₦{ngnAmount}
              </p>
            </div>

            {/* Processing Steps */}
            <div className="space-y-4">
              {processingSteps.map((step, index) => (
                <div key={step.id} className="flex items-center space-x-4">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    step.status === 'completed' ? 'bg-green-500 text-white' :
                    step.status === 'processing' ? 'bg-blue-500 text-white' :
                    step.status === 'failed' ? 'bg-red-500 text-white' :
                    'bg-muted text-muted-foreground'
                  }`}>
                    {step.status === 'completed' ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : step.status === 'processing' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    ) : step.status === 'failed' ? (
                      <AlertCircle className="w-4 h-4" />
                    ) : (
                      <Clock className="w-4 h-4" />
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">{step.title}</span>
                      {step.duration && (
                        <span className="text-xs text-muted-foreground">{step.duration}s</span>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Success Animation */}
            {processingSteps.every(step => step.status === 'completed') && (
              <div className="text-center py-4">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-medium text-green-600 mb-2">Transaction Successful!</h3>
                <p className="text-sm text-muted-foreground">
                  ₦{ngnAmount} has been sent to your {bankAccount.bankName} account
                </p>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {step !== 'amount' && step !== 'processing' && (
            <Button
              variant="outline"
              onClick={() => {
                if (step === 'rate') setStep('amount');
                else if (step === 'bank') setStep('rate');
                else if (step === 'confirm') setStep('bank');
              }}
              className="flex-1"
            >
              Back
            </Button>
          )}

          {step !== 'processing' && (
            <Button
              onClick={handleContinue}
              disabled={isProcessing}
              className="flex-1"
            >
              {step === 'amount' && 'Continue'}
              {step === 'rate' && 'Select Account'}
              {step === 'bank' && 'Review Transaction'}
              {step === 'confirm' && (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Confirm Withdrawal
                </>
              )}
            </Button>
          )}
        </div>

        {/* Features */}
        {step === 'amount' && (
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <Zap className="w-5 h-5 mx-auto mb-1 text-yellow-500" />
              <div className="text-xs font-medium">Instant</div>
              <div className="text-xs text-muted-foreground">30-60s</div>
            </div>
            <div className="text-center">
              <Shield className="w-5 h-5 mx-auto mb-1 text-green-500" />
              <div className="text-xs font-medium">Secure</div>
              <div className="text-xs text-muted-foreground">NIBSS</div>
            </div>
            <div className="text-center">
              <Building className="w-5 h-5 mx-auto mb-1 text-blue-500" />
              <div className="text-xs font-medium">All Banks</div>
              <div className="text-xs text-muted-foreground">Supported</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
