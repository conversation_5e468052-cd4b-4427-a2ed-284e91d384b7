import React, { useState } from 'react';
import { Bell, Check, CheckCheck, Wallet, CreditCard, Users, Clock, Store, QrCode } from 'lucide-react';
import MerchantPaymentDetails from './MerchantPaymentDetails';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Notification, useNotifications } from '@/contexts/NotificationContext';
import { format, formatDistanceToNow } from 'date-fns';

export function NotificationDropdown() {
  const { notifications, unreadCount, markAsRead, markAllAsRead, loading } = useNotifications();
  const [selectedPaymentId, setSelectedPaymentId] = useState<string | null>(null);
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'wallet_funded':
        return <Wallet className="h-4 w-4 text-green-500" />;
      case 'card_funded':
        return <CreditCard className="h-4 w-4 text-blue-500" />;
      case 'group_card_added':
        return <Users className="h-4 w-4 text-purple-500" />;
      case 'group_card_invitation':
        return <Users className="h-4 w-4 text-blue-500" />;
      case 'group_card_accepted':
        return <Users className="h-4 w-4 text-green-500" />;
      case 'group_card_declined':
        return <Users className="h-4 w-4 text-red-500" />;
      case 'transaction':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'merchant_payment':
        return <Store className="h-4 w-4 text-purple-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Handle merchant payment notifications
    if (notification.type === 'merchant_payment' && notification.metadata?.transaction_id) {
      setSelectedPaymentId(notification.metadata.transaction_id);
      setShowPaymentDetails(true);
    }
  };

  return (
    <>
      <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-red-500 text-white"
              variant="destructive"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-xs flex items-center gap-1"
              onClick={() => markAllAsRead()}
            >
              <CheckCheck className="h-3 w-3" />
              Mark all as read
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <ScrollArea className="h-[300px]">
          <DropdownMenuGroup>
            {loading ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                Loading notifications...
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                No notifications yet
              </div>
            ) : (
              notifications.map((notification) => (
                <DropdownMenuItem
                  key={notification.id}
                  className={`flex flex-col items-start p-3 cursor-pointer ${
                    !notification.read ? 'bg-muted/50' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex w-full justify-between">
                    <div className="flex items-center gap-2">
                      {getNotificationIcon(notification.type)}
                      <span className="font-medium">{notification.title}</span>
                    </div>
                    {!notification.read && (
                      <Badge variant="outline" className="h-1.5 w-1.5 rounded-full bg-blue-500 p-0" />
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {notification.message}
                  </p>
                  <div className="flex w-full justify-between items-center mt-2">
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                    </span>
                    {notification.read && (
                      <span className="text-xs flex items-center text-muted-foreground">
                        <Check className="h-3 w-3 mr-1" /> Read
                      </span>
                    )}
                  </div>
                </DropdownMenuItem>
              ))
            )}
          </DropdownMenuGroup>
        </ScrollArea>
      </DropdownMenuContent>
      </DropdownMenu>

      {/* Merchant Payment Details Modal */}
      {selectedPaymentId && (
        <MerchantPaymentDetails
          isOpen={showPaymentDetails}
          onClose={() => {
            setShowPaymentDetails(false);
            setSelectedPaymentId(null);
          }}
          paymentId={selectedPaymentId}
        />
      )}
    </>
  );
}
