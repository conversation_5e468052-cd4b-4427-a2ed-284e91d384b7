/**
 * RPC Test Utility - Test all chain RPC endpoints
 */

import { CHAIN_CONFIGS, SupportedChain } from '@/types/crossChain';

export async function testAllRPCEndpoints() {
  console.log('🧪 Testing all RPC endpoints...');
  
  const results: Record<string, { success: boolean; responseTime?: number; error?: string }> = {};
  
  for (const [chainName, chainConfig] of Object.entries(CHAIN_CONFIGS)) {
    const chain = chainName as SupportedChain;
    console.log(`\n🔗 Testing ${chainConfig.name} (${chain})...`);
    
    try {
      const startTime = Date.now();
      
      if (chain === SupportedChain.SOLANA) {
        // Test Solana RPC
        const response = await fetch(chainConfig.rpcUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'getHealth'
          }),
          signal: AbortSignal.timeout(10000)
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        if (data.error) {
          throw new Error(`RPC Error: ${data.error.message}`);
        }
        
      } else {
        // Test EVM RPC
        const response = await fetch(chainConfig.rpcUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'eth_blockNumber',
            params: []
          }),
          signal: AbortSignal.timeout(10000)
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        if (data.error) {
          throw new Error(`RPC Error: ${data.error.message}`);
        }
        
        const blockNumber = parseInt(data.result, 16);
        console.log(`  📊 Current block: ${blockNumber.toLocaleString()}`);
      }
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      results[chain] = { success: true, responseTime };
      console.log(`  ✅ ${chainConfig.name}: ${responseTime}ms`);
      
    } catch (error) {
      results[chain] = { success: false, error: error.message };
      console.log(`  ❌ ${chainConfig.name}: ${error.message}`);
    }
  }
  
  // Summary
  console.log('\n📊 RPC Test Summary:');
  const successful = Object.values(results).filter(r => r.success).length;
  const total = Object.keys(results).length;
  console.log(`✅ ${successful}/${total} chains working`);
  
  Object.entries(results).forEach(([chain, result]) => {
    const status = result.success ? '✅' : '❌';
    const time = result.responseTime ? ` (${result.responseTime}ms)` : '';
    const error = result.error ? ` - ${result.error}` : '';
    console.log(`  ${status} ${chain.toUpperCase()}${time}${error}`);
  });
  
  return results;
}

// Auto-run test in development
if (process.env.NODE_ENV === 'development') {
  // Run test after a delay to avoid blocking app startup
  setTimeout(() => {
    testAllRPCEndpoints();
  }, 5000);
}
