
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { CardProvider } from "@/types/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useCard } from "@/contexts/CardContext";
import CardConfirmation from "./CardConfirmation";
import BillingAddressForm from "./BillingAddressForm";

// This is an extended version of CardRegistrationForm that includes billing address

const CardRegistrationFormExtended = () => {
  const navigate = useNavigate();
  const { generateCard } = useCard();
  const [name, setName] = useState("");
  const [provider, setProvider] = useState<CardProvider>(CardProvider.VISA);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'card-details' | 'billing-address' | 'confirm'>('card-details');
  const [billingAddress, setBillingAddress] = useState({
    street: "",
    city: "",
    state: "",
    zipCode: "",
    country: "United States",
  });
  const [confirmOpen, setConfirmOpen] = useState(false);
  
  const handleCardDetailsSubmit = () => {
    if (!name.trim()) {
      setError("Please enter your name as it should appear on the card.");
      return;
    }
    
    setError(null);
    setStep('billing-address');
  };
  
  const handleBillingAddressSubmit = (addressData) => {
    setBillingAddress(addressData);
    setStep('confirm');
  };
  
  const handleGenerateCard = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real application, we would pass the billing address to the card issuing service
      const card = await generateCard(provider, name);
      if (card) {
        setConfirmOpen(true);
      } else {
        setError("Failed to generate card. Please try again.");
      }
    } catch (err) {
      setError("An error occurred while generating your card: " + (err instanceof Error ? err.message : String(err)));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      {step === 'card-details' && (
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">Cardholder Name</Label>
            <Input
              id="name"
              placeholder="e.g. John Doe"
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={isLoading}
            />
            <p className="text-sm text-muted-foreground">
              Enter your name as it should appear on the card
            </p>
          </div>

          <div className="space-y-2">
            <Label>Card Provider</Label>
            <RadioGroup
              value={provider}
              onValueChange={(value) => setProvider(value as CardProvider)}
              disabled={isLoading}
              className="flex flex-col space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={CardProvider.VISA} id="visa" />
                <Label htmlFor="visa" className="cursor-pointer">Visa</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={CardProvider.MASTERCARD} id="mastercard" />
                <Label htmlFor="mastercard" className="cursor-pointer">Mastercard</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={CardProvider.AMEX} id="amex" />
                <Label htmlFor="amex" className="cursor-pointer">American Express</Label>
              </div>
            </RadioGroup>
          </div>

          {error && (
            <Alert variant="destructive">
              <ExclamationTriangleIcon className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            onClick={handleCardDetailsSubmit}
            className="w-full"
            disabled={isLoading || !name.trim()}
          >
            Next: Add Billing Address
          </Button>
        </div>
      )}
      
      {step === 'billing-address' && (
        <>
          <h3 className="text-lg font-medium mb-4">Billing Address</h3>
          <BillingAddressForm 
            onSubmit={handleBillingAddressSubmit} 
            initialData={billingAddress}
            isSubmitting={isLoading}
          />
          <Button 
            variant="outline" 
            onClick={() => setStep('card-details')} 
            className="mt-4 w-full"
          >
            Back to Card Details
          </Button>
        </>
      )}
      
      {step === 'confirm' && (
        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Review Card Details</h3>
            
            <div className="bg-muted p-4 rounded-md space-y-3">
              <div>
                <span className="text-sm text-muted-foreground">Cardholder:</span>
                <p className="font-medium">{name}</p>
              </div>
              
              <div>
                <span className="text-sm text-muted-foreground">Card Provider:</span>
                <p className="font-medium capitalize">{provider}</p>
              </div>
              
              <div>
                <span className="text-sm text-muted-foreground">Billing Address:</span>
                <p className="font-medium">{billingAddress.street}</p>
                <p className="font-medium">
                  {billingAddress.city}, {billingAddress.state} {billingAddress.zipCode}
                </p>
                <p className="font-medium">{billingAddress.country}</p>
              </div>
            </div>
          </div>
          
          {error && (
            <Alert variant="destructive">
              <ExclamationTriangleIcon className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="flex space-x-4">
            <Button
              variant="outline"
              onClick={() => setStep('billing-address')}
              className="flex-1"
              disabled={isLoading}
            >
              Back
            </Button>
            <Button
              onClick={handleGenerateCard}
              className="flex-1"
              disabled={isLoading}
            >
              {isLoading ? "Generating..." : "Generate Card"}
            </Button>
          </div>
        </div>
      )}

      <Dialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Card Generated Successfully</DialogTitle>
          </DialogHeader>
          <CardConfirmation onClose={() => navigate('/virtual-card')} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CardRegistrationFormExtended;
