/**
 * Cross-Chain Types and Interfaces
 */

export enum SupportedChain {
  SOLANA = 'solana',
  ETHEREUM = 'ethereum',
  POLYGON = 'polygon',
  BSC = 'bsc',
  ARBITRUM = 'arbitrum',
  AVALANCHE = 'avalanche',
  BASE = 'base'
}

export interface ChainConfig {
  id: SupportedChain;
  name: string;
  symbol: string;
  rpcUrl: string;
  explorerUrl: string;
  chainId?: number; // For EVM chains
  logo: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  supportedTokens: TokenConfig[];
}

export interface TokenConfig {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  logo: string;
  coingeckoId: string;
  isNative?: boolean;
}

export interface CrossChainWallet {
  id: string;
  userId: string;
  chain: SupportedChain;
  address: string;
  publicKey?: string;
  encryptedPrivateKey?: string;
  isActive: boolean;
  balances: TokenBalance[];
  createdAt: string;
  updatedAt: string;
}

export interface TokenBalance {
  tokenSymbol: string;
  tokenAddress: string;
  balance: string;
  balanceUSD: number;
  lastUpdated: string;
}

export interface CrossChainTransaction {
  id: string;
  userId: string;
  fromChain: SupportedChain;
  toChain: SupportedChain;
  fromToken: string;
  toToken: string;
  fromAmount: string;
  toAmount: string;
  fromAddress: string;
  toAddress: string;
  bridgeProvider: BridgeProvider;
  status: CrossChainTxStatus;
  txHash?: string;
  bridgeTxHash?: string;
  destinationTxHash?: string;
  estimatedTime: number; // minutes
  fees: {
    networkFee: string;
    bridgeFee: string;
    totalFeeUSD: number;
  };
  createdAt: string;
  completedAt?: string;
}

export enum CrossChainTxStatus {
  PENDING = 'pending',
  BRIDGING = 'bridging',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

export enum BridgeProvider {
  WORMHOLE = 'wormhole',
  LAYER_ZERO = 'layerzero',
  ALLBRIDGE = 'allbridge',
  PORTAL = 'portal',
  MAYAN = 'mayan'
}

export interface BridgeQuote {
  provider: BridgeProvider;
  fromChain: SupportedChain;
  toChain: SupportedChain;
  fromToken: string;
  toToken: string;
  fromAmount: string;
  toAmount: string;
  estimatedTime: number;
  fees: {
    networkFee: string;
    bridgeFee: string;
    totalFeeUSD: number;
  };
  route: BridgeStep[];
}

export interface BridgeStep {
  action: 'swap' | 'bridge' | 'wrap' | 'unwrap';
  fromToken: string;
  toToken: string;
  fromAmount: string;
  toAmount: string;
  provider: string;
  estimatedTime: number;
}

// Chain configurations
export const CHAIN_CONFIGS: Record<SupportedChain, ChainConfig> = {
  [SupportedChain.SOLANA]: {
    id: SupportedChain.SOLANA,
    name: 'Solana',
    symbol: 'SOL',
    rpcUrl: 'https://api.mainnet-beta.solana.com',
    explorerUrl: 'https://solscan.io',
    logo: '/crypto-logos/sol.svg',
    nativeCurrency: {
      name: 'Solana',
      symbol: 'SOL',
      decimals: 9
    },
    supportedTokens: [
      {
        symbol: 'SOL',
        name: 'Solana',
        address: 'So11111111111111111111111111111111111111112',
        decimals: 9,
        logo: '/crypto-logos/sol.svg',
        coingeckoId: 'solana',
        isNative: true
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        decimals: 6,
        logo: '/crypto-logos/usdc.svg',
        coingeckoId: 'usd-coin'
      }
    ]
  },
  [SupportedChain.ETHEREUM]: {
    id: SupportedChain.ETHEREUM,
    name: 'Ethereum',
    symbol: 'ETH',
    rpcUrl: 'https://ethereum-rpc.publicnode.com',
    explorerUrl: 'https://etherscan.io',
    chainId: 1,
    logo: '/crypto-logos/eth.svg',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18
    },
    supportedTokens: [
      {
        symbol: 'ETH',
        name: 'Ethereum',
        address: '******************************************',
        decimals: 18,
        logo: '/crypto-logos/eth.svg',
        coingeckoId: 'ethereum',
        isNative: true
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
        decimals: 6,
        logo: '/crypto-logos/usdc.svg',
        coingeckoId: 'usd-coin'
      }
    ]
  },
  [SupportedChain.POLYGON]: {
    id: SupportedChain.POLYGON,
    name: 'Polygon',
    symbol: 'MATIC',
    rpcUrl: 'https://polygon-bor-rpc.publicnode.com',
    explorerUrl: 'https://polygonscan.com',
    chainId: 137,
    logo: '/crypto-logos/matic.svg',
    nativeCurrency: {
      name: 'Polygon',
      symbol: 'MATIC',
      decimals: 18
    },
    supportedTokens: [
      {
        symbol: 'MATIC',
        name: 'Polygon',
        address: '******************************************',
        decimals: 18,
        logo: '/crypto-logos/matic.svg',
        coingeckoId: 'matic-network',
        isNative: true
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        address: '******************************************',
        decimals: 6,
        logo: '/crypto-logos/usdc.svg',
        coingeckoId: 'usd-coin'
      }
    ]
  },
  [SupportedChain.BSC]: {
    id: SupportedChain.BSC,
    name: 'BNB Smart Chain',
    symbol: 'BNB',
    rpcUrl: 'https://bsc-rpc.publicnode.com',
    explorerUrl: 'https://bscscan.com',
    chainId: 56,
    logo: '/crypto-logos/bnb.svg',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18
    },
    supportedTokens: [
      {
        symbol: 'BNB',
        name: 'BNB',
        address: '******************************************',
        decimals: 18,
        logo: '/crypto-logos/bnb.svg',
        coingeckoId: 'binancecoin',
        isNative: true
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        address: '******************************************',
        decimals: 18,
        logo: '/crypto-logos/usdc.svg',
        coingeckoId: 'usd-coin'
      }
    ]
  },
  [SupportedChain.ARBITRUM]: {
    id: SupportedChain.ARBITRUM,
    name: 'Arbitrum',
    symbol: 'ETH',
    rpcUrl: 'https://arbitrum-one-rpc.publicnode.com',
    explorerUrl: 'https://arbiscan.io',
    chainId: 42161,
    logo: '/crypto-logos/arbitrum.svg',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18
    },
    supportedTokens: [
      {
        symbol: 'ETH',
        name: 'Ethereum',
        address: '******************************************',
        decimals: 18,
        logo: '/crypto-logos/eth.svg',
        coingeckoId: 'ethereum',
        isNative: true
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        address: '******************************************',
        decimals: 6,
        logo: '/crypto-logos/usdc.svg',
        coingeckoId: 'usd-coin'
      }
    ]
  },
  [SupportedChain.AVALANCHE]: {
    id: SupportedChain.AVALANCHE,
    name: 'Avalanche',
    symbol: 'AVAX',
    rpcUrl: 'https://api.avax.network/ext/bc/C/rpc',
    explorerUrl: 'https://snowtrace.io',
    chainId: 43114,
    logo: '/crypto-logos/avax.svg',
    nativeCurrency: {
      name: 'Avalanche',
      symbol: 'AVAX',
      decimals: 18
    },
    supportedTokens: [
      {
        symbol: 'AVAX',
        name: 'Avalanche',
        address: '******************************************',
        decimals: 18,
        logo: '/crypto-logos/avax.svg',
        coingeckoId: 'avalanche-2',
        isNative: true
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        address: '******************************************',
        decimals: 6,
        logo: '/crypto-logos/usdc.svg',
        coingeckoId: 'usd-coin'
      }
    ]
  },
  [SupportedChain.BASE]: {
    name: 'Base',
    chainId: 8453,
    rpcUrl: 'https://mainnet.base.org',
    explorerUrl: 'https://basescan.org',
    logo: '/crypto-logos/base.svg',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18
    },
    supportedTokens: [
      {
        symbol: 'ETH',
        name: 'Ethereum',
        address: '******************************************',
        decimals: 18,
        logo: '/crypto-logos/eth.svg',
        coingeckoId: 'ethereum',
        isNative: true
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        address: '******************************************',
        decimals: 6,
        logo: '/crypto-logos/usdc.svg',
        coingeckoId: 'usd-coin',
        isNative: false
      }
    ]
  }
};
