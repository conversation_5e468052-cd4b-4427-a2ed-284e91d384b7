/**
 * Service for managing group card notifications
 */
import { supabase } from '@/integrations/supabase/client';

/**
 * Send notification when someone is invited to a group card
 */
export async function sendGroupCardInvitationNotification(
  invitedUserId: string,
  inviterName: string,
  groupCardName: string,
  groupCardId: string,
  role: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: invitedUserId,
        title: 'Group Card Invitation',
        message: `${inviterName} invited you to join "${groupCardName}" as a ${role.toLowerCase()}`,
        type: 'group_card_invitation',
        read: false,
        metadata: {
          group_id: groupCardId,
          group_name: groupCardName,
          inviter_name: inviterName,
          role: role,
          action_required: true
        }
      });

    if (error) {
      console.error('Error sending group card invitation notification:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in sendGroupCardInvitationNotification:', error);
    return false;
  }
}

/**
 * Send notification when someone accepts a group card invitation
 */
export async function sendGroupCardAcceptedNotification(
  ownerId: string,
  memberName: string,
  groupCardName: string,
  groupCardId: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: ownerId,
        title: 'Invitation Accepted',
        message: `${memberName} accepted your invitation to join "${groupCardName}"`,
        type: 'group_card_accepted',
        read: false,
        metadata: {
          group_id: groupCardId,
          group_name: groupCardName,
          member_name: memberName
        }
      });

    if (error) {
      console.error('Error sending group card accepted notification:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in sendGroupCardAcceptedNotification:', error);
    return false;
  }
}

/**
 * Send notification when someone declines a group card invitation
 */
export async function sendGroupCardDeclinedNotification(
  ownerId: string,
  memberName: string,
  groupCardName: string,
  groupCardId: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: ownerId,
        title: 'Invitation Declined',
        message: `${memberName} declined your invitation to join "${groupCardName}"`,
        type: 'group_card_declined',
        read: false,
        metadata: {
          group_id: groupCardId,
          group_name: groupCardName,
          member_name: memberName
        }
      });

    if (error) {
      console.error('Error sending group card declined notification:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in sendGroupCardDeclinedNotification:', error);
    return false;
  }
}

/**
 * Send notification when someone is successfully added to a group card
 */
export async function sendGroupCardMemberAddedNotification(
  memberId: string,
  groupCardName: string,
  groupCardId: string,
  role: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: memberId,
        title: 'Added to Group Card',
        message: `You have been added to "${groupCardName}" as a ${role.toLowerCase()}`,
        type: 'group_card_added',
        read: false,
        metadata: {
          group_id: groupCardId,
          group_name: groupCardName,
          role: role
        }
      });

    if (error) {
      console.error('Error sending group card member added notification:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in sendGroupCardMemberAddedNotification:', error);
    return false;
  }
}
