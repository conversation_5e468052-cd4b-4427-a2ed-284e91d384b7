import Layout from "@/components/Layout";
import SubscriptionCard from "@/components/SubscriptionCard";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

const Marketplace = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  
  // Enhanced mock data for available subscriptions with more detailed plans
  const subscriptions = [
    {
      id: "netflix",
      name: "Netflix",
      logo: "https://www.freepnglogos.com/uploads/netflix-logo-circle-png-5.png",
      price: 15.99,
      currency: "mo",
      isSubscribed: true,
      category: "streaming",
      plans: [
        { 
          id: "basic", 
          name: "Basic with ads", 
          price: 6.99,
          description: "Good video quality in 720p. Watch on 1 supported device at a time. With ads.",
          features: ["720p HD", "1 device", "With ads"]
        },
        { 
          id: "standard", 
          name: "Standard", 
          price: 15.99, 
          isPopular: true,
          description: "Great video quality in 1080p. Watch on 2 supported devices at a time. Ad-free.",
          features: ["1080p Full HD", "2 devices", "Ad-free", "Download on 2 devices"]
        },
        { 
          id: "premium", 
          name: "Premium", 
          price: 19.99,
          description: "Our best video quality in 4K and HDR. Watch on 4 supported devices at a time. Ad-free.",
          features: ["4K Ultra HD + HDR", "4 devices", "Ad-free", "Download on 6 devices", "Spatial audio"]
        }
      ]
    },
    {
      id: "spotify",
      name: "Spotify",
      logo: "https://www.freepnglogos.com/uploads/spotify-logo-png/file-spotify-logo-png-4.png",
      price: 9.99,
      currency: "mo",
      isSubscribed: true,
      category: "music",
      plans: [
        { 
          id: "individual", 
          name: "Individual", 
          price: 9.99,
          isPopular: true,
          description: "Ad-free music listening, play offline, on-demand playback",
          features: ["Ad-free", "Download songs", "On-demand playback", "1 account"]
        },
        { 
          id: "duo", 
          name: "Duo", 
          price: 12.99,
          description: "2 Premium accounts for a couple under one roof",
          features: ["Ad-free", "Download songs", "On-demand playback", "2 accounts"]
        },
        { 
          id: "family", 
          name: "Family", 
          price: 15.99,
          description: "6 Premium accounts for family members living under one roof",
          features: ["Ad-free", "Download songs", "On-demand playback", "6 accounts", "Parental controls"]
        }
      ]
    },
    {
      id: "disney",
      name: "Disney+",
      logo: "https://www.freepnglogos.com/uploads/disney-plus-logo-png/disney-plus-logo-explore-this-weeks-top-new-releases-movies-shows-disney-hotstar-2.png",
      price: 7.99,
      currency: "mo",
      category: "streaming",
      plans: [
        { 
          id: "monthly", 
          name: "Monthly", 
          price: 7.99,
          description: "Unlimited entertainment with no ads.",
          features: ["4K UHD & HDR", "Ad-free", "Download on 10 devices"]
        },
        { 
          id: "annual", 
          name: "Annual", 
          price: 79.99, 
          billedAs: "yr",
          isPopular: true,
          description: "Save up to 16% vs. monthly",
          features: ["4K UHD & HDR", "Ad-free", "Download on 10 devices", "Save 16% vs monthly"]
        }
      ]
    },
    {
      id: "hbo",
      name: "HBO Max",
      logo: "https://www.freepnglogos.com/uploads/hbo-max-logo-png-5.png",
      price: 14.99,
      currency: "mo",
      category: "streaming",
      plans: [
        { 
          id: "withads", 
          name: "With Ads", 
          price: 9.99,
          description: "Watch all of HBO Max with limited ads for a lower price",
          features: ["Stream on 2 devices", "Full HD", "With ads"]
        },
        { 
          id: "noads", 
          name: "Ad-Free", 
          price: 14.99,
          isPopular: true,
          description: "Everything on HBO Max, ad-free",
          features: ["Stream on 2 devices", "Full HD", "No ads", "30 downloads"]
        },
        { 
          id: "ultimate", 
          name: "Ultimate", 
          price: 19.99,
          description: "The complete experience with no ads, plus more features",
          features: ["Stream on 4 devices", "4K UHD", "No ads", "100 downloads", "Dolby Atmos audio"]
        }
      ]
    },
    {
      id: "apple",
      name: "Apple Music",
      logo: "https://www.freepnglogos.com/uploads/apple-music-logo-circle-png-28.png",
      price: 9.99,
      currency: "mo",
      category: "music",
      plans: [
        { 
          id: "student", 
          name: "Student", 
          price: 4.99,
          description: "For verified college students only",
          features: ["90 million songs", "Ad-free", "Download for offline"]
        },
        { 
          id: "individual", 
          name: "Individual", 
          price: 9.99,
          isPopular: true,
          description: "Access to Apple Music library for one account",
          features: ["90 million songs", "Ad-free", "Download for offline", "Lyrics view", "Original shows"]
        },
        { 
          id: "family", 
          name: "Family", 
          price: 14.99,
          description: "Access for up to 6 people",
          features: ["90 million songs", "Ad-free", "Download for offline", "Lyrics view", "Original shows", "6 accounts"]
        }
      ]
    },
    {
      id: "youtube",
      name: "YouTube Premium",
      logo: "https://www.freepnglogos.com/uploads/youtube-play-red-logo-png-transparent-background-6.png",
      price: 11.99,
      currency: "mo",
      category: "streaming",
      plans: [
        { 
          id: "individual", 
          name: "Individual", 
          price: 11.99,
          isPopular: true,
          description: "Ad-free videos, background playback, downloads",
          features: ["Ad-free videos", "Background play", "Downloads", "YouTube Music Premium"]
        },
        { 
          id: "family", 
          name: "Family", 
          price: 22.99,
          description: "Premium for up to 5 family members living in the same household",
          features: ["Ad-free videos", "Background play", "Downloads", "YouTube Music Premium", "5 family members"]
        }
      ]
    },
    {
      id: "amazon",
      name: "Amazon Prime",
      logo: "https://www.freepnglogos.com/uploads/amazon-prime-logo-png-19.png",
      price: 14.99,
      currency: "mo",
      category: "shopping",
      plans: [
        { 
          id: "monthly", 
          name: "Monthly", 
          price: 14.99,
          description: "Free delivery, Prime Video, music, gaming, and more",
          features: ["Fast, free delivery", "Prime Video", "Amazon Music", "Prime Gaming"]
        },
        { 
          id: "annual", 
          name: "Annual", 
          price: 139.00, 
          billedAs: "yr",
          isPopular: true,
          description: "Save over 15% with annual subscription",
          features: ["Fast, free delivery", "Prime Video", "Amazon Music", "Prime Gaming", "Save 15%"]
        }
      ]
    },
    {
      id: "crunchyroll",
      name: "Crunchyroll",
      logo: "https://www.freepnglogos.com/uploads/crunchyroll-png-logo/crunchyroll-logo-symbol-png-9.png",
      price: 7.99,
      currency: "mo",
      category: "streaming",
      plans: [
        { 
          id: "fan", 
          name: "Fan", 
          price: 7.99,
          description: "Unlimited anime with ads, 1 device stream",
          features: ["Ad-supported", "1 stream", "New episodes 1 hour after Japan"]
        },
        { 
          id: "mega", 
          name: "Mega Fan", 
          price: 9.99,
          isPopular: true,
          description: "Ad-free anime, 4 simultaneous streams, offline viewing",
          features: ["Ad-free", "4 streams", "Offline viewing", "New episodes 1 hour after Japan"]
        },
        { 
          id: "ultimate", 
          name: "Ultimate Fan", 
          price: 14.99,
          description: "Everything in Mega Fan, plus exclusive merch",
          features: ["Ad-free", "4 streams", "Offline viewing", "New episodes 1 hour after Japan", "Annual gift", "$15 store coupon"]
        }
      ]
    },
    {
      id: "adobe",
      name: "Adobe Creative Cloud",
      logo: "https://www.freepnglogos.com/uploads/adobe-logo-png/adobe-logo-vector-png-8.png",
      price: 52.99,
      currency: "mo",
      category: "productivity",
      plans: [
        { 
          id: "photography", 
          name: "Photography", 
          price: 19.99,
          description: "Lightroom, Photoshop, and 20GB cloud storage",
          features: ["Photoshop", "Lightroom", "20GB storage", "Portfolio website"]
        },
        { 
          id: "single", 
          name: "Single App", 
          price: 20.99,
          description: "Choose one Creative Cloud app",
          features: ["1 Adobe app", "100GB storage", "Adobe Fonts", "Portfolio website"]
        },
        { 
          id: "all-apps", 
          name: "All Apps", 
          price: 52.99,
          isPopular: true,
          description: "All creative apps and services",
          features: ["20+ desktop apps", "100GB storage", "Adobe Fonts", "Portfolio website", "Adobe Express"]
        }
      ]
    },
    {
      id: "office365",
      name: "Microsoft 365",
      logo: "https://www.freepnglogos.com/uploads/microsoft-logo-png-transparent-background-1.png",
      price: 9.99,
      currency: "mo",
      category: "productivity",
      plans: [
        { 
          id: "personal", 
          name: "Personal", 
          price: 6.99,
          description: "For 1 person",
          features: ["Word, Excel, PowerPoint", "1TB OneDrive", "Outlook", "Mobile apps"]
        },
        { 
          id: "family", 
          name: "Family", 
          price: 9.99,
          isPopular: true,
          description: "For up to 6 people",
          features: ["Word, Excel, PowerPoint", "6TB OneDrive (1TB per person)", "Outlook", "Mobile apps", "Up to 6 users"]
        },
        { 
          id: "business", 
          name: "Business", 
          price: 12.99,
          description: "For businesses",
          features: ["Word, Excel, PowerPoint", "1TB OneDrive", "Teams", "Exchange", "Access, Publisher"]
        }
      ]
    }
  ];

  const filteredSubscriptions = subscriptions.filter(sub => {
    const matchesSearch = sub.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || sub.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Group by category for tab view
  const streamingServices = subscriptions.filter(sub => sub.category === "streaming");
  const musicServices = subscriptions.filter(sub => sub.category === "music");
  const otherServices = subscriptions.filter(sub => !["streaming", "music"].includes(sub.category || ""));

  const categories = [
    { id: "all", name: "All Services" },
    { id: "streaming", name: "Streaming" },
    { id: "music", name: "Music" },
    { id: "shopping", name: "Shopping" },
    { id: "productivity", name: "Productivity" }
  ];

  return (
    <Layout>
      <div className="grid gap-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Subscription Marketplace</h1>
        </div>
        
        <Tabs defaultValue="browse" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="browse">Browse</TabsTrigger>
            <TabsTrigger value="categories">By Category</TabsTrigger>
            <TabsTrigger value="popular">Popular</TabsTrigger>
          </TabsList>
          
          <TabsContent value="browse" className="space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input 
                  placeholder="Search services..." 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
              
              {/* Category Filter */}
              <div className="flex gap-2 overflow-x-auto pb-1">
                {categories.map(category => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>
            
            <p className="text-muted-foreground">
              Browse and subscribe to popular services using your crypto balance. We'll handle the payment conversion.
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredSubscriptions.length > 0 ? (
                filteredSubscriptions.map((subscription) => (
                  <SubscriptionCard
                    key={subscription.id}
                    id={subscription.id}
                    name={subscription.name}
                    logo={subscription.logo}
                    price={subscription.price}
                    currency={subscription.currency}
                    isSubscribed={subscription.isSubscribed}
                    plans={subscription.plans}
                    category={subscription.category}
                  />
                ))
              ) : (
                <div className="col-span-full py-10 text-center text-muted-foreground">
                  No services match your search. Try a different term or category.
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="categories">
            <div className="space-y-8">
              <section>
                <h2 className="text-xl font-semibold mb-4">Streaming Services</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {streamingServices.map(subscription => (
                    <SubscriptionCard
                      key={subscription.id}
                      id={subscription.id}
                      name={subscription.name}
                      logo={subscription.logo}
                      price={subscription.price}
                      currency={subscription.currency}
                      isSubscribed={subscription.isSubscribed}
                      plans={subscription.plans}
                      category={subscription.category}
                    />
                  ))}
                </div>
              </section>
              
              <section>
                <h2 className="text-xl font-semibold mb-4">Music Services</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {musicServices.map(subscription => (
                    <SubscriptionCard
                      key={subscription.id}
                      id={subscription.id}
                      name={subscription.name}
                      logo={subscription.logo}
                      price={subscription.price}
                      currency={subscription.currency}
                      isSubscribed={subscription.isSubscribed}
                      plans={subscription.plans}
                      category={subscription.category}
                    />
                  ))}
                </div>
              </section>
              
              <section>
                <h2 className="text-xl font-semibold mb-4">Other Services</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {otherServices.map(subscription => (
                    <SubscriptionCard
                      key={subscription.id}
                      id={subscription.id}
                      name={subscription.name}
                      logo={subscription.logo}
                      price={subscription.price}
                      currency={subscription.currency}
                      isSubscribed={subscription.isSubscribed}
                      plans={subscription.plans}
                      category={subscription.category}
                    />
                  ))}
                </div>
              </section>
            </div>
          </TabsContent>
          
          <TabsContent value="popular">
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Most popular subscription services based on user subscriptions.
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {subscriptions
                  .filter(sub => sub.plans?.some(plan => plan.isPopular))
                  .map(subscription => (
                    <SubscriptionCard
                      key={subscription.id}
                      id={subscription.id}
                      name={subscription.name}
                      logo={subscription.logo}
                      price={subscription.price}
                      currency={subscription.currency}
                      isSubscribed={subscription.isSubscribed}
                      plans={subscription.plans}
                      category={subscription.category}
                    />
                  ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Marketplace;
