
/**
 * Helius API utilities for fetching Solana wallet data
 */
import { supabase } from '@/integrations/supabase/client';

// The API key is stored in Supabase and fetched securely
const HELIUS_API_URL = 'https://api.helius.xyz/v0';

// Helper function to get Helius API key from storage
async function getHeliusApiKey() {
  try {
    // Get HELIUS_API_KEY from app_assets bucket
    const { data, error } = await supabase
      .storage
      .from('app_assets')
      .download('config/helius_api_key.txt');

    if (error) {
      console.error('Error fetching Helius API key:', error);
      throw error;
    }

    const apiKey = await data.text();
    return apiKey.trim();
  } catch (error) {
    console.error('Failed to get Helius API key:', error);
    // Fallback to previously hardcoded key if fetch fails
    // Using your new API key
    return '6e2d574d-5bc3-4b99-b22f-102b68795f20';
  }
}

/**
 * Fetch token balances for a Solana wallet address
 * @param address The Solana wallet address
 * @returns The wallet balance data including USDC and SOL balances
 */
export async function fetchWalletBalances(address: string) {
  try {
    const apiKey = await getHeliusApiKey();
    const url = `${HELIUS_API_URL}/addresses/${address}/balances?api-key=${apiKey}`;
    console.log(`Fetching wallet balances from Helius for address: ${address}`);

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Helius API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Helius balance data received');

    // Also fetch SOL balance using RPC endpoint
    try {
      const rpcUrl = `https://api.mainnet-beta.solana.com`;
      const rpcResponse = await fetch(rpcUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'my-id',
          method: 'getBalance',
          params: [address],
        }),
      });

      const rpcData = await rpcResponse.json();
      if (rpcData.result && rpcData.result.value) {
        // SOL balance is in lamports (1 SOL = 1,000,000,000 lamports)
        const solBalance = rpcData.result.value / **********;
        data.nativeBalance = solBalance.toString();
        data.nativeBalanceUsd = solBalance * 60.25; // Approximate SOL price
        console.log(`Added SOL balance from RPC: ${solBalance}`);
      }
    } catch (rpcError) {
      console.error('Error fetching SOL balance from RPC:', rpcError);
      // Continue with the data we have
    }

    return data;
  } catch (error) {
    console.error('Error fetching wallet balances from Helius:', error);
    throw error;
  }
}

/**
 * Get token account information for a wallet
 * @param address The Solana wallet address
 * @returns Token account data
 */
export async function getTokenAccounts(address: string) {
  try {
    const apiKey = await getHeliusApiKey();
    const url = `${HELIUS_API_URL}/addresses/${address}/token-accounts?api-key=${apiKey}`;
    console.log(`Fetching token accounts from Helius for address: ${address}`);

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Helius API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Helius token accounts received');

    return data;
  } catch (error) {
    console.error('Error fetching token accounts from Helius:', error);
    throw error;
  }
}

/**
 * USDC token mint address on Solana
 * This is the official USDC SPL token on Solana mainnet
 */
export const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

/**
 * Parse balance data from Helius API response
 * @param balanceData The balance data from Helius API
 * @returns Parsed balance data for our application
 */
export function parseHeliusBalances(balanceData: any) {
  console.log('Parsing Helius balance data');

  const tokens = {
    SOL: { balance: 0, dollarValue: 0 },
    USDC: { balance: 0, dollarValue: 0 }
  };

  try {
    // Parse SOL balance
    if (balanceData.nativeBalance) {
      // SOL balance is in lamports (1 SOL = 1,000,000,000 lamports)
      const solBalance = parseFloat(balanceData.nativeBalance);
      tokens.SOL.balance = solBalance;
      // Use current price from balance data or fallback to estimate
      const solPrice = balanceData.nativeBalanceUsd
        ? balanceData.nativeBalanceUsd / solBalance
        : 60.25; // Fallback price estimate

      tokens.SOL.dollarValue = solBalance * solPrice;
      console.log(`Parsed SOL balance: ${solBalance} SOL (${tokens.SOL.dollarValue} USD)`);
    }

    // Parse token balances looking for USDC
    if (balanceData.tokens) {
      const usdcToken = balanceData.tokens.find((token: any) =>
        token.mint === USDC_MINT
      );

      if (usdcToken) {
        // USDC has 6 decimals
        const usdcBalance = parseFloat(usdcToken.amount) / 1000000;
        tokens.USDC.balance = usdcBalance;
        tokens.USDC.dollarValue = usdcBalance; // USDC is pegged to USD
        console.log(`Parsed USDC balance: ${usdcBalance} USDC (${tokens.USDC.dollarValue} USD)`);
      } else {
        console.log('No USDC token found in balances');
      }
    }

    // If we have token accounts in a different format
    if (balanceData.tokenAccounts) {
      for (const account of balanceData.tokenAccounts) {
        if (account.mint === USDC_MINT) {
          // USDC has 6 decimals
          const usdcBalance = parseFloat(account.amount) / 1000000;
          tokens.USDC.balance = usdcBalance;
          tokens.USDC.dollarValue = usdcBalance; // USDC is pegged to USD
          console.log(`Parsed USDC balance from tokenAccounts: ${usdcBalance} USDC`);
          break;
        }
      }
    }
  } catch (error) {
    console.error('Error parsing Helius balances:', error);
  }

  console.log('Parsed token balances:', tokens);
  return tokens;
}

/**
 * Fetch and format wallet data for our application
 * @param address The Solana wallet address
 * @returns Formatted wallet data with token balances
 */
export async function fetchFormattedWalletData(address: string) {
  try {
    console.log(`Fetching formatted wallet data for address: ${address}`);
    const balanceData = await fetchWalletBalances(address);
    const tokenData = await getTokenAccounts(address);

    const parsedBalances = parseHeliusBalances(balanceData);

    // Store the raw response in Supabase for debugging/auditing
    try {
      await storeWalletResponseData(address, {
        balanceData,
        tokenData,
        timestamp: new Date().toISOString()
      });
    } catch (storeError) {
      console.error('Failed to store wallet response data:', storeError);
      // Continue execution even if storage fails
    }

    return {
      balances: parsedBalances,
      tokenAccounts: tokenData
    };
  } catch (error) {
    console.error('Error fetching formatted wallet data:', error);
    throw error;
  }
}

/**
 * Store raw API response data in Supabase storage for debugging/auditing
 */
async function storeWalletResponseData(address: string, data: any) {
  try {
    const { user } = await supabase.auth.getUser();

    if (!user) {
      console.log('No authenticated user, skipping response data storage');
      return;
    }

    const userId = user.id;
    const fileName = `${userId}/wallet-data/${address}/${Date.now()}.json`;

    // Convert data to a blob
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });

    // Upload to user_uploads bucket
    const { data: uploadData, error } = await supabase
      .storage
      .from('user_uploads')
      .upload(fileName, blob, {
        upsert: true,
      });

    if (error) {
      console.error('Error storing wallet response data:', error);
      return;
    }

    console.log('Wallet response data stored successfully:', uploadData?.path);

  } catch (error) {
    console.error('Failed to store wallet response data:', error);
  }
}
