/**
 * Merchant Overview Component
 * 
 * Comprehensive overview dashboard for merchants showing
 * key metrics, recent activity, and quick actions
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  Users,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  CheckCircle,
  XCircle,
  Key,
  Webhook,
  BarChart3,
  Calendar,
  Globe
} from 'lucide-react';

interface MerchantOverviewProps {
  merchantId: string;
  businessName: string;
}

interface DashboardStats {
  total_revenue: number;
  revenue_change: number;
  total_transactions: number;
  transactions_change: number;
  success_rate: number;
  success_rate_change: number;
  pending_payments: number;
  recent_payments: Array<{
    id: string;
    amount: number;
    currency: string;
    status: string;
    created_at: string;
    customer_email?: string;
  }>;
  top_cryptocurrencies: Array<{
    symbol: string;
    percentage: number;
    amount: number;
  }>;
}

const MerchantOverview: React.FC<MerchantOverviewProps> = ({ merchantId, businessName }) => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadDashboardStats();
  }, [merchantId]);

  const loadDashboardStats = async () => {
    setLoading(true);
    try {
      // This would fetch real data from your API
      // For now, we'll use mock data
      const mockStats: DashboardStats = {
        total_revenue: 24750.00,
        revenue_change: 12.5,
        total_transactions: 156,
        transactions_change: 8.3,
        success_rate: 94.2,
        success_rate_change: 2.1,
        pending_payments: 3,
        recent_payments: [
          {
            id: 'pi_1234567890',
            amount: 150.00,
            currency: 'USD',
            status: 'succeeded',
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            customer_email: '<EMAIL>'
          },
          {
            id: 'pi_0987654321',
            amount: 75.50,
            currency: 'USD',
            status: 'processing',
            created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            customer_email: '<EMAIL>'
          },
          {
            id: 'pi_1122334455',
            amount: 200.00,
            currency: 'USD',
            status: 'succeeded',
            created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
          }
        ],
        top_cryptocurrencies: [
          { symbol: 'USDC', percentage: 45, amount: 11137.50 },
          { symbol: 'SOL', percentage: 35, amount: 8662.50 },
          { symbol: 'ETH', percentage: 20, amount: 4950.00 }
        ]
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const isPositive = value >= 0;
    return (
      <span className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <ArrowUpRight className="h-4 w-4 mr-1" /> : <ArrowDownRight className="h-4 w-4 mr-1" />}
        {Math.abs(value).toFixed(1)}%
      </span>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'succeeded':
        return <CheckCircle className="h-4 w-4" />;
      case 'processing':
        return <Clock className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-600">Failed to load dashboard data</p>
        <Button onClick={loadDashboardStats} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Welcome back, {businessName}! 👋</h1>
        <p className="text-blue-100">
          Here's what's happening with your crypto payments today
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.total_revenue)}</div>
            <p className="text-xs text-muted-foreground flex items-center mt-1">
              {formatPercentage(stats.revenue_change)} from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_transactions}</div>
            <p className="text-xs text-muted-foreground flex items-center mt-1">
              {formatPercentage(stats.transactions_change)} from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.success_rate}%</div>
            <p className="text-xs text-muted-foreground flex items-center mt-1">
              {formatPercentage(stats.success_rate_change)} from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending_payments}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Awaiting confirmation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks and integrations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={() => window.location.href = '/merchant-payment-intents'}
            >
              <CreditCard className="h-6 w-6 text-blue-600" />
              <span>Create Payment</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={() => window.location.href = '/merchant-api-keys'}
            >
              <Key className="h-6 w-6 text-green-600" />
              <span>API Keys</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={() => window.location.href = '/merchant-api-keys?tab=webhooks'}
            >
              <Webhook className="h-6 w-6 text-purple-600" />
              <span>Webhooks</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={() => window.location.href = '/merchant-payments'}
            >
              <BarChart3 className="h-6 w-6 text-orange-600" />
              <span>Analytics</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Payments */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Payments</CardTitle>
            <CardDescription>
              Latest payment activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recent_payments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between py-2">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(payment.status)}>
                        {getStatusIcon(payment.status)}
                        <span className="ml-1">{payment.status}</span>
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        {formatCurrency(payment.amount, payment.currency)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {payment.customer_email || payment.id}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {formatTimeAgo(payment.created_at)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              View All Payments
            </Button>
          </CardContent>
        </Card>

        {/* Top Cryptocurrencies */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>
              Breakdown by cryptocurrency
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.top_cryptocurrencies.map((crypto) => (
                <div key={crypto.symbol} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        crypto.symbol === 'USDC' ? 'bg-blue-500' :
                        crypto.symbol === 'SOL' ? 'bg-purple-500' :
                        crypto.symbol === 'ETH' ? 'bg-gray-700' : 'bg-orange-500'
                      }`}></div>
                      <span className="text-sm font-medium">{crypto.symbol}</span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{crypto.percentage}%</p>
                      <p className="text-xs text-gray-500">
                        {formatCurrency(crypto.amount)}
                      </p>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        crypto.symbol === 'USDC' ? 'bg-blue-500' :
                        crypto.symbol === 'SOL' ? 'bg-purple-500' :
                        crypto.symbol === 'ETH' ? 'bg-gray-700' : 'bg-orange-500'
                      }`}
                      style={{ width: `${crypto.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MerchantOverview;
