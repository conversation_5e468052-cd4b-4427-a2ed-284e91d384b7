/**
 * Merchant Payment History Page
 * Shows all payments received by the merchant
 */

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { 
  ArrowLeft,
  ExternalLink,
  Copy,
  Calendar,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Payment {
  id: string;
  amount_ngn: number;
  crypto_symbol: string;
  crypto_amount: number;
  exchange_rate: number;
  transaction_signature: string;
  payment_description: string;
  status: string;
  created_at: string;
  customer: {
    full_name: string;
    email: string;
  };
}

const MerchantPayments: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalEarnings, setTotalEarnings] = useState(0);

  useEffect(() => {
    if (user) {
      loadPayments();
    }
  }, [user]);

  const loadPayments = async () => {
    if (!user) return;

    try {
      // First get the merchant account
      const { data: merchant } = await supabase
        .from('merchant_accounts')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (!merchant) return;

      // Get all payments for this merchant
      const { data: paymentsData, error } = await supabase
        .from('merchant_crypto_payments')
        .select(`
          *,
          customer:profiles!customer_id (
            full_name,
            email
          )
        `)
        .eq('merchant_id', merchant.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading payments:', error);
        return;
      }

      setPayments(paymentsData || []);
      
      // Calculate total earnings
      const total = paymentsData?.reduce((sum, payment) => {
        return payment.status === 'confirmed' || payment.status === 'sent' 
          ? sum + payment.amount_ngn 
          : sum;
      }, 0) || 0;
      
      setTotalEarnings(total);

    } catch (error) {
      console.error('Error loading merchant payments:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-500';
      case 'sent': return 'bg-blue-500';
      case 'pending': return 'bg-yellow-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const copySignature = (signature: string) => {
    navigator.clipboard.writeText(signature);
  };

  const viewTransaction = (signature: string) => {
    if (signature.startsWith('sim_')) {
      // Simulated transaction
      alert('This is a simulated transaction. In production, this would open the blockchain explorer.');
    } else {
      // Real transaction
      window.open(`https://solscan.io/tx/${signature}`, '_blank');
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-6xl mx-auto p-4">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Loading payment history...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-6xl mx-auto p-4 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              onClick={() => navigate('/merchant-dashboard')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Payment History</h1>
              <p className="text-gray-600">Track all payments received</p>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <DollarSign className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-sm text-gray-600">Total Earnings</p>
                  <p className="text-2xl font-bold">₦{totalEarnings.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <TrendingUp className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">Total Payments</p>
                  <p className="text-2xl font-bold">{payments.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <Calendar className="h-8 w-8 text-purple-500" />
                <div>
                  <p className="text-sm text-gray-600">This Month</p>
                  <p className="text-2xl font-bold">
                    {payments.filter(p => {
                      const paymentDate = new Date(p.created_at);
                      const now = new Date();
                      return paymentDate.getMonth() === now.getMonth() && 
                             paymentDate.getFullYear() === now.getFullYear();
                    }).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payments List */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Payments</CardTitle>
          </CardHeader>
          <CardContent>
            {payments.length === 0 ? (
              <div className="text-center py-8">
                <DollarSign className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No Payments Yet</h3>
                <p className="text-gray-600">
                  Share your QR code to start receiving crypto payments
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {payments.map((payment) => (
                  <div key={payment.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-semibold">{payment.payment_description}</h4>
                          <Badge className={`${getStatusColor(payment.status)} text-white`}>
                            {payment.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Amount</p>
                            <p className="font-medium">₦{payment.amount_ngn.toLocaleString()}</p>
                            <p className="text-xs text-gray-500">
                              {payment.crypto_amount} {payment.crypto_symbol}
                            </p>
                          </div>
                          
                          <div>
                            <p className="text-gray-600">Customer</p>
                            <p className="font-medium">{payment.customer?.full_name || 'Unknown'}</p>
                            <p className="text-xs text-gray-500">{payment.customer?.email}</p>
                          </div>
                          
                          <div>
                            <p className="text-gray-600">Date</p>
                            <p className="font-medium">
                              {new Date(payment.created_at).toLocaleDateString()}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(payment.created_at).toLocaleTimeString()}
                            </p>
                          </div>
                          
                          <div>
                            <p className="text-gray-600">Transaction</p>
                            <div className="flex items-center gap-2">
                              <p className="text-xs font-mono">
                                {payment.transaction_signature.slice(0, 8)}...
                              </p>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => copySignature(payment.transaction_signature)}
                                className="h-6 w-6 p-0"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => viewTransaction(payment.transaction_signature)}
                                className="h-6 w-6 p-0"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default MerchantPayments;
