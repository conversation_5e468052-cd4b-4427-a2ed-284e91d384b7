
import { useState } from 'react';
import { refreshWalletFromHelius, refreshAllUserWallets } from '@/services/walletRefreshService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook for manually refreshing wallet balances
 */
export function useWalletRefresh() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { user } = useAuth();

  /**
   * Initialize Helius API key in Supabase storage if needed
   */
  const initializeHeliusApiKey = async () => {
    try {
      // Check if the API key file exists
      const { data, error } = await supabase
        .storage
        .from('app_assets')
        .list('config');

      const apiKeyFileExists = data?.some(item => item.name === 'helius_api_key.txt');

      // If the file doesn't exist, create it
      if (!apiKeyFileExists) {
        const apiKey = '6e2d574d-5bc3-4b99-b22f-102b68795f20'; // Your new API key
        const blob = new Blob([apiKey], { type: 'text/plain' });

        await supabase
          .storage
          .from('app_assets')
          .upload('config/helius_api_key.txt', blob, {
            upsert: true,
            cacheControl: '3600'
          });

        console.log('Initialized Helius API key in Supabase storage');
      }
    } catch (error) {
      console.error('Error initializing Helius API key:', error);
    }
  };

  /**
   * Refresh a specific wallet's balance
   * @param walletId The wallet ID
   * @param address The wallet address
   */
  const refreshWallet = async (walletId: string, address: string) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please login to refresh wallet balances",
        variant: "destructive"
      });
      return false;
    }

    setIsRefreshing(true);
    console.log(`Refreshing wallet ${walletId} with address ${address}`);

    // Ensure API key is in storage
    await initializeHeliusApiKey();

    try {
      const success = await refreshWalletFromHelius(walletId, address);

      if (success) {
        toast({
          title: "Wallet Refreshed",
          description: "Your wallet balance has been updated",
        });
        console.log("Wallet refresh successful");
      } else {
        toast({
          title: "Refresh Failed",
          description: "Unable to refresh wallet balance",
          variant: "destructive"
        });
        console.error("Wallet refresh failed");
      }

      return success;
    } catch (error) {
      console.error("Error refreshing wallet:", error);
      toast({
        title: "Refresh Error",
        description: "An error occurred while refreshing wallet",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsRefreshing(false);
    }
  };

  /**
   * Refresh all wallets for the current user
   */
  const refreshAllWallets = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please login to refresh wallet balances",
        variant: "destructive"
      });
      return false;
    }

    setIsRefreshing(true);
    console.log(`Refreshing all wallets for user ${user.id}`);

    // Ensure API key is in storage
    await initializeHeliusApiKey();

    try {
      const success = await refreshAllUserWallets(user.id);

      if (success) {
        toast({
          title: "Wallets Refreshed",
          description: "Your wallet balances have been updated",
        });
        console.log("All wallets refresh successful");
      } else {
        toast({
          title: "Refresh Failed",
          description: "Unable to refresh wallet balances",
          variant: "destructive"
        });
        console.error("All wallets refresh failed");
      }

      return success;
    } catch (error) {
      console.error("Error refreshing wallets:", error);
      toast({
        title: "Refresh Error",
        description: "An error occurred while refreshing wallets",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsRefreshing(false);
    }
  };

  return {
    refreshWallet,
    refreshAllWallets,
    isRefreshing,
    initializeHeliusApiKey
  };
}
