/**
 * Merchant Payment Links Page
 * 
 * Main page for merchants to manage their payment links
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import PaymentLinkManagement from '@/components/merchant/PaymentLinkManagement';
import {
  ArrowLeft,
  Link,
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Eye,
  Share2,
  Calendar,
  ExternalLink
} from 'lucide-react';

interface MerchantAccount {
  id: string;
  business_name: string;
  is_verified: boolean;
}

const MerchantPaymentLinks: React.FC = () => {
  const [merchant, setMerchant] = useState<MerchantAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total_links: 0,
    active_links: 0,
    total_clicks: 0,
    total_conversions: 0,
    conversion_rate: 0
  });
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    loadMerchantAccount();
  }, [user]);

  const loadMerchantAccount = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('payment_gateway_merchants')
        .select('id, business_name, verification_status')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error loading merchant account:', error);
        toast({
          title: "Error",
          description: "Failed to load merchant account. Please register as a merchant first.",
          variant: "destructive",
        });
        navigate('/merchant-registration');
        return;
      }

      setMerchant(data);
      loadStats(data.id);
    } catch (error) {
      console.error('Error loading merchant account:', error);
      toast({
        title: "Error",
        description: "Failed to load merchant account",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async (merchantId: string) => {
    try {
      // This would load actual stats from the database
      // For now, we'll use mock data
      setStats({
        total_links: 8,
        active_links: 6,
        total_clicks: 342,
        total_conversions: 89,
        conversion_rate: 26.0
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!merchant) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Merchant Account Required</h2>
              <p className="text-gray-600 mb-4">
                You need to register as a merchant to access payment links.
              </p>
              <Button onClick={() => navigate('/merchant-registration')}>
                Register as Merchant
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/merchant-dashboard')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Payment Links</h1>
                <p className="text-sm text-gray-600">{merchant.business_name}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!merchant.is_verified && (
                <Badge variant="outline" className="text-amber-600 border-amber-600">
                  Verification Pending
                </Badge>
              )}
              {merchant.is_verified && (
                <Badge className="bg-green-100 text-green-800">
                  Verified Merchant
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="links" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="links" className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              Payment Links
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Payment Links Tab */}
          <TabsContent value="links" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Links</CardTitle>
                  <Link className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total_links}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.active_links} active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total_clicks}</div>
                  <p className="text-xs text-muted-foreground">
                    +12% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Conversions</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total_conversions}</div>
                  <p className="text-xs text-muted-foreground">
                    Successful payments
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.conversion_rate}%</div>
                  <p className="text-xs text-muted-foreground">
                    +2.1% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$2,450</div>
                  <p className="text-xs text-muted-foreground">
                    From payment links
                  </p>
                </CardContent>
              </Card>
            </div>

            <PaymentLinkManagement merchantId={merchant.id} />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Link Performance</CardTitle>
                  <CardDescription>
                    Click-through rates over the last 30 days
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <p>Analytics chart would go here</p>
                      <p className="text-sm">Integration with charting library needed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Links</CardTitle>
                  <CardDescription>
                    Links with highest conversion rates
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between py-2">
                      <div>
                        <p className="text-sm font-medium">Premium Subscription</p>
                        <p className="text-xs text-gray-500">45 clicks • 12 conversions</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">26.7%</Badge>
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <div>
                        <p className="text-sm font-medium">Donation Link</p>
                        <p className="text-xs text-gray-500">89 clicks • 23 conversions</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">25.8%</Badge>
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <div>
                        <p className="text-sm font-medium">Product Purchase</p>
                        <p className="text-xs text-gray-500">67 clicks • 15 conversions</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">22.4%</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Traffic Sources</CardTitle>
                <CardDescription>
                  Where your payment link clicks are coming from
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm">Direct</span>
                    </div>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm">Social Media</span>
                    </div>
                    <span className="text-sm font-medium">30%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <span className="text-sm">Email</span>
                    </div>
                    <span className="text-sm font-medium">25%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Link Settings</CardTitle>
                <CardDescription>
                  Configure default settings for new payment links
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-2">Default Settings</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Default Currency</label>
                      <p className="text-sm text-gray-600">USD</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Default Cryptocurrencies</label>
                      <p className="text-sm text-gray-600">SOL, USDC</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Link Expiry</label>
                      <p className="text-sm text-gray-600">Never (unless specified)</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Branding</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Custom Logo</span>
                      <Badge variant="outline">Not Set</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Brand Colors</span>
                      <Badge variant="outline">Default</Badge>
                    </div>
                  </div>
                </div>

                <Button variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Configure Settings
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MerchantPaymentLinks;
