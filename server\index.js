/**
 * solpay Backend Server
 *
 * This server provides:
 * 1. API proxy for Lithic to avoid CORS issues
 * 2. Secure storage of API keys
 * 3. Static file serving for the frontend
 */
const express = require('express');
const cors = require('cors');
const path = require('path');
const fetch = require('node-fetch');
const dotenv = require('dotenv');

// Load environment variables from .env file
dotenv.config();

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Import webhook handlers and routes
const transactionWebhook = require('./webhooks/transactionWebhook');
const notificationsRoutes = require('./routes/notifications');
const flutterwaveCardsRoutes = require('./routes/flutterwave-cards');
const bridgecardTestRoutes = require('./routes/bridgecard-test');
const paymentGatewayRoutes = require('./routes/payment-gateway');

// Middleware
app.use(cors());

// Parse JSON request bodies (except for webhook routes that need raw body)
app.use((req, res, next) => {
  if (req.path.startsWith('/webhooks/')) {
    return next();
  }
  express.json()(req, res, next);
});

app.use(express.urlencoded({ extended: true }));

// Lithic API configuration
const LITHIC_API_BASE_URL = 'https://sandbox.lithic.com/v1';
const LITHIC_API_KEY = process.env.LITHIC_API_KEY;

// Flutterwave API configuration
const FLUTTERWAVE_SECRET_KEY = process.env.FLUTTERWAVE_SECRET_KEY;

// Validate that we have the required API keys
if (!LITHIC_API_KEY) {
  console.warn('⚠️ LITHIC_API_KEY is not set in environment variables');
}

if (!FLUTTERWAVE_SECRET_KEY) {
  console.warn('⚠️ FLUTTERWAVE_SECRET_KEY is not set in environment variables');
}

// Import the Lithic error handler
const lithicErrorHandler = require('./lithicErrorHandler');

// Maximum number of retry attempts for retryable errors
const MAX_RETRY_ATTEMPTS = 3;
// Base delay for exponential backoff (in milliseconds)
const BASE_RETRY_DELAY = 1000;

/**
 * Lithic API Proxy
 * Routes all requests to /api/lithic/* to the Lithic API
 */
app.use('/api/lithic', async (req, res) => {
  try {
    // Extract the Lithic API path from the request URL
    const lithicPath = req.path;
    const url = `${LITHIC_API_BASE_URL}${lithicPath}`;

    // Get request method and body
    const method = req.method;
    const body = method !== 'GET' && method !== 'HEAD' ? req.body : undefined;

    console.log(`Received ${method} request to /api/lithic${lithicPath}`);

    // Check if we have an API key
    if (!LITHIC_API_KEY) {
      console.log('No Lithic API key configured');
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Lithic API key is not configured on the server'
      });
    }

    // For card funding, return a mock success response in development
    if (process.env.NODE_ENV !== 'production' &&
        lithicPath.includes('/cards/') &&
        (lithicPath.includes('/fund') || method === 'PATCH')) {
      console.log('Handling card funding request with mock response');
      return res.status(200).json({
        success: true,
        message: 'Card funded successfully (mock response)',
        mock: true
      });
    }

    // Function to make the Lithic API request with retry logic
    const makeLithicRequest = async (retryAttempt = 0) => {
      try {
        // Prepare headers for the Lithic API request
        const headers = {
          'Authorization': `Bearer ${LITHIC_API_KEY}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        };

        console.log(`Proxying ${method} request to Lithic API: ${url}`);

        // Make the request to Lithic API
        const response = await fetch(url, {
          method,
          headers,
          body: body ? JSON.stringify(body) : undefined,
        });

        // Get response data
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          console.error('Error parsing response as JSON:', e);
          responseData = { message: 'Could not parse response as JSON' };
        }

        console.log(`Received response from Lithic API: ${response.status}`);

        // If response is not OK, handle the error
        if (!response.ok) {
          const errorObj = {
            status: response.status,
            data: responseData,
            message: responseData.message || `HTTP error! status: ${response.status}`
          };

          // Use our error handler to parse the error
          const lithicError = lithicErrorHandler.handleLithicError(errorObj, lithicPath);

          // Check if we should retry the operation
          if (lithicErrorHandler.shouldRetryLithicOperation(lithicError) && retryAttempt < MAX_RETRY_ATTEMPTS) {
            // Calculate delay with exponential backoff
            const delay = BASE_RETRY_DELAY * Math.pow(2, retryAttempt);
            console.log(`Retrying Lithic API call (attempt ${retryAttempt + 1}/${MAX_RETRY_ATTEMPTS}) after ${delay}ms delay...`);

            // Wait for the calculated delay
            await new Promise(resolve => setTimeout(resolve, delay));

            // Retry the API call with incremented retry counter
            return makeLithicRequest(retryAttempt + 1);
          }

          // If we shouldn't retry or have exceeded max retries, return the error response
          return res.status(response.status).json({
            error: lithicError.code,
            message: lithicError.userMessage,
            details: lithicError.details
          });
        }

        // Return successful response
        return res.status(response.status).json(responseData);
      } catch (fetchError) {
        // Use our error handler to parse the error
        const lithicError = lithicErrorHandler.handleLithicError(fetchError, lithicPath);

        // Check if we should retry the operation
        if (lithicErrorHandler.shouldRetryLithicOperation(lithicError) && retryAttempt < MAX_RETRY_ATTEMPTS) {
          // Calculate delay with exponential backoff
          const delay = BASE_RETRY_DELAY * Math.pow(2, retryAttempt);
          console.log(`Retrying Lithic API call (attempt ${retryAttempt + 1}/${MAX_RETRY_ATTEMPTS}) after ${delay}ms delay...`);

          // Wait for the calculated delay
          await new Promise(resolve => setTimeout(resolve, delay));

          // Retry the API call with incremented retry counter
          return makeLithicRequest(retryAttempt + 1);
        }

        // Return a mock response for development
        if (process.env.NODE_ENV !== 'production') {
          console.log('Returning mock response for development');
          return res.status(200).json({
            success: true,
            message: 'Mock response (Lithic API unavailable)',
            mock: true
          });
        }

        // If we shouldn't retry or have exceeded max retries, return the error response
        return res.status(500).json({
          error: lithicError.code,
          message: lithicError.userMessage,
          details: lithicError.details
        });
      }
    };

    // Start the request process
    return await makeLithicRequest();
  } catch (error) {
    console.error('Error in Lithic API proxy:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

// Register webhook routes
app.use('/webhooks', transactionWebhook);

// Bank Account Verification API (Flutterwave Proxy)
app.post('/api/verify-account', async (req, res) => {
  try {
    const { account_number, bank_code } = req.body;

    // Validate input
    if (!account_number || !bank_code) {
      return res.status(400).json({
        status: 'error',
        message: 'Account number and bank code are required'
      });
    }

    if (!/^\d{10}$/.test(account_number)) {
      return res.status(400).json({
        status: 'error',
        message: 'Account number must be exactly 10 digits'
      });
    }

    console.log('🏦 Verifying account:', { account_number, bank_code });

    // Call Flutterwave API
    const flutterwaveResponse = await fetch('https://api.flutterwave.com/v3/accounts/resolve', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${FLUTTERWAVE_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        account_number,
        account_bank: bank_code
      })
    });

    const flutterwaveData = await flutterwaveResponse.json();

    console.log('✅ Flutterwave response:', flutterwaveData);

    if (flutterwaveData.status === 'success' && flutterwaveData.data) {
      // Return successful verification
      return res.status(200).json({
        status: 'success',
        message: 'Account verified successfully',
        data: {
          account_number: flutterwaveData.data.account_number,
          account_name: flutterwaveData.data.account_name,
          bank_code: bank_code
        }
      });
    } else {
      // Return error from Flutterwave
      return res.status(400).json({
        status: 'error',
        message: flutterwaveData.message || 'Account verification failed'
      });
    }

  } catch (error) {
    console.error('❌ Account verification error:', error);

    // Return generic error
    return res.status(500).json({
      status: 'error',
      message: 'Internal server error during account verification'
    });
  }
});

// Get Nigerian Banks API (Flutterwave Proxy)
app.get('/api/banks', async (req, res) => {
  try {
    console.log('🏦 Fetching Nigerian banks from Flutterwave...');

    // Call Flutterwave API to get banks
    const flutterwaveResponse = await fetch('https://api.flutterwave.com/v3/banks/NG', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${FLUTTERWAVE_SECRET_KEY}`,
        'Content-Type': 'application/json',
      }
    });

    const flutterwaveData = await flutterwaveResponse.json();

    console.log(`✅ Fetched ${flutterwaveData.data?.length || 0} banks from Flutterwave`);

    if (flutterwaveData.status === 'success' && flutterwaveData.data) {
      // Return banks list
      return res.status(200).json({
        status: 'success',
        message: 'Banks fetched successfully',
        data: flutterwaveData.data.map(bank => ({
          code: bank.code,
          name: bank.name
        }))
      });
    } else {
      // Return error from Flutterwave
      return res.status(400).json({
        status: 'error',
        message: flutterwaveData.message || 'Failed to fetch banks'
      });
    }

  } catch (error) {
    console.error('❌ Banks fetch error:', error);

    // Return generic error
    return res.status(500).json({
      status: 'error',
      message: 'Internal server error while fetching banks'
    });
  }
});

// Register API routes
app.use('/api/notifications', notificationsRoutes);
app.use('/api/flutterwave-cards', flutterwaveCardsRoutes);
app.use('/api/bridgecard-test', bridgecardTestRoutes);
app.use('/api/v1', paymentGatewayRoutes);

// Serve static files from the 'dist' directory (for production) - with error handling
const distPath = path.join(__dirname, '../dist');
const fs = require('fs');

if (fs.existsSync(distPath)) {
  console.log('✅ Serving static files from dist directory');
  app.use(express.static(distPath));

  // For any other request, serve the index.html (for SPA routing)
  app.get('*', (req, res) => {
    const indexPath = path.join(distPath, 'index.html');
    if (fs.existsSync(indexPath)) {
      res.sendFile(indexPath);
    } else {
      res.status(404).json({
        error: 'Frontend not built',
        message: 'Run "npm run build" in the root directory to build the frontend',
        api_available: true,
        test_endpoints: [
          `http://localhost:${PORT}/api/bridgecard-test/debug`,
          `http://localhost:${PORT}/api/bridgecard-test/connection`
        ]
      });
    }
  });
} else {
  console.log('⚠️ Dist directory not found. Frontend not built.');
  console.log('💡 Run "npm run build" in the root directory to build the frontend');

  // For any request that's not an API route, return helpful message
  app.get('*', (req, res) => {
    // Skip API routes
    if (req.path.startsWith('/api/') || req.path.startsWith('/webhooks/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    res.status(200).json({
      message: 'SolPay Server is running!',
      status: 'Frontend not built',
      instructions: 'Run "npm run build" in the root directory to build the frontend',
      api_available: true,
      test_endpoints: {
        bridgecard_debug: `http://localhost:${PORT}/api/bridgecard-test/debug`,
        bridgecard_connection: `http://localhost:${PORT}/api/bridgecard-test/connection`,
        bridgecard_all_tests: `http://localhost:${PORT}/api/bridgecard-test/all`
      }
    });
  });
}

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Lithic API proxy available at http://localhost:${PORT}/api/lithic`);
});
