
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { setupAppStorage } from './utils/setupStorage'
import './utils/apiTest' // Import API tests for development
import './utils/rpcTest' // Import RPC tests for development

// Initialize app storage when app starts
setupAppStorage();

// Register Service Worker for PWA
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('solpay: SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('solpay: SW registration failed: ', registrationError);
      });
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
