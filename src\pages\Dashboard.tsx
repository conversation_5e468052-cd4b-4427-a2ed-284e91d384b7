import Layout from "@/components/Layout";
import TransactionList from "@/components/TransactionList";
import ActiveSubscriptions from "@/components/ActiveSubscriptions";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Wallet, CreditCard, CalendarCheck, RefreshCw, Plus, ArrowUpDown, Smartphone, Receipt, Shield, QrCode, Store, MessageCircle, Mic } from "lucide-react";
import CryptoLogo from "@/components/ui/CryptoLogo";
import { Button } from "@/components/ui/button";
import { useWallet } from "@/contexts/WalletContext";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { useCard } from "@/contexts/CardContext";
import { useAuth } from "@/contexts/AuthContext";
import { useState, useEffect } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import ProfileDisplay from "@/components/profile/ProfileDisplay";
import { getUserProfile } from "@/services/profileService";
import { Link } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import QuickOffRampWidget from "@/components/off-ramp/QuickOffRampWidget";
import { supabase } from "@/integrations/supabase/client";

const Dashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { wallets, transactions, isLoading: isWalletLoading, getTotalBalance, refreshWallets } = useWallet();
  const { userSubscriptions, isLoading: isSubscriptionLoading, getMonthlySpend, refreshSubscriptions } = useSubscription();
  const { card, waitlistEntry, isLoading: isCardLoading, refreshCard } = useCard();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [hasMerchantAccount, setHasMerchantAccount] = useState(false);

  // Load user profile and check merchant account
  useEffect(() => {
    const loadProfile = async () => {
      if (user) {
        try {
          const profile = await getUserProfile(user.id);
          setUserProfile(profile);

          // Check if user has merchant account
          const { data: merchantAccount } = await supabase
            .from('merchant_accounts')
            .select('id')
            .eq('user_id', user.id)
            .single();

          setHasMerchantAccount(!!merchantAccount);
        } catch (error) {
          console.error('Error loading user profile:', error);
        }
      }
    };

    loadProfile();
  }, [user]);

  // Initialize all data on component mount
  useEffect(() => {
    const initData = async () => {
      try {
        // Load data sequentially to avoid race conditions
        await refreshWallets();
        await refreshSubscriptions();
        await refreshCard();
      } catch (error) {
        console.error("Error initializing dashboard data:", error);
      } finally {
        setIsInitialized(true);
      }
    };

    // Only run this once when the component mounts
    if (!isInitialized) {
      initData();
    }
  }, []);

  // Format transactions for the TransactionList component - only take first 5 if they exist
  const formattedTransactions = transactions.length > 0
    ? transactions.slice(0, 5).map(tx => {
        return {
          id: tx.id,
          type: tx.type,
          amount: tx.amount,
          cryptoType: tx.tokenType,
          date: tx.date,
          service: tx.destination,
        };
      })
    : [];

  // Format subscriptions for the ActiveSubscriptions component - only if they exist
  const formattedSubscriptions = userSubscriptions?.length > 0
    ? userSubscriptions.map(sub => ({
        id: sub.subscription.id,
        name: sub.subscription.name,
        logo: sub.subscription.logo,
        nextPayment: sub.nextPayment,
        amount: sub.plan.price,
        currency: "mo",
      }))
    : [];

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Refresh all data - calling the imported functions directly
    await Promise.all([
      refreshWallets(),
      refreshSubscriptions(),
      refreshCard()
    ]);
    setIsRefreshing(false);
  };

  // Get time-based greeting
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
      return 'morning';
    } else if (hour < 17) {
      return 'afternoon';
    } else {
      return 'evening';
    }
  };

  const isLoading = isWalletLoading || isSubscriptionLoading || isCardLoading || !isInitialized;

  // Quick actions data - Keep it clean with just Add Funds
  const quickActions = [
    {
      icon: Plus,
      title: "Add Funds",
      subtitle: "Top up your wallet balance",
      color: "bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-600 hover:from-emerald-600 hover:via-teal-700 hover:to-cyan-700",
      iconColor: "text-white",
      textColor: "text-white",
      link: "/wallets"
    }
  ];

  return (
    <Layout>
      <div className="relative bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 min-h-screen overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-blue-200/40 to-cyan-200/40 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-indigo-200/30 to-purple-200/30 rounded-full mix-blend-multiply filter blur-2xl animate-pulse delay-4000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 py-6 space-y-8 z-10">
          {/* Header with Profile and Greeting */}
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4">
              <ProfileDisplay
                user={userProfile}
                size="lg"
                showName={true}
                showEmail={false}
                showVerificationBadge={true}
                layout="horizontal"
                className="bg-white rounded-xl p-4 shadow-sm border"
              />
            </div>

            {/* Greeting - Always visible on mobile and desktop */}
            <div className="space-y-1">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                Good {getTimeBasedGreeting()}!
              </h1>
              <p className="text-gray-600 text-sm sm:text-base">Welcome back to SolPay</p>
            </div>
          </div>

          {/* Balance Overview */}
          <Card className="relative bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white border-0 shadow-2xl overflow-hidden">
            {/* Animated background elements */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
              <div className="absolute top-0 right-0 w-72 h-72 bg-gradient-to-br from-cyan-400 to-blue-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
              <div className="absolute bottom-0 left-1/2 w-72 h-72 bg-gradient-to-br from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"></div>
            </div>

            {/* Subtle pattern overlay */}
            <div className="absolute inset-0 opacity-10" style={{
              backgroundImage: `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)`,
              backgroundSize: '100% 100%'
            }}></div>

            <CardContent className="relative p-8 z-10">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                <div className="space-y-6">
                  {/* Total Balance USD - Main display at top */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <p className="text-purple-200 text-sm font-medium tracking-wide">Total Balance</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                        className="h-7 w-7 p-0 text-purple-200 hover:text-white hover:bg-white/20 rounded-full transition-all duration-300"
                      >
                        <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                      </Button>
                    </div>
                    {isLoading ? (
                      <Skeleton className="h-14 w-56 bg-white/20 rounded-lg" />
                    ) : (
                      <div className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
                        ${getTotalBalance().toLocaleString('en-US', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        })}
                      </div>
                    )}
                  </div>

                  {/* Individual Crypto Balances - Show real logos */}
                  <div className="space-y-3">
                    {wallets.length > 0 && wallets[0]?.tokens?.map((token) => (
                      <div key={token.type} className="flex items-center gap-3">
                        <CryptoLogo symbol={token.type} size="md" />
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-purple-200">
                            {token.balance.toLocaleString('en-US', {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 6
                            })} {token.type}
                          </span>
                          <span className="text-xs text-purple-300">
                            ≈ ${token.dollarValue.toFixed(2)}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-8 text-center">
                  <div className="space-y-2">
                    <p className="text-purple-200 text-sm font-medium">Monthly Spend</p>
                    {isLoading ? (
                      <Skeleton className="h-8 w-24 bg-white/20 mx-auto rounded-lg" />
                    ) : (
                      <p className="text-3xl font-bold text-white">${getMonthlySpend().toFixed(2)}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <p className="text-purple-200 text-sm font-medium">Active Services</p>
                    {isLoading ? (
                      <Skeleton className="h-8 w-16 bg-white/20 mx-auto rounded-lg" />
                    ) : (
                      <p className="text-3xl font-bold text-white">{userSubscriptions?.length || 0}</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Quick Actions</h2>
            <div className="max-w-sm">
              {quickActions.map((action, index) => (
                <Link key={index} to={action.link}>
                  <Card className={`relative ${action.color} border-0 transition-all duration-500 cursor-pointer hover:scale-105 hover:shadow-2xl group overflow-hidden`}>
                    {/* Animated background elements */}
                    <div className="absolute inset-0 opacity-30">
                      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white to-transparent rounded-full mix-blend-overlay filter blur-xl group-hover:scale-150 transition-transform duration-700"></div>
                      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-white to-transparent rounded-full mix-blend-overlay filter blur-lg group-hover:scale-125 transition-transform duration-500"></div>
                    </div>

                    <CardContent className="relative p-8 space-y-4 z-10">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm group-hover:bg-white/30 transition-all duration-300">
                          <action.icon className={`h-8 w-8 ${action.iconColor} group-hover:scale-110 transition-transform duration-300`} />
                        </div>
                        <div className="space-y-1">
                          <h3 className={`text-xl font-bold ${action.textColor} group-hover:text-white transition-colors duration-300`}>{action.title}</h3>
                          <p className={`text-sm ${action.textColor} opacity-90 leading-tight group-hover:opacity-100 transition-opacity duration-300`}>
                            {action.subtitle}
                          </p>
                        </div>
                      </div>

                      {/* Subtle arrow indicator */}
                      <div className="flex justify-end">
                        <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 group-hover:scale-110 transition-all duration-300">
                          <ArrowUpDown className="h-4 w-4 text-white rotate-45" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>

          {/* New Features Section - Clean and Mobile-Friendly */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">🚀 New Features</h2>
                <p className="text-sm text-gray-600">Revolutionary ways to use your crypto</p>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {/* QR Scanner */}
              <Card
                className="cursor-pointer hover:shadow-md transition-all duration-200 border-purple-200 hover:border-purple-300"
                onClick={() => navigate('/qr-scanner')}
              >
                <CardContent className="p-3 text-center">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <QrCode className="h-5 w-5 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-xs text-gray-900">QR Scanner</h3>
                  <p className="text-xs text-gray-600 mt-1">Pay merchants</p>
                </CardContent>
              </Card>

              {/* Merchant Register */}
              <Card
                className="cursor-pointer hover:shadow-md transition-all duration-200 border-orange-200 hover:border-orange-300"
                onClick={() => navigate(hasMerchantAccount ? '/merchant-dashboard' : '/merchant-register')}
              >
                <CardContent className="p-3 text-center">
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Store className="h-5 w-5 text-orange-600" />
                  </div>
                  <h3 className="font-semibold text-xs text-gray-900">
                    {hasMerchantAccount ? 'My Store' : 'Merchant'}
                  </h3>
                  <p className="text-xs text-gray-600 mt-1">
                    {hasMerchantAccount ? 'View QR & sales' : 'Accept crypto'}
                  </p>
                </CardContent>
              </Card>

              {/* Telegram Bot */}
              <Card
                className="cursor-pointer hover:shadow-md transition-all duration-200 border-blue-200 hover:border-blue-300"
                onClick={() => navigate('/telegram')}
              >
                <CardContent className="p-3 text-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <MessageCircle className="h-5 w-5 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-xs text-gray-900">Telegram</h3>
                  <p className="text-xs text-gray-600 mt-1">Chat control</p>
                </CardContent>
              </Card>

              {/* Voice Commands */}
              <Card
                className="cursor-pointer hover:shadow-md transition-all duration-200 border-pink-200 hover:border-pink-300"
                onClick={() => navigate('/voice-commands')}
              >
                <CardContent className="p-3 text-center">
                  <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Mic className="h-5 w-5 text-pink-600" />
                  </div>
                  <h3 className="font-semibold text-xs text-gray-900">Voice</h3>
                  <p className="text-xs text-gray-600 mt-1">Speak to pay</p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Virtual Card & Off-Ramp Quick Access */}
          <div className="grid lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Virtual Card</h2>
                <Link to="/virtual-card">
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                </Link>
              </div>

              {waitlistEntry ? (
                /* Waitlist Status Card */
                <Card className="relative bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 text-white border-0 shadow-2xl overflow-hidden">
                  {/* Animated background elements */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
                    <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-pink-400 to-red-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
                    <div className="absolute top-1/2 left-1/2 w-36 h-36 bg-gradient-to-br from-cyan-400 to-blue-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000 transform -translate-x-1/2 -translate-y-1/2"></div>
                  </div>

                  {/* Waitlist Badge */}
                  <div className="absolute top-4 right-4 z-20">
                    <div className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                      WAITLIST #{waitlistEntry.position}
                    </div>
                  </div>

                  {/* SolPay watermark */}
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    <div className="text-center opacity-10 transform rotate-12">
                      <div className="text-6xl font-bold tracking-wider text-white">SolPay</div>
                      <div className="text-sm font-light tracking-widest mt-2 text-white">COMING SOON</div>
                    </div>
                  </div>

                  <CardContent className="relative p-8 z-10">
                    <div className="text-center space-y-6">
                      {/* Status Icon */}
                      <div className="mx-auto w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                        <div className="w-16 h-16 bg-white/30 rounded-full flex items-center justify-center">
                          <CreditCard className="w-8 h-8 text-white animate-pulse" />
                        </div>
                      </div>

                      {/* Waitlist Info */}
                      <div className="space-y-3">
                        <h3 className="text-2xl font-bold text-white">
                          Your Card is Coming! 🎉
                        </h3>
                        <p className="text-white/90 text-lg">
                          You're #{waitlistEntry.position} in line
                        </p>
                        <p className="text-white/80 text-sm max-w-md mx-auto leading-relaxed">
                          {waitlistEntry.position <= 50 ?
                            "🔥 You're in the VIP zone! Your card will be ready in 1-2 weeks." :
                            waitlistEntry.position <= 200 ?
                            "⚡ Almost there! Expected wait time: 2-4 weeks." :
                            "🚀 We're working fast to get your card ready!"
                          }
                        </p>
                      </div>

                      {/* Progress Indicator */}
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm text-white/80">
                          <span>Position #{waitlistEntry.position}</span>
                          <span>{waitlistEntry.referrals_count} referrals made</span>
                        </div>
                        <div className="bg-white/20 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-yellow-400 to-orange-400 h-2 rounded-full transition-all duration-1000"
                            style={{
                              width: `${Math.max(10, Math.min(90, (1000 - waitlistEntry.position) / 10))}%`
                            }}
                          ></div>
                        </div>
                      </div>

                      {/* Card Details Preview */}
                      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 space-y-2">
                        <div className="flex justify-between items-center">
                          <div className="text-left">
                            <p className="text-white/70 text-xs font-medium tracking-widest">CARD HOLDER</p>
                            <p className="font-semibold text-white">{waitlistEntry.card_name}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-white/70 text-xs font-medium tracking-widest">PROVIDER</p>
                            <p className="font-semibold text-white">{waitlistEntry.card_provider}</p>
                          </div>
                        </div>
                        <div className="text-center pt-2">
                          <p className="text-white/70 text-xs">Ready for Netflix, Twitter Blue, and more!</p>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3 justify-center">
                        <Link to="/waitlist-details">
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm"
                          >
                            View Details
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-yellow-400/20 border-yellow-400/30 text-yellow-100 hover:bg-yellow-400/30 backdrop-blur-sm"
                          onClick={() => {
                            const referralLink = `http://localhost:8081/card-registration?ref=${waitlistEntry.referral_code}`;
                            navigator.clipboard.writeText(referralLink);
                            toast({
                              title: "Referral Link Copied! 🎉",
                              description: "Share with friends to move up 5 spots in line!",
                            });
                          }}
                        >
                          Share & Skip Line
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : card ? (
                <Card className="relative bg-gradient-to-br from-slate-900 via-gray-900 to-black text-white border-0 shadow-2xl overflow-hidden">
                  {/* Animated background elements */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
                    <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-blue-500 to-cyan-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
                  </div>

                  {/* SolPay watermark - more prominent */}
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    <div className="text-center opacity-10 transform rotate-12">
                      <div className="text-6xl font-bold tracking-wider text-white">SolPay</div>
                      <div className="text-sm font-light tracking-widest mt-2 text-white">CRYPTO CARD</div>
                    </div>
                  </div>

                  {/* Subtle pattern overlay */}
                  <div className="absolute inset-0 opacity-5" style={{
                    backgroundImage: `radial-gradient(circle at 20% 80%, rgba(147, 51, 234, 0.4) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.4) 0%, transparent 50%)`,
                    backgroundSize: '100% 100%'
                  }}></div>

                  <CardContent className="relative p-8 z-10">
                    <div className="flex justify-between items-start mb-8">
                      <div className="space-y-2">
                        <p className="text-gray-300 text-sm font-medium tracking-wide">Virtual Card</p>
                        <p className="font-mono text-2xl mt-2 tracking-wider">
                          •••• •••• •••• {card.number.slice(-4)}
                        </p>
                      </div>
                      <div className="text-right space-y-2">
                        <p className="text-gray-300 text-sm font-medium">Balance</p>
                        <p className="text-3xl font-bold bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                          ${card.balance.toFixed(2)}
                        </p>
                      </div>
                    </div>

                    <div className="flex justify-between items-end">
                      <div className="space-y-1">
                        <p className="text-gray-400 text-xs font-medium tracking-widest">CARD HOLDER</p>
                        <p className="font-semibold text-lg">{card.name}</p>
                      </div>
                      <div className="text-right space-y-1">
                        <p className="text-gray-400 text-xs font-medium tracking-widest">EXPIRES</p>
                        <p className="font-semibold text-lg">{card.expiry}</p>
                      </div>
                    </div>

                    {/* Card provider logo area */}
                    <div className="absolute top-6 right-6">
                      <div className="w-12 h-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-md flex items-center justify-center">
                        <span className="text-black text-xs font-bold">VISA</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="relative border-2 border-dashed border-purple-300 hover:border-purple-400 transition-all duration-300 bg-gradient-to-br from-purple-50 to-indigo-50 overflow-hidden group">
                  {/* Animated background */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl group-hover:scale-150 transition-transform duration-700"></div>
                  </div>

                  <CardContent className="relative p-8 text-center z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                      <CreditCard className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">No Virtual Card</h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">Create your first virtual card for global payments and start your crypto journey</p>
                    <Link to="/card-registration">
                      <Button className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg">
                        Create Card
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Quick Off-Ramp Widget */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Crypto Off-Ramp</h2>
                <Link to="/off-ramp">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </div>
              <QuickOffRampWidget />
            </div>
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
                <Link to="/transaction-history">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </div>

              {isWalletLoading ? (
                <Card>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <Skeleton className="h-12 w-full" />
                      <Skeleton className="h-12 w-full" />
                      <Skeleton className="h-12 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="shadow-sm">
                  <CardContent className="p-0">
                    <TransactionList transactions={formattedTransactions.slice(0, 5)} />
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Active Subscriptions */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Active Services</h2>
              <Link to="/services">
                <Button variant="outline" size="sm">
                  Manage All
                </Button>
              </Link>
            </div>

            <Card className="shadow-sm">
              <CardContent className="p-0">
                <ActiveSubscriptions
                  subscriptions={formattedSubscriptions}
                  showSkeleton={isSubscriptionLoading}
                />
              </CardContent>
            </Card>
          </div>

          {/* Footer */}
          <div className="text-center py-8">
            <div className="flex items-center justify-center gap-2 text-gray-500 mb-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">S</span>
              </div>
              <span className="font-semibold">SolPay</span>
            </div>
            <p className="text-sm text-gray-400">
              Your trusted crypto payment platform
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
