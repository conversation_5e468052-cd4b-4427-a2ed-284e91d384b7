/**
 * Payment Intent Validation
 * 
 * Validates payment intent creation and update requests
 */

interface ValidationResult {
  success: boolean;
  error?: string;
}

const SUPPORTED_CURRENCIES = ['USD', 'EUR', 'GBP', 'NGN'];
const SUPPORTED_CRYPTOCURRENCIES = ['SOL', 'USDC', 'ETH', 'BTC'];
const SETTLEMENT_CURRENCIES = ['NGN', 'crypto'];

export const validatePaymentIntent = (data: any): ValidationResult => {
  // Required fields
  if (!data.amount) {
    return { success: false, error: 'Amount is required' };
  }

  if (typeof data.amount !== 'number' || data.amount <= 0) {
    return { success: false, error: 'Amount must be a positive number' };
  }

  if (data.amount < 50) { // Minimum 50 cents
    return { success: false, error: 'Amount must be at least 50 cents' };
  }

  if (data.amount > 100000000) { // Maximum $1M
    return { success: false, error: 'Amount cannot exceed $1,000,000' };
  }

  // Currency validation
  if (data.currency && !SUPPORTED_CURRENCIES.includes(data.currency)) {
    return { 
      success: false, 
      error: `Currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}` 
    };
  }

  // Cryptocurrency validation
  if (data.accepted_cryptocurrencies) {
    if (!Array.isArray(data.accepted_cryptocurrencies)) {
      return { success: false, error: 'accepted_cryptocurrencies must be an array' };
    }

    if (data.accepted_cryptocurrencies.length === 0) {
      return { success: false, error: 'At least one cryptocurrency must be accepted' };
    }

    for (const crypto of data.accepted_cryptocurrencies) {
      if (!SUPPORTED_CRYPTOCURRENCIES.includes(crypto)) {
        return { 
          success: false, 
          error: `Unsupported cryptocurrency: ${crypto}. Supported: ${SUPPORTED_CRYPTOCURRENCIES.join(', ')}` 
        };
      }
    }
  }

  // Settlement currency validation
  if (data.settlement_currency && !SETTLEMENT_CURRENCIES.includes(data.settlement_currency)) {
    return { 
      success: false, 
      error: `Settlement currency must be one of: ${SETTLEMENT_CURRENCIES.join(', ')}` 
    };
  }

  // Email validation
  if (data.customer_email && !isValidEmail(data.customer_email)) {
    return { success: false, error: 'Invalid customer email format' };
  }

  // URL validation
  if (data.success_url && !isValidUrl(data.success_url)) {
    return { success: false, error: 'Invalid success_url format' };
  }

  if (data.cancel_url && !isValidUrl(data.cancel_url)) {
    return { success: false, error: 'Invalid cancel_url format' };
  }

  // Description validation
  if (data.description && typeof data.description !== 'string') {
    return { success: false, error: 'Description must be a string' };
  }

  if (data.description && data.description.length > 500) {
    return { success: false, error: 'Description cannot exceed 500 characters' };
  }

  // Customer name validation
  if (data.customer_name && typeof data.customer_name !== 'string') {
    return { success: false, error: 'Customer name must be a string' };
  }

  if (data.customer_name && data.customer_name.length > 100) {
    return { success: false, error: 'Customer name cannot exceed 100 characters' };
  }

  // Metadata validation
  if (data.metadata) {
    if (typeof data.metadata !== 'object' || Array.isArray(data.metadata)) {
      return { success: false, error: 'Metadata must be an object' };
    }

    const metadataString = JSON.stringify(data.metadata);
    if (metadataString.length > 2000) {
      return { success: false, error: 'Metadata cannot exceed 2000 characters when serialized' };
    }

    // Check for reserved metadata keys
    const reservedKeys = ['exchange_rates', 'created_via', 'internal_id'];
    for (const key of reservedKeys) {
      if (data.metadata.hasOwnProperty(key)) {
        return { success: false, error: `Metadata cannot contain reserved key: ${key}` };
      }
    }
  }

  return { success: true };
};

export const validatePaymentIntentUpdate = (data: any): ValidationResult => {
  // Only allow specific fields to be updated
  const allowedFields = ['description', 'metadata', 'customer_email', 'customer_name'];
  const providedFields = Object.keys(data);
  
  for (const field of providedFields) {
    if (!allowedFields.includes(field)) {
      return { 
        success: false, 
        error: `Field '${field}' cannot be updated. Allowed fields: ${allowedFields.join(', ')}` 
      };
    }
  }

  // Validate individual fields if provided
  if (data.description !== undefined) {
    if (typeof data.description !== 'string') {
      return { success: false, error: 'Description must be a string' };
    }
    if (data.description.length > 500) {
      return { success: false, error: 'Description cannot exceed 500 characters' };
    }
  }

  if (data.customer_email !== undefined) {
    if (data.customer_email !== null && !isValidEmail(data.customer_email)) {
      return { success: false, error: 'Invalid customer email format' };
    }
  }

  if (data.customer_name !== undefined) {
    if (data.customer_name !== null && typeof data.customer_name !== 'string') {
      return { success: false, error: 'Customer name must be a string' };
    }
    if (data.customer_name && data.customer_name.length > 100) {
      return { success: false, error: 'Customer name cannot exceed 100 characters' };
    }
  }

  if (data.metadata !== undefined) {
    if (data.metadata !== null && (typeof data.metadata !== 'object' || Array.isArray(data.metadata))) {
      return { success: false, error: 'Metadata must be an object' };
    }
    
    if (data.metadata) {
      const metadataString = JSON.stringify(data.metadata);
      if (metadataString.length > 2000) {
        return { success: false, error: 'Metadata cannot exceed 2000 characters when serialized' };
      }

      // Check for reserved metadata keys
      const reservedKeys = ['exchange_rates', 'created_via', 'internal_id'];
      for (const key of reservedKeys) {
        if (data.metadata.hasOwnProperty(key)) {
          return { success: false, error: `Metadata cannot contain reserved key: ${key}` };
        }
      }
    }
  }

  return { success: true };
};

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

const isValidUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
};
