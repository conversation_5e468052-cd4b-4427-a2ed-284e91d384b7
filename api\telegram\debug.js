/**
 * Debug endpoint to check bot configuration
 */

export default async function handler(req, res) {
  const botToken = process.env.TELEGRAM_BOT_TOKEN;
  
  if (!botToken) {
    return res.status(500).json({ 
      error: 'TELEGRAM_BOT_TOKEN not configured'
    });
  }

  try {
    // Get bot info
    const botResponse = await fetch(`https://api.telegram.org/bot${botToken}/getMe`);
    const botInfo = await botResponse.json();
    
    // Get webhook info
    const webhookResponse = await fetch(`https://api.telegram.org/bot${botToken}/getWebhookInfo`);
    const webhookInfo = await webhookResponse.json();
    
    return res.status(200).json({
      botToken: botToken.substring(0, 20) + '...',
      botInfo: botInfo.result,
      webhookInfo: webhookInfo.result,
      expectedBotId: 8129108374,
      expectedUsername: 'Solpay_crypto_bot',
      isCorrectBot: botInfo.result?.id === 8129108374,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return res.status(500).json({
      error: 'Failed to get bot info',
      details: error.message
    });
  }
}
