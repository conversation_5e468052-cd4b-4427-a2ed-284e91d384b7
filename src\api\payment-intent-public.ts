/**
 * Public Payment Intent API
 * 
 * This endpoint allows public access to payment intents for payment links
 * Uses service role to bypass RLS restrictions
 */

import { createClient } from '@supabase/supabase-js';

// Create service role client
const supabaseServiceRole = createClient(
  import.meta.env.VITE_SUPABASE_URL || "https://agnetkiywvjdufmwajle.supabase.co",
  // Note: In production, this should be handled by a backend API
  // For now, we'll use a workaround
  import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnbmV0a2l5d3ZqZHVmbXdhamxlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NTYyMzEsImV4cCI6MjA2MzIzMjIzMX0.kDGLNmJG7chA3MUoKiyxpzz2SM6u5GJ4HH6d0Adlzjo"
);

export interface PaymentIntentResponse {
  id: string;
  payment_gateway_merchant_id: string;
  client_secret: string;
  amount: number;
  currency: string;
  settlement_currency: string;
  accepted_cryptocurrencies: string[];
  status: string;
  title?: string;
  description?: string;
  type?: string;
  supported_chains?: string[];
  supported_tokens?: string[];
  link_url?: string;
  expires_at?: string;
  payment_method?: string;
  paid_at?: string;
  created_at?: string;
  updated_at?: string;
  metadata?: any;
  payment_gateway_merchants?: {
    id: string;
    business_name: string;
    business_type: string;
    website_url?: string;
    [key: string]: any;
  };
  wallet_configs?: Array<{
    chain: string;
    token: string;
    wallet_address: string;
    is_active: boolean;
  }>;
}

/**
 * Fetch payment intent by ID with public access
 */
export async function fetchPaymentIntentPublic(paymentId: string): Promise<PaymentIntentResponse | null> {
  try {
    console.log('🔍 Fetching payment intent:', paymentId);

    // First, try to get the payment intent directly
    const { data: payment, error: paymentError } = await (supabaseServiceRole as any)
      .from('payment_intents')
      .select(`
        *,
        payment_gateway_merchants!inner(
          id,
          business_name,
          business_type,
          website_url
        )
      `)
      .eq('id', paymentId)
      .single();

    console.log('📊 Payment query result:', { payment, paymentError });

    if (paymentError) {
      console.error('Payment error:', paymentError);

      // If RLS is blocking, try a simpler query
      if (paymentError.code === 'PGRST116' || paymentError.message?.includes('row')) {
        console.log('🔄 RLS detected, trying public access method...');

        // Try to fetch just the payment intent without joins
        const { data: simplePayment, error: simpleError } = await (supabaseServiceRole as any)
          .from('payment_intents')
          .select('*')
          .eq('id', paymentId)
          .maybeSingle();

        console.log('📊 Simple query result:', { simplePayment, simpleError });

        if (simpleError || !simplePayment) {
          console.error('Failed to fetch payment intent:', simpleError?.message || 'Payment ID not found in database');
          return null;
        }

        // Now fetch merchant info separately
        const { data: merchant, error: merchantError } = await (supabaseServiceRole as any)
          .from('payment_gateway_merchants')
          .select('id, business_name, business_type, website_url')
          .eq('id', simplePayment.payment_gateway_merchant_id)
          .single();

        console.log('🏪 Merchant query result:', { merchant, merchantError });

        // Fetch wallet configurations
        let merchantWithWallets = merchant;
        if (merchant) {
          const { data: walletConfigs, error: walletError } = await (supabaseServiceRole as any)
            .from('merchant_wallet_configs')
            .select('chain, token, wallet_address, is_active')
            .eq('payment_gateway_merchant_id', simplePayment.payment_gateway_merchant_id)
            .eq('is_active', true);

          console.log('💰 Wallet configs result:', { walletConfigs, walletError });

          if (!walletError && walletConfigs) {
            // Add wallet addresses to merchant object for backward compatibility
            walletConfigs.forEach(config => {
              const walletKey = `${config.chain}_wallet_address`;
              merchantWithWallets[walletKey] = config.wallet_address;
            });
          }
        }

        // Combine the data
        const combinedPayment = {
          ...simplePayment,
          payment_gateway_merchants: merchantWithWallets,
          wallet_configs: walletConfigs || []
        };

        console.log('✅ Combined payment data:', combinedPayment);
        return combinedPayment;
      }

      return null;
    }

    if (!payment) {
      console.error('Payment not found: No payment intent found with this ID');
      return null;
    }

    // Fetch wallet configurations for the merchant
    if (payment.payment_gateway_merchants) {
      const { data: walletConfigs, error: walletError } = await (supabaseServiceRole as any)
        .from('merchant_wallet_configs')
        .select('chain, token, wallet_address, is_active')
        .eq('payment_gateway_merchant_id', payment.payment_gateway_merchant_id)
        .eq('is_active', true);

      console.log('💰 Wallet configs result:', { walletConfigs, walletError });

      if (!walletError && walletConfigs) {
        // Add wallet addresses to merchant object for backward compatibility
        walletConfigs.forEach(config => {
          const walletKey = `${config.chain}_wallet_address`;
          payment.payment_gateway_merchants[walletKey] = config.wallet_address;
        });

        // Also add wallet_configs array
        payment.wallet_configs = walletConfigs;
      }
    }

    console.log('✅ Final payment data:', payment);
    return payment;

  } catch (error) {
    console.error('Error fetching payment intent:', error);
    return null;
  }
}
