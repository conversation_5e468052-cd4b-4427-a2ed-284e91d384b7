
/**
 * Group Card Rules Component
 */
import React, { useState } from 'react';
import { Plus, Shield, X, Check, Calendar, MapPin, Tag, Store, MoreHorizontal, BookOpen } from 'lucide-react';
import { useGroupCard } from '@/contexts/GroupCardContext';
import { GroupCardSpendingRuleType, SpendingRuleValue } from '@/types/groupCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import RuleTemplateSelector from './RuleTemplateSelector';
import type { RuleTemplate } from '@/data/groupCardRuleTemplates';

export default function GroupCardRules() {
  const { selectedCard, members, rules, isLoading, addRule, deleteRule } = useGroupCard();
  const [addRuleDialogOpen, setAddRuleDialogOpen] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [ruleType, setRuleType] = useState<GroupCardSpendingRuleType>(GroupCardSpendingRuleType.CATEGORY);
  const [isWhitelist, setIsWhitelist] = useState(true);
  const [ruleValue, setRuleValue] = useState('');
  const [isAddingRule, setIsAddingRule] = useState(false);
  const [isDeletingRule, setIsDeletingRule] = useState<Record<string, boolean>>({});
  const [selectedTemplateRules, setSelectedTemplateRules] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('custom');

  // Handle add rule
  const handleAddRule = async () => {
    if (!ruleValue.trim()) return;

    setIsAddingRule(true);
    try {
      let parsedRuleValue: SpendingRuleValue;

      switch (ruleType) {
        case GroupCardSpendingRuleType.CATEGORY:
          parsedRuleValue = {
            categories: ruleValue.split(',').map(c => c.trim()),
          };
          break;
        case GroupCardSpendingRuleType.MERCHANT:
          parsedRuleValue = {
            merchants: ruleValue.split(',').map(m => m.trim()),
          };
          break;
        case GroupCardSpendingRuleType.TIME:
          // This would be more complex in a real implementation
          parsedRuleValue = {
            daysOfWeek: [1, 2, 3, 4, 5], // Mon-Fri
            startTime: '09:00',
            endTime: '17:00',
          };
          break;
        case GroupCardSpendingRuleType.GEO:
          parsedRuleValue = {
            countries: ruleValue.split(',').map(c => c.trim()),
          };
          break;
        default:
          throw new Error('Invalid rule type');
      }

      await addRule(selectedMemberId, ruleType, parsedRuleValue, isWhitelist);

      // Reset form
      setRuleValue('');
      setAddRuleDialogOpen(false);
    } finally {
      setIsAddingRule(false);
    }
  };

  // Handle delete rule
  const handleDeleteRule = async (ruleId: string) => {
    setIsDeletingRule(prev => ({ ...prev, [ruleId]: true }));
    try {
      await deleteRule(ruleId);
    } finally {
      setIsDeletingRule(prev => ({ ...prev, [ruleId]: false }));
    }
  };

  // Handle template rule selection
  const handleSelectTemplateRule = async (template: RuleTemplate) => {
    if (selectedTemplateRules.includes(template.id)) {
      return; // Already selected
    }

    setIsAddingRule(true);
    try {
      // Convert template to a spending rule
      // For now, we'll add it as a category rule with the template description
      const ruleValue: SpendingRuleValue = {
        categories: [template.title], // Use template title as category
        description: template.rule, // Store the full rule text
      };

      await addRule(
        null, // Apply to all members
        GroupCardSpendingRuleType.CATEGORY,
        ruleValue,
        template.severity !== 'strict' // Whitelist unless it's a strict rule
      );

      // Mark as selected
      setSelectedTemplateRules(prev => [...prev, template.id]);

      // Show success message
      console.log(`Added rule template: ${template.title}`);
    } catch (error) {
      console.error('Error adding template rule:', error);
    } finally {
      setIsAddingRule(false);
    }
  };

  // Get rule type icon
  const getRuleTypeIcon = (type: GroupCardSpendingRuleType) => {
    switch (type) {
      case GroupCardSpendingRuleType.CATEGORY:
        return <Tag className="h-4 w-4" />;
      case GroupCardSpendingRuleType.MERCHANT:
        return <Store className="h-4 w-4" />;
      case GroupCardSpendingRuleType.TIME:
        return <Calendar className="h-4 w-4" />;
      case GroupCardSpendingRuleType.GEO:
        return <MapPin className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  // Format rule value for display
  const formatRuleValue = (type: GroupCardSpendingRuleType, value: any) => {
    switch (type) {
      case GroupCardSpendingRuleType.CATEGORY:
        return value.categories?.join(', ') || 'No categories';
      case GroupCardSpendingRuleType.MERCHANT:
        return value.merchants?.join(', ') || 'No merchants';
      case GroupCardSpendingRuleType.TIME:
        return `${value.startTime || '00:00'} - ${value.endTime || '23:59'}`;
      case GroupCardSpendingRuleType.GEO:
        return value.countries?.join(', ') || 'No countries';
      default:
        return 'Unknown rule value';
    }
  };

  // Render loading state
  if (isLoading || !selectedCard) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Group Card Rules</h2>
        <div className="flex items-center gap-2">
          <Dialog open={addRuleDialogOpen} onOpenChange={setAddRuleDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Custom Rule
              </Button>
            </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Spending Rule</DialogTitle>
              <DialogDescription>
                Create rules to control how the card can be used.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="member">Apply to Member</Label>
                <Select value={selectedMemberId || ''} onValueChange={setSelectedMemberId}>
                  <SelectTrigger id="member">
                    <SelectValue placeholder="All members" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All members</SelectItem>
                    {members.map(member => (
                      <SelectItem key={member.id} value={member.id}>
                        {member.user?.fullName || member.user?.email || 'Unknown'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="rule-type">Rule Type</Label>
                <Select value={ruleType} onValueChange={(value) => setRuleType(value as GroupCardSpendingRuleType)}>
                  <SelectTrigger id="rule-type">
                    <SelectValue placeholder="Select rule type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={GroupCardSpendingRuleType.CATEGORY}>Category</SelectItem>
                    <SelectItem value={GroupCardSpendingRuleType.MERCHANT}>Merchant</SelectItem>
                    <SelectItem value={GroupCardSpendingRuleType.TIME}>Time</SelectItem>
                    <SelectItem value={GroupCardSpendingRuleType.GEO}>Location</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="is-whitelist">
                  {isWhitelist ? 'Allow only these' : 'Block these'}
                </Label>
                <Switch
                  id="is-whitelist"
                  checked={isWhitelist}
                  onCheckedChange={setIsWhitelist}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rule-value">
                  {ruleType === GroupCardSpendingRuleType.CATEGORY && 'Categories (comma separated)'}
                  {ruleType === GroupCardSpendingRuleType.MERCHANT && 'Merchants (comma separated)'}
                  {ruleType === GroupCardSpendingRuleType.TIME && 'Time Settings'}
                  {ruleType === GroupCardSpendingRuleType.GEO && 'Countries (comma separated)'}
                </Label>
                <Textarea
                  id="rule-value"
                  value={ruleValue}
                  onChange={(e) => setRuleValue(e.target.value)}
                  placeholder={
                    ruleType === GroupCardSpendingRuleType.CATEGORY ? 'Groceries, Restaurants, Entertainment' :
                    ruleType === GroupCardSpendingRuleType.MERCHANT ? 'Amazon, Netflix, Uber' :
                    ruleType === GroupCardSpendingRuleType.TIME ? 'Time settings not implemented in this demo' :
                    'US, CA, UK'
                  }
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleAddRule}
                disabled={isAddingRule || !ruleValue.trim()}
              >
                {isAddingRule ? 'Adding Rule...' : 'Add Rule'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Rule Templates
          </TabsTrigger>
          <TabsTrigger value="custom" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Custom Rules
          </TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="mt-6">
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">Choose from Pre-written Rules</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Select from professionally written rules to quickly set up your group card policies.
              </p>
            </div>
            <RuleTemplateSelector
              onSelectRule={handleSelectTemplateRule}
              selectedRules={selectedTemplateRules}
            />
          </div>
        </TabsContent>

        <TabsContent value="custom" className="mt-6">
          <div className="space-y-4">
        {rules.length === 0 ? (
          <Card>
            <CardContent className="pt-6 text-center">
              <p className="text-muted-foreground">No spending rules defined</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setAddRuleDialogOpen(true)}
              >
                Add Your First Rule
              </Button>
            </CardContent>
          </Card>
        ) : (
          rules.map((rule) => {
            const member = members.find(m => m.id === rule.memberId);

            return (
              <Card key={rule.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      {getRuleTypeIcon(rule.ruleType)}
                      <CardTitle className="text-base capitalize">
                        {rule.ruleType.toLowerCase()} Rule
                      </CardTitle>
                      <Badge variant={rule.isWhitelist ? 'outline' : 'destructive'} className="capitalize">
                        {rule.isWhitelist ? 'Whitelist' : 'Blacklist'}
                      </Badge>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => handleDeleteRule(rule.id)}
                          disabled={isDeletingRule[rule.id]}
                        >
                          {isDeletingRule[rule.id] ? 'Deleting...' : 'Delete Rule'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription>
                    {rule.memberId ? `Applies to ${member?.user?.fullName || 'specific member'}` : 'Applies to all members'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p>
                    {rule.isWhitelist ? 'Only allow' : 'Block'}{' '}
                    <span className="font-medium">
                      {formatRuleValue(rule.ruleType, rule.ruleValue)}
                    </span>
                  </p>
                </CardContent>
              </Card>
            );
          })
        )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
