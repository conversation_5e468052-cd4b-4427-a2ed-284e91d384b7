import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  ArrowDown,
  DollarSign,
  Clock,
  TrendingUp,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Zap,
  Copy
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { CrossChainOffRampService, CrossChainOffRampQuote } from '@/services/crossChainOffRampService';
import { CrossChainWalletService } from '@/services/crossChainWalletService';
import { 
  SupportedChain, 
  CrossChainWallet,
  CHAIN_CONFIGS 
} from '@/types/crossChain';
import { toast } from '@/hooks/use-toast';

interface BankAccount {
  bankName: string;
  accountNumber: string;
  accountName: string;
}

export const CrossChainOffRamp: React.FC = () => {
  const { user } = useAuth();
  const [wallets, setWallets] = useState<CrossChainWallet[]>([]);
  const [selectedChain, setSelectedChain] = useState<SupportedChain>(SupportedChain.SOLANA);
  const [selectedToken, setSelectedToken] = useState('USDC');
  const [amount, setAmount] = useState('');
  const [targetCurrency, setTargetCurrency] = useState('NGN');
  const [bankAccount, setBankAccount] = useState<BankAccount>({
    bankName: '',
    accountNumber: '',
    accountName: ''
  });
  const [quotes, setQuotes] = useState<CrossChainOffRampQuote[]>([]);
  const [selectedQuote, setSelectedQuote] = useState<CrossChainOffRampQuote | null>(null);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    if (user) {
      loadWallets();
      loadQuotes();
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadQuotes();
    }
  }, [selectedChain, selectedToken, amount, targetCurrency]);

  const loadWallets = async () => {
    if (!user) return;

    try {
      const userWallets = await CrossChainWalletService.getUserWallets(user.id);
      setWallets(userWallets);
    } catch (error) {
      console.error('Error loading wallets:', error);
    }
  };

  const loadQuotes = async () => {
    if (!user || !amount || parseFloat(amount) <= 0) {
      setQuotes([]);
      setSelectedQuote(null);
      return;
    }

    try {
      setLoading(true);
      const offRampQuotes = await CrossChainOffRampService.getOffRampQuotes(user.id, targetCurrency);
      
      // Filter quotes for selected chain and token
      const filteredQuotes = offRampQuotes.filter(
        quote => quote.fromChain === selectedChain && quote.fromToken === selectedToken
      );
      
      setQuotes(filteredQuotes);
      if (filteredQuotes.length > 0) {
        setSelectedQuote(filteredQuotes[0]); // Select best quote
      }
    } catch (error) {
      console.error('Error loading quotes:', error);
      toast({
        title: "Error",
        description: "Failed to load off-ramp quotes. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const executeOffRamp = async () => {
    if (!selectedQuote || !user || !bankAccount.bankName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      setProcessing(true);
      
      const result = await CrossChainOffRampService.executeOffRamp({
        userId: user.id,
        fromChain: selectedChain,
        fromToken: selectedToken,
        amount,
        targetCurrency,
        bankAccount,
        bridgeToOptimalChain: selectedQuote.optimalChain !== undefined
      });

      if (result.success) {
        toast({
          title: "Off-Ramp Initiated! 🎉",
          description: `Your ${selectedToken} is being converted to ${targetCurrency}`,
        });
        
        // Reset form
        setAmount('');
        setBankAccount({ bankName: '', accountNumber: '', accountName: '' });
        setQuotes([]);
        setSelectedQuote(null);
      } else {
        throw new Error(result.error || 'Off-ramp execution failed');
      }
    } catch (error) {
      console.error('Error executing off-ramp:', error);
      toast({
        title: "Off-Ramp Failed",
        description: "Failed to execute off-ramp transaction. Please try again.",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const getAvailableTokens = (chain: SupportedChain) => {
    const chainConfig = CHAIN_CONFIGS[chain];
    return chainConfig.supportedTokens.map(token => token.symbol);
  };

  const getWalletBalance = (chain: SupportedChain, token: string) => {
    const wallet = wallets.find(w => w.chain === chain);
    if (!wallet) return '0';
    
    const balance = wallet.balances.find(b => b.tokenSymbol === token);
    return balance ? parseFloat(balance.balance).toFixed(6) : '0';
  };

  const formatCurrency = (amount: number, currency: string = 'NGN') => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getRouteStepIcon = (action: string) => {
    switch (action) {
      case 'bridge':
        return <ArrowDown className="h-4 w-4 text-blue-500" />;
      case 'convert':
        return <RefreshCw className="h-4 w-4 text-green-500" />;
      case 'withdraw':
        return <DollarSign className="h-4 w-4 text-purple-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <ArrowDown className="h-5 w-5 text-green-500" />
          <h2 className="text-lg font-semibold">Cross-Chain Off-Ramp</h2>
        </div>

        {/* Supported Tokens Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 mb-1">Supported Tokens & Deposits</h3>
              <p className="text-sm text-blue-700">
                • <strong>Solana:</strong> SOL and USDC supported<br/>
                • <strong>Other chains:</strong> Only USDC supported (Ethereum, Polygon, BSC, Arbitrum, Avalanche, Base)<br/>
                • <strong>Deposit addresses:</strong> Use the wallet addresses shown in the chain selection below
              </p>
            </div>
          </div>
        </div>

        {/* Source Selection */}
        <div className="space-y-4">
          <div>
            <Label>From Chain & Token</Label>
            <div className="grid grid-cols-2 gap-4 mt-2">
              <Select value={selectedChain} onValueChange={(value) => setSelectedChain(value as SupportedChain)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(SupportedChain).map((chain) => (
                    <SelectItem key={chain} value={chain}>
                      <div className="flex items-center gap-2">
                        <img 
                          src={CHAIN_CONFIGS[chain].logo} 
                          alt={CHAIN_CONFIGS[chain].name}
                          className="w-4 h-4"
                        />
                        {CHAIN_CONFIGS[chain].name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedToken} onValueChange={setSelectedToken}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableTokens(selectedChain).map((token) => (
                    <SelectItem key={token} value={token}>
                      {token}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="mt-2 space-y-1">
              <p className="text-sm text-muted-foreground">
                Balance: {getWalletBalance(selectedChain, selectedToken)} {selectedToken}
              </p>
              {(() => {
                const wallet = wallets.find(w => w.chain === selectedChain);
                if (wallet) {
                  return (
                    <div className="bg-gray-50 rounded p-2">
                      <p className="text-xs text-gray-600 mb-1">
                        Deposit Address {selectedChain === SupportedChain.SOLANA ? '(SOL & USDC)' : '(USDC only)'}:
                      </p>
                      <div className="flex items-center gap-2">
                        <code className="text-xs font-mono bg-white px-2 py-1 rounded border flex-1">
                          {wallet.address}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => {
                            navigator.clipboard.writeText(wallet.address);
                            toast({
                              title: "Address Copied! 📋",
                              description: "Deposit address copied to clipboard",
                            });
                          }}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  );
                }
                return null;
              })()}
            </div>
          </div>

          {/* Amount Input */}
          <div>
            <Label>Amount to Convert</Label>
            <Input
              type="number"
              placeholder="0.0"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="mt-2"
            />
          </div>

          {/* Target Currency */}
          <div>
            <Label>To Currency</Label>
            <Select value={targetCurrency} onValueChange={setTargetCurrency}>
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="NGN">🇳🇬 Nigerian Naira (NGN)</SelectItem>
                <SelectItem value="USD">🇺🇸 US Dollar (USD)</SelectItem>
                <SelectItem value="EUR">🇪🇺 Euro (EUR)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bank Account Details */}
          <div className="space-y-3">
            <Label>Bank Account Details</Label>
            <Input
              placeholder="Bank Name"
              value={bankAccount.bankName}
              onChange={(e) => setBankAccount(prev => ({ ...prev, bankName: e.target.value }))}
            />
            <Input
              placeholder="Account Number"
              value={bankAccount.accountNumber}
              onChange={(e) => setBankAccount(prev => ({ ...prev, accountNumber: e.target.value }))}
            />
            <Input
              placeholder="Account Name"
              value={bankAccount.accountName}
              onChange={(e) => setBankAccount(prev => ({ ...prev, accountName: e.target.value }))}
            />
          </div>
        </div>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-5 w-5 animate-spin mr-2" />
            <span>Finding best rates across all chains...</span>
          </div>
        </Card>
      )}

      {/* Off-Ramp Quotes */}
      {quotes.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Available Routes</h3>
          <div className="space-y-3">
            {quotes.map((quote, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedQuote === quote ? 'border-green-500 bg-green-50' : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedQuote(quote)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <img 
                      src={CHAIN_CONFIGS[quote.fromChain].logo} 
                      alt={CHAIN_CONFIGS[quote.fromChain].name}
                      className="w-8 h-8"
                    />
                    <div>
                      <p className="font-medium">
                        {CHAIN_CONFIGS[quote.fromChain].name}
                        {quote.optimalChain && (
                          <Badge variant="outline" className="ml-2 text-xs">
                            <Zap className="h-3 w-3 mr-1" />
                            Optimal Route
                          </Badge>
                        )}
                      </p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>~{quote.estimatedTime} min</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-lg">
                      {formatCurrency(quote.targetAmount, targetCurrency)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Rate: 1 {selectedToken} = {formatCurrency(quote.exchangeRate, targetCurrency)}
                    </p>
                  </div>
                </div>

                {selectedQuote === quote && quote.route.length > 1 && (
                  <div className="mt-4 pt-4 border-t">
                    <p className="text-sm font-medium mb-3">Transaction Route:</p>
                    <div className="space-y-2">
                      {quote.route.map((step, stepIndex) => (
                        <div key={stepIndex} className="flex items-center gap-3 text-sm">
                          {getRouteStepIcon(step.action)}
                          <span className="capitalize">{step.action}</span>
                          <span className="text-muted-foreground">
                            {step.fromAmount} {step.fromToken} → {step.toAmount} {step.toToken}
                          </span>
                          {step.fromChain && step.toChain && (
                            <Badge variant="outline" className="text-xs">
                              {CHAIN_CONFIGS[step.fromChain].name} → {CHAIN_CONFIGS[step.toChain].name}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {selectedQuote && (
            <div className="mt-6">
              <Separator className="mb-4" />
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <span className="text-muted-foreground">You will receive:</span>
                  <p className="text-lg font-semibold">
                    {formatCurrency(selectedQuote.targetAmount, targetCurrency)}
                  </p>
                </div>
                <div>
                  <span className="text-muted-foreground">Total fees:</span>
                  <p className="text-lg font-semibold">
                    {formatCurrency(selectedQuote.fees.totalFeeUSD, 'USD')}
                  </p>
                </div>
              </div>

              <Button
                onClick={executeOffRamp}
                disabled={processing || !selectedQuote || !bankAccount.bankName}
                className="w-full"
                size="lg"
              >
                {processing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <ArrowDown className="h-4 w-4 mr-2" />
                    Convert to {targetCurrency}
                  </>
                )}
              </Button>
            </div>
          )}
        </Card>
      )}

      {quotes.length === 0 && amount && parseFloat(amount) > 0 && !loading && (
        <Card className="p-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <p className="text-muted-foreground">
              No off-ramp routes available for this token and amount.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};
