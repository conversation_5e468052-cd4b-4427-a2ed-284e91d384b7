/**
 * Service for refreshing wallet data from Helius API
 */
import { supabase } from '@/integrations/supabase/client';
import { CryptoType, TokenBalance } from '@/types/wallet';
import { fetchFormattedWalletData, parseHeliusBalances } from '@/utils/heliusApi';

/**
 * Refresh wallet balances from Helius API
 * @param walletId The wallet ID in our database
 * @param address The Solana wallet address
 * @returns True if refresh was successful
 */
export async function refreshWalletFromHelius(walletId: string, address: string): Promise<boolean> {
  try {
    console.log(`Fetching data from Helius for address: ${address}`);
    // Fetch wallet data from Helius
    const walletData = await fetchFormattedWalletData(address);
    
    if (!walletData || !walletData.balances) {
      console.error('No valid data returned from Helius');
      return false;
    }
    
    console.log('Helius returned wallet data successfully');
    
    // Get current tokens for this wallet
    const { data: tokensData, error: tokensError } = await supabase
      .from('wallet_tokens')
      .select('*')
      .eq('wallet_id', walletId);
      
    if (tokensError) {
      console.error('Error fetching wallet tokens:', tokensError);
      return false;
    }
    
    console.log(`Found ${tokensData?.length} tokens in database for wallet ${walletId}`);
    
    // Keep track of updated tokens for refresh log
    const updatedTokens: { type: string; oldBalance: number; newBalance: number; }[] = [];
    
    // Update each token balance
    for (const token of tokensData) {
      let newBalance = token.balance;
      let oldBalance = Number(token.balance);
      let dollarValue = 0;
      
      // Update based on Helius data
      if (token.type === 'SOL' && walletData.balances.SOL) {
        newBalance = walletData.balances.SOL.balance;
        dollarValue = walletData.balances.SOL.dollarValue;
        console.log(`Updating SOL balance to ${newBalance} (${dollarValue} USD)`);
        
        if (oldBalance !== newBalance) {
          updatedTokens.push({
            type: 'SOL',
            oldBalance,
            newBalance
          });
        }
      } else if (token.type === 'USDC' && walletData.balances.USDC) {
        newBalance = walletData.balances.USDC.balance;
        dollarValue = walletData.balances.USDC.dollarValue;
        console.log(`Updating USDC balance to ${newBalance} (${dollarValue} USD)`);
        
        if (oldBalance !== newBalance) {
          updatedTokens.push({
            type: 'USDC',
            oldBalance,
            newBalance
          });
        }
      }
      
      // Update the token in the database
      const { error: updateError } = await supabase
        .from('wallet_tokens')
        .update({ 
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', token.id);
        
      if (updateError) {
        console.error('Error updating token balance:', updateError);
      } else {
        console.log(`Successfully updated ${token.type} balance to ${newBalance}`);
      }
    }
    
    // Update the wallet's last_refreshed timestamp
    const { error: walletUpdateError } = await supabase
      .from('wallets')
      .update({ 
        updated_at: new Date().toISOString()
      })
      .eq('id', walletId);
      
    if (walletUpdateError) {
      console.error('Error updating wallet refresh timestamp:', walletUpdateError);
    }
    
    // Store refresh history in Supabase
    if (updatedTokens.length > 0) {
      try {
        const { error: refreshLogError } = await supabase
          .from('wallet_refresh_logs')
          .insert({
            wallet_id: walletId,
            address: address,
            changes: updatedTokens,
            refreshed_at: new Date().toISOString()
          });
          
        if (refreshLogError) {
          console.error('Error storing refresh history:', refreshLogError);
        }
      } catch (logError) {
        console.error('Failed to store refresh history:', logError);
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error refreshing wallet from Helius:', error);
    return false;
  }
}

/**
 * Refresh all wallets for a user
 * @param userId The user ID
 * @returns True if refresh was successful
 */
export async function refreshAllUserWallets(userId: string): Promise<boolean> {
  try {
    console.log(`Refreshing all wallets for user: ${userId}`);
    // Get all wallets for the user
    const { data: wallets, error: walletsError } = await supabase
      .from('wallets')
      .select('*')
      .eq('user_id', userId);
      
    if (walletsError) {
      console.error('Error fetching user wallets:', walletsError);
      return false;
    }
    
    if (!wallets || wallets.length === 0) {
      console.log('No wallets found for user');
      return false;
    }
    
    console.log(`Found ${wallets.length} wallets to refresh`);
    
    // Refresh each wallet
    const results = await Promise.all(
      wallets.map(wallet => {
        console.log(`Refreshing wallet: ${wallet.id} (${wallet.address})`);
        return refreshWalletFromHelius(wallet.id, wallet.address);
      })
    );
    
    // Return true if at least one wallet was refreshed successfully
    const success = results.some(result => result === true);
    console.log(`Refresh completed. Overall success: ${success}`);
    
    // Store user refresh event in Supabase
    try {
      const { error: refreshEventError } = await supabase
        .from('user_activity_logs')
        .insert({
          user_id: userId,
          activity_type: 'wallet_refresh',
          details: {
            wallet_count: wallets.length,
            success_count: results.filter(r => r === true).length,
            timestamp: new Date().toISOString()
          }
        });
        
      if (refreshEventError) {
        console.error('Error logging user refresh activity:', refreshEventError);
      }
    } catch (logError) {
      console.error('Failed to log user refresh activity:', logError);
    }
    
    return success;
  } catch (error) {
    console.error('Error refreshing all user wallets:', error);
    return false;
  }
}
