/**
 * Wallet service for managing cryptocurrency wallets
 * This service provides a unified wallet interface that can hold multiple token types
 */
import { simulateApiCall } from './api';
import { Wallet, Transaction, CryptoType, TransactionType, TokenBalance } from '../types/wallet';
import { getExchangeRate, CryptoCurrency } from './cryptoFiatService';
import { supabase } from '@/integrations/supabase/client';
import { Keypair } from '@solana/web3.js';

/**
 * Map our CryptoType to CryptoCurrency for exchange rate lookups
 */
function mapCryptoTypeToCurrency(type: CryptoType): CryptoCurrency {
  switch (type) {
    case CryptoType.SOL:
      return CryptoCurrency.SOL;
    case CryptoType.USDC:
      return CryptoCurrency.USDC;
    case CryptoType.BTC:
      return CryptoCurrency.BTC;
    case CryptoType.ETH:
      return CryptoCurrency.ETH;
    default:
      return CryptoCurrency.USDC;
  }
}

/**
 * Get wallets for the current user - ensures only one wallet is returned
 */
export async function getWallets(): Promise<Wallet[]> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.log("No user found, returning empty wallet list");
      return [];
    }

    // Fetch ALL wallets to check for duplicates
    const { data: walletsData, error: walletsError } = await supabase
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (walletsError) {
      console.error("Error fetching wallets:", walletsError);
      throw walletsError;
    }

    if (!walletsData || walletsData.length === 0) {
      console.log("No wallets found for user, returning empty list");
      return [];
    }

    // CRITICAL FIX: Ensure we only process ONE wallet - the most recent one
    console.log(`Found ${walletsData.length} wallets for user ${user.id}, using only the most recent one`);
    const primaryWallet = walletsData[0];

    // If we have more than one wallet, delete the duplicates in the database
    if (walletsData.length > 1) {
      await cleanupDuplicateWallets(walletsData);
    }

    // Process only the primary wallet
    const wallets: Wallet[] = [];

    // Fetch tokens for this wallet
    const { data: tokensData, error: tokensError } = await supabase
      .from('wallet_tokens')
      .select('*')
      .eq('wallet_id', primaryWallet.id);

    if (tokensError) {
      console.error("Error fetching tokens:", tokensError);
      throw tokensError;
    }

    // Convert tokens data
    const tokens: TokenBalance[] = tokensData.map(token => {
      // Get dollar value based on exchange rate
      const exchangeRate = token.type === 'USDC' ? 1 :
        token.type === 'SOL' ? 60.25 :
        token.type === 'BTC' ? 63000 :
        token.type === 'ETH' ? 3500 : 1;

      const dollarValue = Number(token.balance) * exchangeRate;

      return {
        type: token.type as CryptoType,
        balance: Number(token.balance),
        dollarValue
      };
    });

    // Calculate total dollar value
    const totalDollarValue = tokens.reduce((sum, token) => sum + token.dollarValue, 0);

    wallets.push({
      id: primaryWallet.id,
      name: primaryWallet.name,
      address: primaryWallet.address,
      network: primaryWallet.network as 'solana' | 'ethereum' | 'bitcoin',
      tokens,
      totalDollarValue,
      isDefault: primaryWallet.is_default,
      createdAt: new Date(primaryWallet.created_at),
      updatedAt: primaryWallet.updated_at ? new Date(primaryWallet.updated_at) : undefined
    });

    console.log(`Successfully processed primary wallet: ${primaryWallet.id}`);
    return wallets;
  } catch (error) {
    console.error("Error in getWallets:", error);
    return [];
  }
}

/**
 * Delete all duplicate wallets, keeping only the primary one
 */
async function cleanupDuplicateWallets(walletsData: any[]): Promise<void> {
  if (walletsData.length <= 1) return;

  const { data: { user } } = await supabase.auth.getUser();
  if (!user) return;

  console.log(`Cleaning up ${walletsData.length - 1} duplicate wallets for user ${user.id}`);

  // Keep only the most recent wallet (already sorted by created_at desc)
  const primaryWallet = walletsData[0];
  const duplicateWallets = walletsData.slice(1);

  // Delete duplicate wallets
  for (const wallet of duplicateWallets) {
    console.log(`Deleting duplicate wallet ${wallet.id}`);

    // First delete associated tokens
    const { error: tokensError } = await supabase
      .from('wallet_tokens')
      .delete()
      .eq('wallet_id', wallet.id);

    if (tokensError) {
      console.error(`Failed to delete tokens for wallet ${wallet.id}:`, tokensError);
      continue;
    }

    // Then delete transactions
    const { error: transactionsError } = await supabase
      .from('transactions')
      .delete()
      .eq('wallet_id', wallet.id);

    if (transactionsError) {
      console.error(`Failed to delete transactions for wallet ${wallet.id}:`, transactionsError);
      continue;
    }

    // Finally delete the wallet
    const { error: walletError } = await supabase
      .from('wallets')
      .delete()
      .eq('id', wallet.id);

    if (walletError) {
      console.error(`Failed to delete wallet ${wallet.id}:`, walletError);
    } else {
      console.log(`Successfully deleted duplicate wallet ${wallet.id}`);
    }
  }
}

/**
 * Get a specific wallet by ID
 */
export async function getWallet(id: string): Promise<Wallet | null> {
  const wallets = await getWallets();
  const wallet = wallets.find(w => w.id === id);
  return wallet || null;
}

/**
 * Get token balance for a specific crypto type across all wallets
 */
export async function getTokenBalance(type: CryptoType): Promise<TokenBalance | null> {
  const wallets = await getWallets();

  for (const wallet of wallets) {
    const token = wallet.tokens.find(t => t.type === type);
    if (token) {
      return token;
    }
  }

  return null;
}

/**
 * Deposit funds into a wallet
 */
export async function depositToWallet(
  walletId: string,
  tokenType: CryptoType,
  amount: number,
  source: string = 'external'
): Promise<Transaction> {
  // Get the current dollar value of the deposit
  const cryptoCurrency = mapCryptoTypeToCurrency(tokenType);
  const exchangeRate = await getExchangeRate(cryptoCurrency);
  const dollarValue = tokenType === CryptoType.USDC ? amount : amount * exchangeRate;

  // Generate a realistic Solana transaction hash
  // In a real app, this would be the actual transaction hash from the blockchain
  const keypair = Keypair.generate();
  const transactionHash = keypair.publicKey.toString();

  // Create the transaction in the database
  const { data: transactionData, error: transactionError } = await supabase
    .from('transactions')
    .insert({
      wallet_id: walletId,
      type: TransactionType.DEPOSIT,
      amount,
      token_type: tokenType,
      date: new Date().toISOString(),
      status: 'completed',
      source,
      dollar_value: dollarValue,
      hash: transactionHash // Real Solana public key as transaction hash
    })
    .select()
    .single();

  if (transactionError) throw transactionError;

  // Update token balance
  const { data: token, error: tokenError } = await supabase
    .from('wallet_tokens')
    .select('*')
    .eq('wallet_id', walletId)
    .eq('type', tokenType)
    .single();

  if (tokenError) throw tokenError;

  const newBalance = Number(token.balance) + amount;

  const { error: updateError } = await supabase
    .from('wallet_tokens')
    .update({ balance: newBalance, updated_at: new Date().toISOString() })
    .eq('id', token.id);

  if (updateError) throw updateError;

  const transaction: Transaction = {
    id: transactionData.id,
    type: TransactionType.DEPOSIT,
    amount,
    tokenType,
    walletId,
    date: new Date(transactionData.date),
    status: 'completed',
    source,
    dollarValue,
    hash: transactionData.hash,
  };

  return simulateApiCall(transaction, 1000);
}

/**
 * Fund a card from a wallet
 */
export async function fundCardFromWallet(
  walletId: string,
  tokenType: CryptoType,
  amount: number,
  cardId: string
): Promise<Transaction> {
  // Get the current dollar value of the funding amount
  const cryptoCurrency = mapCryptoTypeToCurrency(tokenType);
  const exchangeRate = await getExchangeRate(cryptoCurrency);
  const dollarValue = tokenType === CryptoType.USDC ? amount : amount * exchangeRate;

  // Calculate a small fee (0.5%)
  const fee = dollarValue * 0.005;

  // Generate a realistic Solana transaction hash
  const keypair = Keypair.generate();
  const transactionHash = keypair.publicKey.toString();

  // Create the transaction in the database
  const { data: transactionData, error: transactionError } = await supabase
    .from('transactions')
    .insert({
      wallet_id: walletId,
      type: TransactionType.FUNDING,
      amount,
      token_type: tokenType,
      date: new Date().toISOString(),
      status: 'completed',
      destination: 'Virtual Card',
      dollar_value: dollarValue,
      fee,
      card_id: cardId,
      hash: transactionHash,
      description: `Funded virtual card with ${amount} ${tokenType}`
    })
    .select()
    .single();

  if (transactionError) throw transactionError;

  // Update token balance
  const { data: token, error: tokenError } = await supabase
    .from('wallet_tokens')
    .select('*')
    .eq('wallet_id', walletId)
    .eq('type', tokenType)
    .single();

  if (tokenError) throw tokenError;

  const newBalance = Number(token.balance) - amount;

  const { error: updateError } = await supabase
    .from('wallet_tokens')
    .update({ balance: newBalance, updated_at: new Date().toISOString() })
    .eq('id', token.id);

  if (updateError) throw updateError;

  const transaction: Transaction = {
    id: transactionData.id,
    type: TransactionType.FUNDING,
    amount,
    tokenType,
    walletId,
    date: new Date(transactionData.date),
    status: 'completed',
    destination: 'Virtual Card',
    dollarValue,
    fee,
    cardId,
    hash: transactionHash,
    description: `Funded virtual card with ${amount} ${tokenType}`,
  };

  return simulateApiCall(transaction, 1500);
}

/**
 * Get transaction history for a wallet
 */
export async function getWalletTransactions(walletId: string): Promise<Transaction[]> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return [];
    }

    // Verify that the wallet belongs to the current user
    const { data: walletData, error: walletError } = await supabase
      .from('wallets')
      .select('id')
      .eq('id', walletId)
      .eq('user_id', user.id)
      .single();

    if (walletError || !walletData) {
      console.error("Wallet not found or doesn't belong to the current user");
      return [];
    }

    // Fetch transactions from the database
    const { data: transactionsData, error: transactionsError } = await supabase
      .from('transactions')
      .select('*')
      .eq('wallet_id', walletId)
      .order('date', { ascending: false });

    if (transactionsError) throw transactionsError;

    // Convert to our Transaction type
    const transactions: Transaction[] = transactionsData.map(tx => ({
      id: tx.id,
      type: tx.type as TransactionType,
      amount: Number(tx.amount),
      walletId: tx.wallet_id,
      tokenType: tx.token_type as CryptoType,
      date: new Date(tx.date),
      status: tx.status as 'pending' | 'completed' | 'failed' | 'refunded',
      source: tx.source,
      destination: tx.destination,
      fee: tx.fee ? Number(tx.fee) : undefined,
      description: tx.description,
      dollarValue: tx.dollar_value ? Number(tx.dollar_value) : undefined,
      hash: tx.hash,
      cardId: tx.card_id
    }));

    return transactions;
  } catch (error) {
    console.error("Error fetching wallet transactions:", error);
    return [];
  }
}

/**
 * Get all transactions for the current user across all wallets
 */
export async function getAllTransactions(): Promise<Transaction[]> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return [];
    }

    // Get the user's wallets
    const { data: walletsData, error: walletsError } = await supabase
      .from('wallets')
      .select('id')
      .eq('user_id', user.id);

    if (walletsError) throw walletsError;

    if (!walletsData || walletsData.length === 0) {
      return [];
    }

    // Get wallet IDs
    const walletIds = walletsData.map(wallet => wallet.id);

    // Fetch transactions for the user's wallets
    const { data: transactionsData, error: transactionsError } = await supabase
      .from('transactions')
      .select('*')
      .in('wallet_id', walletIds)
      .order('date', { ascending: false });

    if (transactionsError) throw transactionsError;

    // Convert to our Transaction type
    const transactions: Transaction[] = transactionsData.map(tx => ({
      id: tx.id,
      type: tx.type as TransactionType,
      amount: Number(tx.amount),
      walletId: tx.wallet_id,
      tokenType: tx.token_type as CryptoType,
      date: new Date(tx.date),
      status: tx.status as 'pending' | 'completed' | 'failed' | 'refunded',
      source: tx.source,
      destination: tx.destination,
      fee: tx.fee ? Number(tx.fee) : undefined,
      description: tx.description,
      dollarValue: tx.dollar_value ? Number(tx.dollar_value) : undefined,
      hash: tx.hash,
      cardId: tx.card_id
    }));

    return transactions;
  } catch (error) {
    console.error("Error fetching all transactions:", error);
    return [];
  }
}
