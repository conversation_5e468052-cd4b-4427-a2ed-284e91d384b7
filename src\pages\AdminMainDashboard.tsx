/**
 * Main Admin Dashboard
 * 
 * Comprehensive admin panel for SolPay management
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { supabase } from '@/lib/supabase';
import RateHealthMonitor from '@/components/RateHealthMonitor';
import {
  Users,
  CreditCard,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Settings,
  Shield,
  LogOut,
  Activity,
  Banknote,
  UserCheck,
  AlertTriangle,
  BarChart3,
  FileText,
  Zap,
  ArrowRight,
  RefreshCw,
  Store
} from 'lucide-react';

interface DashboardStats {
  totalUsers: number;
  pendingTransfers: number;
  totalTransferAmount: number;
  waitlistCount: number;
  nibssStatus: string;
  recentActivity: any[];
  totalMerchants: number;
  pendingMerchants: number;
  verifiedMerchants: number;
}

export default function AdminMainDashboard() {
  const navigate = useNavigate();
  const { adminUser, logout, hasPermission, refreshPermissions } = useAdminAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    pendingTransfers: 0,
    totalTransferAmount: 0,
    waitlistCount: 0,
    nibssStatus: 'operational',
    recentActivity: [],
    totalMerchants: 0,
    pendingMerchants: 0,
    verifiedMerchants: 0
  });
  const [loading, setLoading] = useState(true);

  console.log('🎯 AdminMainDashboard render:', { adminUser: !!adminUser, loading });

  useEffect(() => {
    console.log('🔄 AdminMainDashboard useEffect:', { adminUser: !!adminUser });
    if (!adminUser) {
      console.log('❌ No admin user, redirecting to login');
      navigate('/admin/login');
      return;
    }
    console.log('✅ Admin user found, loading stats');
    loadDashboardStats();
  }, [adminUser, navigate]);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading dashboard stats...');

      // Load various stats in parallel
      const [
        usersResult,
        transfersResult,
        waitlistResult,
        activityResult,
        merchantsResult,
        pendingMerchantsResult,
        verifiedMerchantsResult
      ] = await Promise.allSettled([
        supabase.from('profiles').select('id', { count: 'exact' }),
        supabase.from('manual_transfer_queue').select('*').eq('status', 'pending'),
        supabase.from('card_waitlist').select('id', { count: 'exact' }),
        supabase.from('manual_transfer_queue').select('*').order('created_at', { ascending: false }).limit(5),
        supabase.from('merchant_accounts').select('id', { count: 'exact' }),
        supabase.from('merchant_accounts').select('id', { count: 'exact' }).eq('is_verified', false),
        supabase.from('merchant_accounts').select('id', { count: 'exact' }).eq('is_verified', true)
      ]);

      console.log('📊 Query results:', {
        usersResult: usersResult.status === 'fulfilled' ? usersResult.value : usersResult.reason,
        transfersResult: transfersResult.status === 'fulfilled' ? transfersResult.value : transfersResult.reason,
        waitlistResult: waitlistResult.status === 'fulfilled' ? waitlistResult.value : waitlistResult.reason,
        activityResult: activityResult.status === 'fulfilled' ? activityResult.value : activityResult.reason
      });

      // Process results
      const totalUsers = usersResult.status === 'fulfilled' ? usersResult.value.count || 0 : 0;
      const pendingTransfers = transfersResult.status === 'fulfilled' ? transfersResult.value.data?.length || 0 : 0;
      const totalTransferAmount = transfersResult.status === 'fulfilled'
        ? transfersResult.value.data?.reduce((sum, t) => sum + t.ngn_amount, 0) || 0
        : 0;
      const waitlistCount = waitlistResult.status === 'fulfilled' ? waitlistResult.value.count || 0 : 0;
      const recentActivity = activityResult.status === 'fulfilled' ? activityResult.value.data || [] : [];

      // Process merchant stats
      const totalMerchants = merchantsResult.status === 'fulfilled' ? merchantsResult.value.count || 0 : 0;
      const pendingMerchants = pendingMerchantsResult.status === 'fulfilled' ? pendingMerchantsResult.value.count || 0 : 0;
      const verifiedMerchants = verifiedMerchantsResult.status === 'fulfilled' ? verifiedMerchantsResult.value.count || 0 : 0;

      const newStats = {
        totalUsers,
        pendingTransfers,
        totalTransferAmount,
        waitlistCount,
        nibssStatus: 'operational',
        recentActivity,
        totalMerchants,
        pendingMerchants,
        verifiedMerchants
      };

      console.log('✅ Dashboard stats loaded:', newStats);
      setStats(newStats);
    } catch (error) {
      console.error('❌ Error loading dashboard stats:', error);
    } finally {
      console.log('🏁 Dashboard loading complete');
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    navigate('/admin/login');
  };

  const quickActions = [
    {
      title: 'Manual Transfers',
      description: 'Process pending crypto off-ramp requests',
      icon: Banknote,
      count: stats.pendingTransfers,
      path: '/admin/transfers',
      permission: 'process_manual_transfers',
      urgent: stats.pendingTransfers > 0
    },
    {
      title: 'NIBSS Status',
      description: 'Manage payment system status',
      icon: Activity,
      path: '/admin/nibss-status',
      permission: 'manage_nibss_status'
    },
    {
      title: 'User Management',
      description: 'View and manage user accounts',
      icon: Users,
      count: stats.totalUsers,
      path: '/admin/users',
      permission: 'manage_users'
    },
    {
      title: 'Waitlist',
      description: 'Manage card application waitlist',
      icon: UserCheck,
      count: stats.waitlistCount,
      path: '/admin/waitlist',
      permission: 'view_waitlist'
    },
    {
      title: 'Analytics',
      description: 'View platform analytics and reports',
      icon: BarChart3,
      path: '/admin/analytics',
      permission: 'view_analytics'
    },
    {
      title: 'Settings',
      description: 'Configure platform settings',
      icon: Settings,
      path: '/admin/settings',
      permission: 'manage_settings'
    },
    {
      title: 'Admin Users',
      description: 'Create and manage admin accounts',
      icon: Shield,
      path: '/admin/admin-users',
      permission: 'manage_admins'
    },
    {
      title: 'Merchant Management',
      description: 'Verify and manage merchant accounts',
      icon: Store,
      count: stats.pendingMerchants,
      path: '/admin/merchants',
      permission: 'manage_merchants',
      urgent: stats.pendingMerchants > 0
    }
  ];

  if (loading) {
    console.log('⏳ Dashboard is loading...');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  console.log('🎨 Rendering dashboard content');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Shield className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">SolPay Admin</h1>
                <p className="text-sm text-gray-500">Welcome back, {adminUser?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline" className="text-green-600 border-green-600">
                {adminUser?.role.replace('_', ' ').toUpperCase()}
              </Badge>
              <Button variant="outline" size="sm" onClick={refreshPermissions}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh Permissions
              </Button>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Pending Transfers</p>
                  <p className="text-2xl font-bold">{stats.pendingTransfers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Transfer Amount</p>
                  <p className="text-2xl font-bold">₦{stats.totalTransferAmount.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Waitlist</p>
                  <p className="text-2xl font-bold">{stats.waitlistCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Store className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Total Merchants</p>
                  <p className="text-2xl font-bold">{stats.totalMerchants}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Pending Verification</p>
                  <p className="text-2xl font-bold">{stats.pendingMerchants}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">Verified Merchants</p>
                  <p className="text-2xl font-bold">{stats.verifiedMerchants}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Urgent Actions */}
        {stats.pendingTransfers > 0 && (
          <Alert className="mb-6 border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <div className="flex items-center justify-between">
                <span>
                  <strong>{stats.pendingTransfers} pending transfers</strong> require manual processing (₦{stats.totalTransferAmount.toLocaleString()})
                </span>
                <Button 
                  size="sm" 
                  onClick={() => navigate('/admin/transfers')}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  Process Now
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Debug: Current Permissions */}
        <Card className="mb-6 border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-blue-900">Debug: Current Admin Permissions</h3>
                <p className="text-sm text-blue-700">
                  Role: {adminUser?.role} | Permissions: {adminUser?.permissions.join(', ')}
                </p>
              </div>
              <Button variant="outline" size="sm" onClick={refreshPermissions}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh Permissions
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {quickActions.map((action) => {
            if (!hasPermission(action.permission)) return null;
            
            return (
              <Card 
                key={action.title} 
                className={`cursor-pointer transition-all hover:shadow-md ${action.urgent ? 'border-orange-300 bg-orange-50' : ''}`}
                onClick={() => navigate(action.path)}
              >
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <action.icon className={`h-8 w-8 ${action.urgent ? 'text-orange-500' : 'text-blue-500'}`} />
                      <div className="ml-4">
                        <h3 className="font-medium">{action.title}</h3>
                        <p className="text-sm text-muted-foreground">{action.description}</p>
                        {action.count !== undefined && (
                          <p className="text-lg font-bold mt-1">{action.count}</p>
                        )}
                      </div>
                    </div>
                    <ArrowRight className="h-5 w-5 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transfer Requests</CardTitle>
            <CardDescription>Latest crypto off-ramp requests</CardDescription>
          </CardHeader>
          <CardContent>
            {stats.recentActivity.length === 0 ? (
              <p className="text-center text-muted-foreground py-4">No recent activity</p>
            ) : (
              <div className="space-y-3">
                {stats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center">
                      <Banknote className="w-4 h-4 text-green-500 mr-3" />
                      <div>
                        <p className="font-medium">
                          {activity.crypto_amount} {activity.crypto_symbol} → ₦{activity.ngn_amount.toLocaleString()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {activity.bank_name} • {new Date(activity.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <Badge variant={activity.status === 'pending' ? 'secondary' : 'default'}>
                      {activity.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Exchange Rate Monitor */}
        <Card>
          <CardHeader>
            <CardTitle>Exchange Rate Health</CardTitle>
            <CardDescription>Monitor the status of exchange rate APIs</CardDescription>
          </CardHeader>
          <CardContent>
            <RateHealthMonitor />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
