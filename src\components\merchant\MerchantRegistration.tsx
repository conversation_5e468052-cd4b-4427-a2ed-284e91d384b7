/**
 * Merchant Registration Component
 * Allows users to register as merchants and generate QR codes
 */

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { MerchantQRService, MerchantAccount } from '@/services/merchantQRService';
import { paystackService } from '@/services/paystackService';
import { 
  Store, 
  QrCode, 
  Download, 
  Copy, 
  CheckCircle,
  AlertTriangle,
  Banknote,
  Settings
} from 'lucide-react';

interface MerchantRegistrationProps {
  onRegistrationComplete?: (merchant: MerchantAccount) => void;
}

const MerchantRegistration: React.FC<MerchantRegistrationProps> = ({
  onRegistrationComplete
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'form' | 'success'>('form');
  const [registeredMerchant, setRegisteredMerchant] = useState<MerchantAccount | null>(null);
  const [copiedQR, setCopiedQR] = useState(false);
  
  const [formData, setFormData] = useState({
    businessName: '',
    businessType: '',
    businessDescription: '',
    businessAddress: '',
    contactPhone: '',
    contactEmail: '',
    bankName: '',
    bankCode: '',
    accountNumber: '',
    accountName: '',
    acceptsSol: true,
    acceptsUsdc: true,
    minPaymentAmount: 100,
    maxPaymentAmount: 500000,
    collectionPreference: 'naira', // 'naira' or 'crypto'
    cryptoWalletAddress: '' // For direct crypto collection
  });

  const [banks, setBanks] = useState([]);
  const [isVerifyingBank, setIsVerifyingBank] = useState(false);
  const [bankVerified, setBankVerified] = useState(false);

  const businessTypes = [
    'Restaurant/Food',
    'Retail Store',
    'Online Business',
    'Service Provider',
    'Tech/Software',
    'Beauty/Salon',
    'Automotive',
    'Education',
    'Healthcare',
    'Entertainment',
    'Other'
  ];

  // Load banks on component mount
  useEffect(() => {
    loadBanks();
  }, []);

  const loadBanks = async () => {
    try {
      console.log('Loading banks from Paystack service...');
      const banksData = await paystackService.getNigerianBanks();
      setBanks(banksData);
      console.log(`✅ Loaded ${banksData.length} banks from Paystack`);
    } catch (error) {
      console.error('Error loading banks from Paystack:', error);
      // Since we now use a static list in the service, this shouldn't happen
      // But if it does, set an empty array and show an error
      setBanks([]);
      toast({
        title: "Error Loading Banks",
        description: "Unable to load bank list. Please refresh the page.",
        variant: "destructive",
      });
    }
  };

  const verifyBankAccount = async () => {
    if (!formData.accountNumber || !formData.bankCode) {
      toast({
        title: "Missing Information",
        description: "Please select a bank and enter account number",
        variant: "destructive",
      });
      return;
    }

    // Validate account number format
    if (formData.accountNumber.length < 10) {
      toast({
        title: "Invalid Account Number",
        description: "Account number must be at least 10 digits",
        variant: "destructive",
      });
      return;
    }

    setIsVerifyingBank(true);

    try {
      console.log(`🔍 Verifying account ${formData.accountNumber} with bank ${formData.bankCode}`);

      const verification = await paystackService.verifyBankAccount(
        formData.accountNumber,
        formData.bankCode
      );

      if (verification.status === 'success' && verification.data) {
        setFormData(prev => ({
          ...prev,
          accountName: verification.data!.account_name
        }));
        setBankVerified(true);

        toast({
          title: "Bank Account Verified! ✅",
          description: `Account holder: ${verification.data.account_name}`,
        });
      } else {
        toast({
          title: "Verification Failed",
          description: verification.message || "Could not verify bank account",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Bank verification error:', error);
      toast({
        title: "Verification Error",
        description: "Failed to verify bank account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsVerifyingBank(false);
    }
  };

  // Banks are now loaded dynamically from Paystack

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to register as a merchant",
        variant: "destructive",
      });
      return;
    }

    // Validation
    if (!formData.businessName || !formData.bankName || !formData.accountNumber || !formData.accountName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (!formData.acceptsSol && !formData.acceptsUsdc) {
      toast({
        title: "Payment Methods Required",
        description: "Please accept at least one payment method (SOL or USDC)",
        variant: "destructive",
      });
      return;
    }

    if (formData.collectionPreference === 'crypto' && !formData.cryptoWalletAddress.trim()) {
      toast({
        title: "Wallet Address Required",
        description: "Please provide your crypto wallet address for direct crypto collection",
        variant: "destructive",
      });
      return;
    }

    // Validate Solana wallet address format (basic validation)
    if (formData.collectionPreference === 'crypto' && formData.cryptoWalletAddress.trim()) {
      const walletAddress = formData.cryptoWalletAddress.trim();
      if (walletAddress.length < 32 || walletAddress.length > 44) {
        toast({
          title: "Invalid Wallet Address",
          description: "Please provide a valid Solana wallet address",
          variant: "destructive",
        });
        return;
      }
    }

    setLoading(true);

    try {
      const result = await MerchantQRService.registerMerchant({
        userId: user.id,
        ...formData
      });

      if (result.success && result.merchant) {
        setRegisteredMerchant(result.merchant);
        setStep('success');
        
        toast({
          title: "Merchant Registration Successful! 🎉",
          description: "Your QR code has been generated and is ready to use",
        });

        onRegistrationComplete?.(result.merchant);
      } else {
        toast({
          title: "Registration Failed",
          description: result.error || "Failed to register merchant account",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast({
        title: "Registration Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const downloadQRCode = () => {
    if (!registeredMerchant?.qrCodeUrl) return;

    const link = document.createElement('a');
    link.href = registeredMerchant.qrCodeUrl;
    link.download = `${(registeredMerchant.businessName || 'Merchant').replace(/\s+/g, '_')}_QR_Code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "QR Code Downloaded! 📥",
      description: "Print and display this QR code in your business",
    });
  };

  const copyQRCode = async () => {
    if (!registeredMerchant?.qrCodeUrl) return;

    try {
      // Convert data URL to blob
      const response = await fetch(registeredMerchant.qrCodeUrl);
      const blob = await response.blob();
      
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);

      setCopiedQR(true);
      setTimeout(() => setCopiedQR(false), 2000);

      toast({
        title: "QR Code Copied! 📋",
        description: "QR code image copied to clipboard",
      });
    } catch (error) {
      console.error('Copy failed:', error);
      toast({
        title: "Copy Failed",
        description: "Could not copy QR code. Try downloading instead.",
        variant: "destructive",
      });
    }
  };

  if (step === 'success' && registeredMerchant) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto p-4 space-y-6">
        {/* Success Header */}
        <Card className="p-6 bg-green-50 border-green-200">
          <div className="flex items-center gap-3 mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div>
              <h2 className="text-xl font-bold text-green-900">Merchant Registration Complete!</h2>
              <p className="text-green-700">Your business is now ready to accept crypto payments</p>
            </div>
          </div>
        </Card>

        {/* Business Info */}
        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Store className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold">Business Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">Business Name:</span>
              <p className="font-semibold">{registeredMerchant.businessName || 'N/A'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Business Type:</span>
              <p>{registeredMerchant.businessType || 'Not specified'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Merchant ID:</span>
              <p className="font-mono text-xs">{registeredMerchant.qrCodeId || 'N/A'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-600">Bank Account:</span>
              <p>{registeredMerchant.bankName || 'N/A'} - {registeredMerchant.accountNumber || 'N/A'}</p>
            </div>
          </div>
        </Card>

        {/* QR Code Display */}
        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <QrCode className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-semibold">Your Payment QR Code</h3>
          </div>

          <div className="text-center space-y-4">
            {registeredMerchant.qrCodeUrl && (
              <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                <img 
                  src={registeredMerchant.qrCodeUrl} 
                  alt="Merchant QR Code"
                  className="w-64 h-64 mx-auto"
                />
              </div>
            )}

            <div className="flex justify-center gap-3">
              <Button onClick={downloadQRCode} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Download QR Code
              </Button>
              
              <Button 
                variant="outline" 
                onClick={copyQRCode}
                className="flex items-center gap-2"
              >
                {copiedQR ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
                {copiedQR ? 'Copied!' : 'Copy QR Code'}
              </Button>
            </div>
          </div>
        </Card>

        {/* Payment Settings */}
        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Settings className="h-6 w-6 text-gray-600" />
            <h3 className="text-lg font-semibold">Payment Settings</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">Accepted Tokens:</span>
              <div className="flex gap-2 mt-1">
                {(registeredMerchant.acceptsSol ?? true) && (
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">SOL</span>
                )}
                {(registeredMerchant.acceptsUsdc ?? true) && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">USDC</span>
                )}
              </div>
            </div>
            <div>
              <span className="font-medium text-gray-600">Payment Limits:</span>
              <p>₦{(registeredMerchant.minPaymentAmount || 0).toLocaleString()} - ₦{(registeredMerchant.maxPaymentAmount || 0).toLocaleString()}</p>
            </div>
          </div>
        </Card>

        {/* Instructions */}
        <Card className="p-6 bg-blue-50 border-blue-200">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 mb-2">How to Use Your QR Code</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• <strong>Print the QR code</strong> and display it prominently in your business</li>
                <li>• <strong>Customers scan</strong> the code with the SolPay app</li>
                <li>• <strong>They pay with crypto</strong> (SOL or USDC)</li>
                <li>• <strong>You receive Naira</strong> instantly in your bank account</li>
                <li>• <strong>Transaction fee:</strong> 1.5% (much lower than traditional POS)</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-center gap-3">
          <Button 
            variant="outline" 
            onClick={() => setStep('form')}
          >
            Register Another Business
          </Button>
          
          <Button onClick={() => window.location.href = '/merchant-dashboard'}>
            Go to Merchant Dashboard
          </Button>
        </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-2xl mx-auto p-4">
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Store className="h-8 w-8 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold">Register as Merchant</h2>
            <p className="text-gray-600">Accept crypto payments and receive Naira instantly</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Business Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Store className="h-5 w-5" />
              Business Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="businessName">Business Name *</Label>
                <Input
                  id="businessName"
                  value={formData.businessName}
                  onChange={(e) => handleInputChange('businessName', e.target.value)}
                  placeholder="e.g., Mama Cass Restaurant"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="businessType">Business Type</Label>
                <Select onValueChange={(value) => handleInputChange('businessType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    {businessTypes.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="businessDescription">Business Description</Label>
              <Textarea
                id="businessDescription"
                value={formData.businessDescription}
                onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                placeholder="Brief description of your business"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="businessAddress">Business Address</Label>
              <Input
                id="businessAddress"
                value={formData.businessAddress}
                onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                placeholder="Your business location"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contactPhone">Contact Phone</Label>
                <Input
                  id="contactPhone"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  placeholder="e.g., +234 ************"
                />
              </div>
              
              <div>
                <Label htmlFor="contactEmail">Contact Email</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={formData.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>

          {/* Bank Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Banknote className="h-5 w-5" />
              Bank Account Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="bankName">Bank Name *</Label>
                <Select
                  onValueChange={(value) => {
                    const selectedBank = banks.find(bank => bank.code === value);
                    if (selectedBank) {
                      handleInputChange('bankName', selectedBank.name);
                      handleInputChange('bankCode', selectedBank.code);
                      setBankVerified(false); // Reset verification when bank changes
                    }
                  }}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder={banks.length > 0 ? "Select your bank" : "Loading banks..."} />
                  </SelectTrigger>
                  <SelectContent>
                    {banks.map((bank, index) => (
                      <SelectItem key={`${bank.code}-${index}`} value={bank.code}>
                        {bank.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="accountNumber">Account Number *</Label>
                <div className="flex gap-2">
                  <Input
                    id="accountNumber"
                    value={formData.accountNumber}
                    onChange={(e) => {
                      handleInputChange('accountNumber', e.target.value);
                      setBankVerified(false); // Reset verification when account number changes
                    }}
                    placeholder="**********"
                    required
                  />
                  <Button
                    type="button"
                    onClick={verifyBankAccount}
                    disabled={!formData.accountNumber || !formData.bankCode || isVerifyingBank}
                    className="whitespace-nowrap"
                  >
                    {isVerifyingBank ? 'Verifying...' : 'Verify'}
                  </Button>
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="accountName">Account Name *</Label>
              <Input
                id="accountName"
                value={formData.accountName}
                onChange={(e) => handleInputChange('accountName', e.target.value)}
                placeholder={bankVerified ? "Account name will appear after verification" : "Account holder name"}
                readOnly={bankVerified}
                className={bankVerified ? "bg-green-50 border-green-200" : ""}
                required
              />
              {bankVerified && (
                <div className="flex items-center gap-2 mt-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Bank account verified successfully</span>
                </div>
              )}
            </div>
          </div>

          {/* Payment Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Payment Settings
            </h3>
            
            <div className="space-y-4">
              <div>
                <Label>How do you want to receive payments?</Label>
                <Select
                  value={formData.collectionPreference}
                  onValueChange={(value) => handleInputChange('collectionPreference', value)}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Select collection method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="naira">Convert to Naira (Bank Transfer)</SelectItem>
                    <SelectItem value="crypto">Collect Crypto Directly</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  {formData.collectionPreference === 'naira'
                    ? 'Crypto payments will be converted to Naira and sent to your bank account'
                    : 'Crypto payments will be sent directly to your wallet address'
                  }
                </p>
              </div>

              {formData.collectionPreference === 'crypto' && (
                <div>
                  <Label htmlFor="cryptoWalletAddress">Your Crypto Wallet Address</Label>
                  <Input
                    id="cryptoWalletAddress"
                    type="text"
                    value={formData.cryptoWalletAddress}
                    onChange={(e) => handleInputChange('cryptoWalletAddress', e.target.value)}
                    placeholder="Enter your Solana wallet address (supports SOL and USDC)"
                    className="mt-2"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    This should be your Solana wallet address that can receive both SOL and USDC tokens
                  </p>
                </div>
              )}

              <div>
                <Label>Accepted Cryptocurrencies</Label>
                <div className="flex items-center space-x-6 mt-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="acceptsSol"
                      checked={formData.acceptsSol}
                      onCheckedChange={(checked) => handleInputChange('acceptsSol', checked)}
                    />
                    <Label htmlFor="acceptsSol">Accept SOL</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="acceptsUsdc"
                      checked={formData.acceptsUsdc}
                      onCheckedChange={(checked) => handleInputChange('acceptsUsdc', checked)}
                    />
                    <Label htmlFor="acceptsUsdc">Accept USDC</Label>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="minPaymentAmount">Minimum Payment (₦)</Label>
                <Input
                  id="minPaymentAmount"
                  type="number"
                  value={formData.minPaymentAmount}
                  onChange={(e) => handleInputChange('minPaymentAmount', parseFloat(e.target.value))}
                  min="1"
                />
              </div>
              
              <div>
                <Label htmlFor="maxPaymentAmount">Maximum Payment (₦)</Label>
                <Input
                  id="maxPaymentAmount"
                  type="number"
                  value={formData.maxPaymentAmount}
                  onChange={(e) => handleInputChange('maxPaymentAmount', parseFloat(e.target.value))}
                  min="1"
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            className="w-full" 
            disabled={loading}
          >
            {loading ? 'Registering...' : 'Register as Merchant'}
          </Button>
        </form>
      </Card>
      </div>
    </Layout>
  );
};

export default MerchantRegistration;
