
import Layout from "@/components/Layout";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, RefreshCw, CreditCard, Store, QrCode, ArrowRight } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";

interface SupportedService {
  id: string;
  name: string;
  logo: string;
  category: string;
  compatibilityStatus: 'full' | 'manual' | 'coming-soon';
  description?: string;
}

const Services = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const isMobile = useIsMobile();

  // Supported services that can be used with the virtual card
  const [supportedServices, setSupportedServices] = useState<SupportedService[]>([]);

  useEffect(() => {
    // Simulate loading the supported services
    const loadServices = async () => {
      setIsLoading(true);
      // In a production app, this would fetch from an API
      await new Promise(resolve => setTimeout(resolve, 1000));

      const services: SupportedService[] = [
        {
          id: "twitter",
          name: "Twitter",
          logo: "https://www.freepnglogos.com/uploads/twitter-logo-png/twitter-logo-vector-png-clipart-1.png",
          category: "social",
          compatibilityStatus: 'full',
          description: "Pay for Twitter Blue subscription or promote tweets"
        },
        {
          id: "amazon",
          name: "Amazon",
          logo: "https://www.freepnglogos.com/uploads/amazon-png-logo-vector/woodland-gardening-amazon-png-logo-vector-8.png",
          category: "shopping",
          compatibilityStatus: 'full',
          description: "Shop on Amazon with your virtual card"
        },
        {
          id: "netflix",
          name: "Netflix",
          logo: "https://www.freepnglogos.com/uploads/netflix-logo-circle-png-5.png",
          category: "streaming",
          compatibilityStatus: 'full',
          description: "Pay for Netflix streaming subscriptions"
        },
        {
          id: "spotify",
          name: "Spotify",
          logo: "https://www.freepnglogos.com/uploads/spotify-logo-png/file-spotify-logo-png-4.png",
          category: "music",
          compatibilityStatus: 'full',
          description: "Pay for Spotify Premium and other audio services"
        },
        {
          id: "aliexpress",
          name: "AliExpress",
          logo: "https://www.freepnglogos.com/uploads/aliexpress-logo-png/aliexpress-logo-vector-21.png",
          category: "shopping",
          compatibilityStatus: 'full',
          description: "Shop on AliExpress securely with your virtual card"
        },
        {
          id: "youtube",
          name: "YouTube",
          logo: "https://www.freepnglogos.com/uploads/youtube-play-red-logo-png-transparent-background-6.png",
          category: "streaming",
          compatibilityStatus: 'full',
          description: "Pay for YouTube Premium or Super Chat"
        },
        {
          id: "uber",
          name: "Uber",
          logo: "https://www.freepnglogos.com/uploads/uber-logo-png-1.png",
          category: "transport",
          compatibilityStatus: 'full',
          description: "Pay for Uber rides or Uber Eats"
        },
        {
          id: "airbnb",
          name: "Airbnb",
          logo: "https://www.freepnglogos.com/uploads/airbnb-logo-png/airbnb-logo-icon-symbol-17.png",
          category: "travel",
          compatibilityStatus: 'full',
          description: "Book accommodations through Airbnb"
        },
        {
          id: "instagram",
          name: "Instagram",
          logo: "https://www.freepnglogos.com/uploads/instagram-logos-png-images-free-download-2.png",
          category: "social",
          compatibilityStatus: 'manual',
          description: "Pay for Instagram promotions and ads"
        },
        {
          id: "facebook",
          name: "Facebook",
          logo: "https://www.freepnglogos.com/uploads/facebook-logo-png/facebook-logo-vector-download-11.png",
          category: "social",
          compatibilityStatus: 'manual',
          description: "Pay for Facebook ads and services"
        },
        {
          id: "steam",
          name: "Steam",
          logo: "https://www.freepnglogos.com/uploads/steam-logo-png/steam-icon-logo-15.png",
          category: "gaming",
          compatibilityStatus: 'full',
          description: "Buy games and add funds to your Steam wallet"
        },
        {
          id: "apple",
          name: "Apple",
          logo: "https://www.freepnglogos.com/uploads/apple-logo-png/apple-logo-png-transparent-svg-vector-bie-supply-29.png",
          category: "tech",
          compatibilityStatus: 'full',
          description: "Pay for Apple services and app store purchases"
        }
      ];

      setSupportedServices(services);
      setIsLoading(false);
    };

    loadServices();
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsRefreshing(false);
    toast({
      title: "Services Refreshed",
      description: "The supported services list has been updated.",
    });
  };

  const categories = [
    { id: "all", name: "All" },
    { id: "streaming", name: "Streaming" },
    { id: "shopping", name: "Shopping" },
    { id: "social", name: "Social" },
    { id: "tech", name: "Tech" },
    { id: "travel", name: "Travel" },
    { id: "gaming", name: "Gaming" },
    { id: "transport", name: "Transport" },
  ];

  const filteredServices = supportedServices.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || service.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const renderCompatibilityBadge = (status: 'full' | 'manual' | 'coming-soon') => {
    switch (status) {
      case 'full':
        return <Badge className="bg-green-500 hover:bg-green-600">Full Compatibility</Badge>;
      case 'manual':
        return <Badge className="bg-yellow-500 hover:bg-yellow-600">Manual Setup Required</Badge>;
      case 'coming-soon':
        return <Badge className="bg-gray-500 hover:bg-gray-600">Coming Soon</Badge>;
      default:
        return null;
    }
  };

  // Group by category for tab view
  const streamingServices = supportedServices.filter(service => service.category === "streaming");
  const shoppingServices = supportedServices.filter(service => service.category === "shopping");
  const socialServices = supportedServices.filter(service => service.category === "social");
  const otherServices = supportedServices.filter(service => !["streaming", "shopping", "social"].includes(service.category));

  return (
    <Layout>
      <div className="container mx-auto px-4 py-4 max-w-6xl">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
          <h1 className="text-xl sm:text-2xl font-bold">Supported Services</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">Refresh</span>
            <span className="sm:hidden">Refresh Services</span>
          </Button>
        </div>

        <div className="text-sm text-muted-foreground mb-4">
          Explore the various services and platforms where you can use your solpay virtual card for payments.
        </div>

        {/* Merchant Services Section */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200 mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Store className="h-6 w-6 text-green-600" />
              Merchant Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="bg-white">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <QrCode className="h-8 w-8 text-blue-600" />
                    <div>
                      <h3 className="font-semibold">QR Payment System</h3>
                      <p className="text-sm text-gray-600">Accept crypto payments in your business</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => window.location.href = '/merchant-dashboard'}
                      className="flex items-center gap-2"
                    >
                      <Store className="h-4 w-4" />
                      View Dashboard
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.location.href = '/merchant-register'}
                      className="flex items-center gap-2"
                    >
                      <ArrowRight className="h-4 w-4" />
                      Register
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <CreditCard className="h-8 w-8 text-purple-600" />
                    <div>
                      <h3 className="font-semibold">Payment Processing</h3>
                      <p className="text-sm text-gray-600">Instant crypto to Naira conversion</p>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p>• Accept SOL and USDC payments</p>
                    <p>• Automatic conversion to Naira</p>
                    <p>• Direct bank transfers</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="browse" className="w-full">
          <TabsList className="mb-4 w-full grid grid-cols-2">
            <TabsTrigger value="browse">Browse All</TabsTrigger>
            <TabsTrigger value="categories">By Category</TabsTrigger>
          </TabsList>

          <TabsContent value="browse" className="space-y-4">
            <div className="flex flex-col gap-3">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search services..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>

              {/* Category Filter - scrollable on mobile */}
              <div className="flex gap-2 overflow-x-auto pb-2 -mx-1 px-1">
                {categories.map(category => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className="whitespace-nowrap"
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2 mb-4">
              <div className="flex flex-wrap gap-2 text-xs">
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span>Fully Compatible</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span>Manual Setup Required</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                  <span>Coming Soon</span>
                </div>
              </div>
            </div>

            {isLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
                {Array(8).fill(0).map((_, index) => (
                  <Card key={index}>
                    <CardContent className="pt-6 pb-6">
                      <div className="flex flex-col items-center space-y-3">
                        <Skeleton className="h-16 w-16 rounded-md" />
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-4 w-16" />
                        <Skeleton className="h-8 w-full mt-2" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
                {filteredServices.length > 0 ? (
                  filteredServices.map((service) => (
                    <Card key={service.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <div className="flex justify-center">
                          <div className="h-16 w-16 flex items-center justify-center">
                            <img src={service.logo} alt={service.name} className="max-h-full max-w-full object-contain" />
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="text-center pb-2">
                        <CardTitle className="text-lg mb-1">{service.name}</CardTitle>
                        <p className="text-sm text-muted-foreground mb-2">{service.description}</p>
                        {renderCompatibilityBadge(service.compatibilityStatus)}
                      </CardContent>
                      <CardFooter className="pt-0 pb-4 justify-center">
                        <Button variant="outline" className="w-full flex gap-2">
                          <CreditCard className="h-4 w-4" />
                          Use with Virtual Card
                        </Button>
                      </CardFooter>
                    </Card>
                  ))
                ) : (
                  <div className="col-span-full py-8 text-center text-muted-foreground">
                    No services match your search.
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="categories">
            {isLoading ? (
              <div className="space-y-6">
                {["Streaming Services", "Shopping Services", "Social Media Services", "Other Services"].map((category, idx) => (
                  <section key={idx}>
                    <h2 className="text-xl font-semibold mb-3">{category}</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
                      {Array(4).fill(0).map((_, index) => (
                        <Card key={index}>
                          <CardContent className="pt-6 pb-6">
                            <div className="flex flex-col items-center space-y-3">
                              <Skeleton className="h-16 w-16 rounded-md" />
                              <Skeleton className="h-5 w-24" />
                              <Skeleton className="h-4 w-16" />
                              <Skeleton className="h-8 w-full mt-2" />
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </section>
                ))}
              </div>
            ) : (
              <div className="space-y-6">
                <section>
                  <h2 className="text-xl font-semibold mb-3">Streaming Services</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
                    {streamingServices.length > 0 ? (
                      streamingServices.map(service => (
                        <Card key={service.id} className="overflow-hidden">
                          <CardHeader className="pb-2">
                            <div className="flex justify-center">
                              <div className="h-16 w-16 flex items-center justify-center">
                                <img src={service.logo} alt={service.name} className="max-h-full max-w-full object-contain" />
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="text-center pb-2">
                            <CardTitle className="text-lg mb-1">{service.name}</CardTitle>
                            <p className="text-sm text-muted-foreground mb-2">{service.description}</p>
                            {renderCompatibilityBadge(service.compatibilityStatus)}
                          </CardContent>
                          <CardFooter className="pt-0 pb-4 justify-center">
                            <Button variant="outline" className="w-full flex gap-2">
                              <CreditCard className="h-4 w-4" />
                              Use with Virtual Card
                            </Button>
                          </CardFooter>
                        </Card>
                      ))
                    ) : (
                      <div className="col-span-full py-4 text-center text-muted-foreground">
                        No streaming services available.
                      </div>
                    )}
                  </div>
                </section>

                <section>
                  <h2 className="text-xl font-semibold mb-3">Shopping Services</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
                    {shoppingServices.length > 0 ? (
                      shoppingServices.map(service => (
                        <Card key={service.id} className="overflow-hidden">
                          <CardHeader className="pb-2">
                            <div className="flex justify-center">
                              <div className="h-16 w-16 flex items-center justify-center">
                                <img src={service.logo} alt={service.name} className="max-h-full max-w-full object-contain" />
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="text-center pb-2">
                            <CardTitle className="text-lg mb-1">{service.name}</CardTitle>
                            <p className="text-sm text-muted-foreground mb-2">{service.description}</p>
                            {renderCompatibilityBadge(service.compatibilityStatus)}
                          </CardContent>
                          <CardFooter className="pt-0 pb-4 justify-center">
                            <Button variant="outline" className="w-full flex gap-2">
                              <CreditCard className="h-4 w-4" />
                              Use with Virtual Card
                            </Button>
                          </CardFooter>
                        </Card>
                      ))
                    ) : (
                      <div className="col-span-full py-4 text-center text-muted-foreground">
                        No shopping services available.
                      </div>
                    )}
                  </div>
                </section>

                <section>
                  <h2 className="text-xl font-semibold mb-3">Social Media Services</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
                    {socialServices.length > 0 ? (
                      socialServices.map(service => (
                        <Card key={service.id} className="overflow-hidden">
                          <CardHeader className="pb-2">
                            <div className="flex justify-center">
                              <div className="h-16 w-16 flex items-center justify-center">
                                <img src={service.logo} alt={service.name} className="max-h-full max-w-full object-contain" />
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="text-center pb-2">
                            <CardTitle className="text-lg mb-1">{service.name}</CardTitle>
                            <p className="text-sm text-muted-foreground mb-2">{service.description}</p>
                            {renderCompatibilityBadge(service.compatibilityStatus)}
                          </CardContent>
                          <CardFooter className="pt-0 pb-4 justify-center">
                            <Button variant="outline" className="w-full flex gap-2">
                              <CreditCard className="h-4 w-4" />
                              Use with Virtual Card
                            </Button>
                          </CardFooter>
                        </Card>
                      ))
                    ) : (
                      <div className="col-span-full py-4 text-center text-muted-foreground">
                        No social media services available.
                      </div>
                    )}
                  </div>
                </section>

                <section>
                  <h2 className="text-xl font-semibold mb-3">Other Services</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3">
                    {otherServices.length > 0 ? (
                      otherServices.map(service => (
                        <Card key={service.id} className="overflow-hidden">
                          <CardHeader className="pb-2">
                            <div className="flex justify-center">
                              <div className="h-16 w-16 flex items-center justify-center">
                                <img src={service.logo} alt={service.name} className="max-h-full max-w-full object-contain" />
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="text-center pb-2">
                            <CardTitle className="text-lg mb-1">{service.name}</CardTitle>
                            <p className="text-sm text-muted-foreground mb-2">{service.description}</p>
                            {renderCompatibilityBadge(service.compatibilityStatus)}
                          </CardContent>
                          <CardFooter className="pt-0 pb-4 justify-center">
                            <Button variant="outline" className="w-full flex gap-2">
                              <CreditCard className="h-4 w-4" />
                              Use with Virtual Card
                            </Button>
                          </CardFooter>
                        </Card>
                      ))
                    ) : (
                      <div className="col-span-full py-4 text-center text-muted-foreground">
                        No other services available.
                      </div>
                    )}
                  </div>
                </section>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Services;
