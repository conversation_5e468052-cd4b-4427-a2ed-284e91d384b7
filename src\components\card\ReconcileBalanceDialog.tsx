
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, AlertCircle } from 'lucide-react';
import { useWallet } from '@/contexts/WalletContext';
import { useCard } from '@/contexts/CardContext';
import { CryptoType } from '@/types/wallet';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ReconcileBalanceDialogProps {
  trigger?: React.ReactNode;
}

export function ReconcileBalanceDialog({ trigger }: ReconcileBalanceDialogProps) {
  const { wallets, reconcileWalletBalance, isLoading } = useWallet();
  const { card } = useCard();
  
  const [selectedWalletId, setSelectedWalletId] = useState<string>('');
  const [selectedTokenType, setSelectedTokenType] = useState<CryptoType>(CryptoType.USDC);
  const [amountToDeduct, setAmountToDeduct] = useState<number>(0);
  const [isOpen, setIsOpen] = useState(false);

  const handleReconcile = async () => {
    if (!selectedWalletId || amountToDeduct <= 0) return;
    
    const success = await reconcileWalletBalance(selectedWalletId, selectedTokenType, amountToDeduct);
    if (success) {
      setIsOpen(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="flex items-center gap-2 transition-all hover:bg-primary/10">
            <RefreshCw className="h-4 w-4" />
            Reconcile Balance
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] p-6 rounded-xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-center">Reconcile Wallet Balance</DialogTitle>
        </DialogHeader>
        
        <Alert variant="warning" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Use this to fix your wallet balance if you previously funded your card but the amount wasn't deducted from your wallet.
          </AlertDescription>
        </Alert>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="wallet" className="text-sm font-medium">Select Wallet</Label>
            <Select
              value={selectedWalletId}
              onValueChange={setSelectedWalletId}
            >
              <SelectTrigger className="w-full rounded-lg border-input shadow-sm">
                <SelectValue placeholder="Select a wallet" />
              </SelectTrigger>
              <SelectContent>
                {wallets.map(wallet => (
                  <SelectItem key={wallet.id} value={wallet.id} className="cursor-pointer">
                    {wallet.name} (${wallet.totalDollarValue.toFixed(2)})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="token" className="text-sm font-medium">Token Type</Label>
            <Select
              value={selectedTokenType}
              onValueChange={(value) => setSelectedTokenType(value as CryptoType)}
            >
              <SelectTrigger className="w-full rounded-lg border-input shadow-sm">
                <SelectValue placeholder="Select token" />
              </SelectTrigger>
              <SelectContent>
                {selectedWalletId && wallets.find(w => w.id === selectedWalletId)?.tokens.map(token => (
                  <SelectItem key={token.type} value={token.type} className="cursor-pointer">
                    {token.type} (Available: {token.balance})
                  </SelectItem>
                ))}
                {!selectedWalletId && (
                  <>
                    <SelectItem value={CryptoType.USDC}>USDC</SelectItem>
                    <SelectItem value={CryptoType.SOL}>SOL</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount" className="text-sm font-medium">Amount to Deduct</Label>
            <Input
              id="amount"
              type="number"
              min="0"
              step="0.01"
              value={amountToDeduct || ''}
              onChange={(e) => setAmountToDeduct(parseFloat(e.target.value) || 0)}
              placeholder="Enter amount"
              className="rounded-lg shadow-sm"
            />
            {selectedWalletId && selectedTokenType && (
              <p className="text-xs text-muted-foreground mt-1">
                Available: {wallets.find(w => w.id === selectedWalletId)?.tokens.find(t => t.type === selectedTokenType)?.balance || 0} {selectedTokenType}
              </p>
            )}
            {card && (
              <p className="text-xs text-muted-foreground mt-1">
                Card Balance: ${card.balance.toFixed(2)}
              </p>
            )}
          </div>

          <Button
            className="w-full mt-6 rounded-lg font-medium transition-all"
            onClick={handleReconcile}
            disabled={isLoading || !selectedWalletId || amountToDeduct <= 0}
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Reconcile Balance'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
