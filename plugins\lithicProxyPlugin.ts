
import { Plugin } from 'vite';
import fs from 'fs';
import path from 'path';
import { IncomingMessage, ServerResponse } from 'http';

// Define HeadersInit type if it's not available
type HeadersInit = Headers | Record<string, string> | [string, string][];

export default function lithicProxyPlugin(): Plugin {
  return {
    name: 'lithic-proxy-plugin',
    configureServer(server) {
      server.middlewares.use('/api/lithic', async (req: IncomingMessage, res: ServerResponse, next) => {
        // Extract the path after /api/lithic
        const lithicPath = req.url?.replace(/^\/\??/, '') || '';
        
        // Get the lithic base URL
        const LITHIC_BASE_URL = 'https://sandbox.lithic.com/v1';
        
        // Your Lithic API key (in a real app this would be in env variables)
        const LITHIC_API_KEY = process.env.LITHIC_API_KEY || 'eb3c43cf-66ca-4714-8fb4-d87f4308cc57';

        try {
          // Prepare headers for the request to Lithic
          const headers: HeadersInit = {
            'Authorization': `Bearer ${LITHIC_API_KEY}`,
            'Content-Type': 'application/json',
          };

          // Get the request method
          const method = req.method || 'GET';

          // Prepare the request body for non-GET requests
          let body;
          if (method !== 'GET' && method !== 'HEAD') {
            // Read the request body
            const buffers = [];
            for await (const chunk of req) {
              buffers.push(chunk);
            }
            const data = Buffer.concat(buffers).toString();
            if (data) {
              try {
                body = JSON.parse(data);
              } catch (e) {
                body = data;
              }
            }
          }

          // Make the request to Lithic
          const response = await fetch(`${LITHIC_BASE_URL}/${lithicPath}`, {
            method,
            headers,
            body: body ? JSON.stringify(body) : undefined,
          });

          // Read the response from Lithic
          const responseData = await response.text();
          
          // Set the status code
          res.statusCode = response.status;
          
          // Set appropriate headers
          for (const [key, value] of response.headers.entries()) {
            res.setHeader(key, value);
          }
          
          // Send the response
          res.end(responseData);
        } catch (error) {
          console.error('Error proxying request to Lithic:', error);
          res.statusCode = 500;
          res.end(JSON.stringify({ error: 'Internal Server Error' }));
        }
      });
    }
  };
}
