/**
 * Telegram Integration Component - Coming Soon
 */

import React from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Message<PERSON>ir<PERSON>, 
  Bo<PERSON>,
  ArrowRight,
  Clock,
  CheckCircle
} from 'lucide-react';

const TelegramIntegration: React.FC = () => {
  return (
    <Layout>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Bot className="h-8 w-8 text-blue-600" />
              <div>
                <CardTitle className="text-2xl">Telegram Integration</CardTitle>
                <p className="text-gray-600">Control your crypto with Telegram bot commands</p>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Coming Soon Banner */}
        <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
          <CardContent className="p-8">
            <div className="text-center">
              <div className="text-6xl mb-6">🚧</div>
              <h2 className="text-3xl font-bold text-yellow-800 mb-4">Coming Soon!</h2>
              <p className="text-lg text-yellow-700 mb-6 max-w-2xl mx-auto">
                We're putting the finishing touches on our Telegram bot integration. 
                This revolutionary feature will be available very soon!
              </p>
              
              <div className="bg-yellow-100 p-6 rounded-lg mb-6">
                <h3 className="text-lg font-semibold text-yellow-800 mb-4">What to expect:</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-yellow-800">Check Balances</p>
                      <p className="text-sm text-yellow-700">View your crypto balances across all chains</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-yellow-800">Withdraw Funds</p>
                      <p className="text-sm text-yellow-700">Convert crypto to Naira via chat commands</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-yellow-800">Pay Merchants</p>
                      <p className="text-sm text-yellow-700">Send payments to QR merchants instantly</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-yellow-800">Real-time Notifications</p>
                      <p className="text-sm text-yellow-700">Get instant alerts for all transactions</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-center gap-2 text-yellow-700">
                <Clock className="h-5 w-5" />
                <span className="font-medium">Expected launch: Very Soon</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preview Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <div className="text-center">
              <MessageCircle className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-blue-800 mb-2">Sneak Peek</h3>
              <p className="text-blue-700 mb-4">
                Here's what using the Telegram bot will look like:
              </p>
              
              <div className="bg-white p-4 rounded-lg border max-w-md mx-auto">
                <div className="text-left space-y-2 font-mono text-sm">
                  <div className="text-blue-600">You: /balance</div>
                  <div className="text-gray-600">
                    Bot: 💰 Your Balances<br/>
                    🔗 SOLANA: 0.5 SOL ($90)<br/>
                    💵 USDC: 100 USDC ($100)<br/>
                    Total: ₦293,000
                  </div>
                  <div className="text-blue-600">You: /withdraw 50 USDC GTBank</div>
                  <div className="text-gray-600">
                    Bot: ✅ Withdrawal initiated!<br/>
                    ₦77,000 will be sent to your GTBank account.
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stay Updated */}
        <Card>
          <CardContent className="p-6 text-center">
            <h3 className="text-xl font-bold mb-4">Stay Updated</h3>
            <p className="text-gray-600 mb-4">
              We'll notify you as soon as the Telegram integration is ready!
            </p>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <MessageCircle className="h-4 w-4 mr-2" />
              Get Notified When Ready
            </Button>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default TelegramIntegration;
