/**
 * Off-Ramp Page - Crypto to NGN Conversion
 * 
 * Main page for users to convert their crypto to Nigerian Naira
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Banknote,
  TrendingUp,
  Clock,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  Building,
  CreditCard,
  History,
  Info
} from 'lucide-react';
import Layout from '@/components/Layout';
import OffRampWidget from '@/components/off-ramp/OffRampWidget';
import { SimpleCrossChainDeposit } from '@/components/deposit/SimpleCrossChainDeposit';
import BankAccountManager from '@/components/off-ramp/BankAccountManager';
import { useWallet } from '@/contexts/WalletContext';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

export default function OffRampPage() {
  const { wallets } = useWallet();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('withdraw');
  const [offRampTransactions, setOffRampTransactions] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  // Load real transaction history
  useEffect(() => {
    if (user && activeTab === 'history') {
      loadTransactionHistory();
    }
  }, [user, activeTab]);

  const loadTransactionHistory = async () => {
    if (!user) return;

    setIsLoadingHistory(true);

    try {
      // Now that tables are created, use real database query
      const { data: transactions, error } = await supabase
        .from('withdrawal_requests' as any)
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        console.error('Error loading transaction history:', error);
        // Show mock data as fallback
        setOffRampTransactions([
          {
            id: '1',
            type: 'withdrawal',
            cryptoAmount: '25.00',
            cryptoType: 'USDC',
            ngnAmount: '41,250',
            bankAccount: '**********',
            bankName: 'Access Bank',
            status: 'completed',
            date: new Date(Date.now() - ********),
            duration: '45 seconds'
          }
        ]);
      } else if (transactions && transactions.length > 0) {
        const formattedTransactions = transactions.map((tx: any) => ({
          id: tx.id,
          type: 'withdrawal',
          cryptoAmount: '25.00', // Will be filled from crypto_deposits when linked
          cryptoType: 'USDC',
          ngnAmount: tx.amount_ngn.toLocaleString(),
          bankAccount: tx.account_number,
          bankName: tx.bank_name,
          status: tx.status,
          date: new Date(tx.created_at),
          duration: tx.status === 'completed' ? '45 seconds' : 'Processing...'
        }));
        setOffRampTransactions(formattedTransactions);
      } else {
        // No transactions yet, show empty state
        setOffRampTransactions([]);
      }
    } catch (error) {
      console.error('Error loading transaction history:', error);
      setOffRampTransactions([]);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} hours ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} days ago`;
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-3 md:px-4 py-4 md:py-6 max-w-6xl">
        {/* Header - Mobile Optimized */}
        <div className="mb-4 md:mb-8">
          <h1 className="text-xl md:text-3xl font-bold mb-1 md:mb-2">Crypto Off-Ramp</h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Convert your cryptocurrency to Nigerian Naira instantly
          </p>

          {/* Demo Mode Notice */}
          <div className="mt-3 bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <Info className="w-4 h-4 text-amber-600" />
              <p className="text-sm text-amber-800">
                <strong>Demo Mode:</strong> Real transactions are disabled until Paystack business registration is complete.
              </p>
            </div>
          </div>
        </div>

        {/* Stats Cards - Mobile Optimized */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4 mb-4 md:mb-8">
          <Card>
            <CardContent className="pt-3 md:pt-6 pb-3 md:pb-6">
              <div className="flex items-center">
                <Zap className="h-5 w-5 md:h-8 md:w-8 text-yellow-500" />
                <div className="ml-2 md:ml-4">
                  <p className="text-xs md:text-sm font-medium text-muted-foreground">Speed</p>
                  <p className="text-lg md:text-2xl font-bold">45s</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-3 md:pt-6 pb-3 md:pb-6">
              <div className="flex items-center">
                <TrendingUp className="h-5 w-5 md:h-8 md:w-8 text-green-500" />
                <div className="ml-2 md:ml-4">
                  <p className="text-xs md:text-sm font-medium text-muted-foreground">Rate</p>
                  <p className="text-lg md:text-2xl font-bold">₦1,650</p>
                  <p className="text-xs text-muted-foreground hidden md:block">per USDC</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-3 md:pt-6 pb-3 md:pb-6">
              <div className="flex items-center">
                <Building className="h-5 w-5 md:h-8 md:w-8 text-blue-500" />
                <div className="ml-2 md:ml-4">
                  <p className="text-xs md:text-sm font-medium text-muted-foreground">Banks</p>
                  <p className="text-lg md:text-2xl font-bold">All</p>
                  <p className="text-xs text-muted-foreground hidden md:block">Nigerian banks</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-3 md:pt-6 pb-3 md:pb-6">
              <div className="flex items-center">
                <Shield className="h-5 w-5 md:h-8 md:w-8 text-purple-500" />
                <div className="ml-2 md:ml-4">
                  <p className="text-xs md:text-sm font-medium text-muted-foreground">Security</p>
                  <p className="text-lg md:text-2xl font-bold">100%</p>
                  <p className="text-xs text-muted-foreground hidden md:block">NIBSS secured</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-4 md:gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              {/* Mobile-Optimized Tabs */}
              <TabsList className="grid w-full grid-cols-3 md:grid-cols-4 gap-1 md:gap-0 h-auto md:h-10 p-1">
                <TabsTrigger
                  value="deposit"
                  className="text-xs md:text-sm py-2 md:py-2 px-1 md:px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  Deposit
                </TabsTrigger>
                <TabsTrigger
                  value="withdraw"
                  className="text-xs md:text-sm py-2 md:py-2 px-1 md:px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  Withdraw
                </TabsTrigger>
                <TabsTrigger
                  value="accounts"
                  className="text-xs md:text-sm py-2 md:py-2 px-1 md:px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  Accounts
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="text-xs md:text-sm py-2 md:py-2 px-1 md:px-3 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  History
                </TabsTrigger>
              </TabsList>

              <TabsContent value="deposit" className="mt-3 md:mt-6">
                <SimpleCrossChainDeposit />
              </TabsContent>

              <TabsContent value="withdraw" className="mt-3 md:mt-6">
                <OffRampWidget />
              </TabsContent>

              <TabsContent value="accounts" className="mt-3 md:mt-6">
                <BankAccountManager />
              </TabsContent>

              <TabsContent value="history" className="mt-3 md:mt-6">
                <Card>
                  <CardContent className="pt-3 md:pt-6">
                    {isLoadingHistory ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <span className="ml-2 text-sm text-muted-foreground">Loading history...</span>
                      </div>
                    ) : offRampTransactions.length > 0 ? (
                      <div className="space-y-4">
                        {offRampTransactions.map((tx) => (
                          <div key={tx.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                            <div className="flex items-center space-x-4">
                              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                                <Banknote className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <div className="font-medium">
                                  {tx.cryptoAmount} {tx.cryptoType} → ₦{tx.ngnAmount}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {tx.bankName} • {tx.bankAccount} • {formatTimeAgo(tx.date)}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <Badge className={getStatusColor(tx.status)}>
                                {tx.status}
                              </Badge>
                              <div className="text-sm text-muted-foreground mt-1">
                                {tx.duration}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Banknote className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="font-medium mb-2">No Transactions Yet</h3>
                        <p className="text-sm text-muted-foreground">
                          Your off-ramp transaction history will appear here
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>


            </Tabs>
          </div>

          {/* Sidebar - Hidden on Mobile */}
          <div className="hidden lg:block space-y-6">
            {/* How It Works */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">How It Works</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                    1
                  </div>
                  <div>
                    <p className="font-medium">Choose Amount</p>
                    <p className="text-sm text-muted-foreground">Select crypto and amount to convert</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                    2
                  </div>
                  <div>
                    <p className="font-medium">Bank Details</p>
                    <p className="text-sm text-muted-foreground">Enter your Nigerian bank account</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                    3
                  </div>
                  <div>
                    <p className="font-medium">Instant Transfer</p>
                    <p className="text-sm text-muted-foreground">Receive NGN in 30-60 seconds</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Supported Assets */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Supported Assets</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      $
                    </div>
                    <div>
                      <p className="font-medium">USDC</p>
                      <p className="text-sm text-muted-foreground">USD Coin</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">₦1,650</p>
                    <p className="text-sm text-green-600">+0.5%</p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      S
                    </div>
                    <div>
                      <p className="font-medium">SOL</p>
                      <p className="text-sm text-muted-foreground">Solana</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">₦99,375</p>
                    <p className="text-sm text-green-600">+2.1%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security & Compliance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">NIBSS Integration</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Bank-Grade Security</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Real-time Monitoring</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm">Instant Settlement</span>
                </div>
              </CardContent>
            </Card>

            {/* Support */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Our support team is available 24/7 to help with your transactions.
                </p>
                <Button variant="outline" className="w-full">
                  Contact Support
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Mobile-Only Quick Info Section */}
        <div className="lg:hidden mt-6 space-y-4">
          {/* Quick Stats */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Quick Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Average Speed</span>
                <span className="text-sm font-medium">45 seconds</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Supported Assets</span>
                <span className="text-sm font-medium">USDC, SOL</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Security</span>
                <span className="text-sm font-medium">NIBSS Secured</span>
              </div>
            </CardContent>
          </Card>

          {/* Mobile Support */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">
                24/7 support for all transactions
              </p>
              <Button variant="outline" size="sm" className="w-full">
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
