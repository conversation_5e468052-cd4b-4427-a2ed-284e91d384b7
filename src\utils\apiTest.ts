/**
 * API Test Utility - Test crypto price APIs
 */

import { cryptoApiService } from '@/services/cryptoApiService';
import { crossChainPricingService } from '@/services/crossChainPricingService';

export async function testCryptoApis() {
  console.log('🧪 Testing crypto price APIs...');

  try {
    const startTime = Date.now();
    const prices = await cryptoApiService.fetchCryptoPrices();
    const endTime = Date.now();

    console.log('✅ API Test Results:');
    console.log(`⏱️ Response time: ${endTime - startTime}ms`);
    console.log('📊 Prices received:');

    Object.entries(prices).forEach(([coin, data]) => {
      const price = data.usd;
      const change = data.usd_24h_change || 0;
      const changeSymbol = change >= 0 ? '📈' : '📉';
      console.log(`  ${coin}: $${price.toLocaleString()} ${changeSymbol} ${change.toFixed(2)}%`);
    });

    return { success: true, prices, responseTime: endTime - startTime };
  } catch (error) {
    console.error('❌ API Test Failed:', error);
    return { success: false, error: error.message };
  }
}

export async function testCrossChainPricing() {
  console.log('🧪 Testing cross-chain pricing service...');

  try {
    const startTime = Date.now();
    const tokenPrices = await crossChainPricingService.getAllTokenPrices();
    const endTime = Date.now();

    console.log('✅ Cross-Chain Pricing Test Results:');
    console.log(`⏱️ Response time: ${endTime - startTime}ms`);
    console.log(`📊 Token prices loaded: ${tokenPrices.length}`);

    // Group by chain
    const pricesByChain = tokenPrices.reduce((acc, price) => {
      if (!acc[price.chain]) acc[price.chain] = [];
      acc[price.chain].push(price);
      return acc;
    }, {} as Record<string, any[]>);

    Object.entries(pricesByChain).forEach(([chain, prices]) => {
      console.log(`\n🔗 ${chain.toUpperCase()}:`);
      prices.forEach(price => {
        const changeSymbol = (price.change24h || 0) >= 0 ? '📈' : '📉';
        const changeText = price.change24h ? `${changeSymbol} ${price.change24h.toFixed(2)}%` : '';
        console.log(`  ${price.symbol}: $${price.priceUSD.toFixed(4)} ${changeText}`);
      });
    });

    return { success: true, tokenPrices, responseTime: endTime - startTime };
  } catch (error) {
    console.error('❌ Cross-Chain Pricing Test Failed:', error);
    return { success: false, error: error.message };
  }
}

// Auto-run tests in development
console.log('⚠️ API tests DISABLED - Using ReliableExchangeRateService instead');
/*
if (process.env.NODE_ENV === 'development') {
  // Run tests after a short delay to avoid blocking app startup
  setTimeout(() => {
    testCryptoApis();
    // Test cross-chain pricing after basic API test
    setTimeout(() => {
      testCrossChainPricing();
    }, 3000);
  }, 2000);
}
*/
