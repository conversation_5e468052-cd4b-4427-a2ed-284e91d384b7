/**
 * Bank Account Manager Component
 * 
 * Manages user bank accounts with verification and selection
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Building,
  Plus,
  Check,
  X,
  Star,
  Trash2,
  Shield,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Search,
  ChevronDown,
  ChevronsUpDown
} from 'lucide-react';
import { bankAccountService, BankAccount, validateAccountNumber } from '@/services/bankAccountService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';

interface BankAccountManagerProps {
  onAccountSelected?: (account: BankAccount) => void;
  selectedAccountId?: string;
  showAddForm?: boolean;
}

export default function BankAccountManager({
  onAccountSelected,
  selectedAccountId,
  showAddForm = true
}: BankAccountManagerProps) {
  const { user } = useAuth();
  const [accounts, setAccounts] = useState<BankAccount[]>([]);
  const [banks, setBanks] = useState<Array<{ code: string; name: string }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isAdding, setIsAdding] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    bankName: '',
    bankCode: '',
    accountNumber: '',
    setAsDefault: false
  });

  // Bank search state
  const [bankSearchOpen, setBankSearchOpen] = useState(false);
  const [bankSearchValue, setBankSearchValue] = useState('');

  // Filter banks based on search
  const filteredBanks = banks.filter(bank =>
    bank.name.toLowerCase().includes(bankSearchValue.toLowerCase())
  );
  const [verificationResult, setVerificationResult] = useState<{
    success: boolean;
    accountName?: string;
    message: string;
  } | null>(null);

  useEffect(() => {
    if (user) {
      loadUserAccounts();
      loadBanks();
    }
  }, [user]);

  const loadUserAccounts = async () => {
    if (!user) return;
    
    try {
      const userAccounts = await bankAccountService.getUserBankAccounts(user.id);
      setAccounts(userAccounts);
    } catch (error) {
      console.error('Error loading accounts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadBanks = async () => {
    try {
      const nigerianBanks = await bankAccountService.getNigerianBanks();
      setBanks(nigerianBanks);
    } catch (error) {
      console.error('Error loading banks:', error);
    }
  };

  const handleVerifyAccount = async () => {
    if (!formData.bankName || !formData.bankCode || !formData.accountNumber) {
      toast({
        title: "Missing Information",
        description: "Please select a bank and enter account number",
        variant: "destructive",
      });
      return;
    }

    if (!validateAccountNumber(formData.accountNumber)) {
      toast({
        title: "Invalid Account Number",
        description: "Account number must be exactly 10 digits",
        variant: "destructive",
      });
      return;
    }

    setIsVerifying(true);
    setVerificationResult(null);

    try {
      console.log(`🔍 Verifying account ${formData.accountNumber} with bank ${formData.bankName} (${formData.bankCode})`);

      const result = await bankAccountService.verifyBankAccount(
        formData.accountNumber,
        formData.bankCode
      );

      setVerificationResult(result);

      if (result.success) {
        toast({
          title: "Account Verified! ✅",
          description: `Account belongs to ${result.accountName}`,
        });
      } else {
        toast({
          title: "Verification Failed",
          description: result.message,
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('Verification error:', error);
      setVerificationResult({
        success: false,
        message: 'Network error during verification'
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleAddAccount = async () => {
    if (!user || !verificationResult?.success) return;

    setIsAdding(true);

    try {
      // Extract just the bank name from the "bankCode|bankName" format
      const bankName = formData.bankName.includes('|')
        ? formData.bankName.split('|')[1]
        : formData.bankName;

      const result = await bankAccountService.addBankAccount(
        user.id,
        bankName,
        formData.accountNumber,
        formData.setAsDefault
      );

      if (result.success && result.account) {
        setAccounts(prev => [result.account!, ...prev]);
        setFormData({ bankName: '', bankCode: '', accountNumber: '', setAsDefault: false });
        setVerificationResult(null);
        setShowForm(false);

        toast({
          title: "Account Added! 🎉",
          description: "Bank account has been added to your profile",
        });

        // Auto-select the new account
        if (onAccountSelected) {
          onAccountSelected(result.account);
        }
      } else {
        toast({
          title: "Failed to Add Account",
          description: result.message,
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('Error adding account:', error);
      toast({
        title: "Error",
        description: "Failed to add bank account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAdding(false);
    }
  };

  const handleSetDefault = async (accountId: string) => {
    if (!user) return;

    try {
      const success = await bankAccountService.setDefaultBankAccount(user.id, accountId);
      
      if (success) {
        setAccounts(prev => prev.map(account => ({
          ...account,
          isDefault: account.id === accountId
        })));

        toast({
          title: "Default Account Updated",
          description: "This account is now your default for withdrawals",
        });
      }
    } catch (error) {
      console.error('Error setting default:', error);
    }
  };

  const handleDeleteAccount = async (accountId: string) => {
    if (!user) return;

    try {
      const success = await bankAccountService.deleteBankAccount(user.id, accountId);
      
      if (success) {
        setAccounts(prev => prev.filter(account => account.id !== accountId));
        
        toast({
          title: "Account Removed",
          description: "Bank account has been removed from your profile",
        });
      }
    } catch (error) {
      console.error('Error deleting account:', error);
    }
  };

  const handleSelectAccount = (account: BankAccount) => {
    if (onAccountSelected) {
      onAccountSelected(account);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Bank Accounts</h3>
          <p className="text-sm text-muted-foreground">Manage your Nigerian bank accounts</p>
        </div>
        {accounts.length > 0 && showAddForm && !showForm && (
          <Button
            onClick={() => setShowForm(true)}
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Account
          </Button>
        )}
      </div>

      {/* Existing Accounts */}
      {accounts.length > 0 && (
        <div className="space-y-3">
          {accounts.map((account) => (
            <Card 
              key={account.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedAccountId === account.id ? 'ring-2 ring-primary bg-primary/5' : ''
              }`}
              onClick={() => handleSelectAccount(account)}
            >
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Building className="w-5 h-5 text-blue-600" />
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{account.bankName}</span>
                        {account.isDefault && (
                          <Badge className="bg-green-100 text-green-800">
                            <Star className="w-3 h-3 mr-1" />
                            Default
                          </Badge>
                        )}
                        {account.isVerified && (
                          <Badge className="bg-blue-100 text-blue-800">
                            <Shield className="w-3 h-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {account.accountNumber} • {account.accountName}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Used {account.usageCount} times
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {!account.isDefault && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSetDefault(account.id);
                        }}
                      >
                        <Star className="w-4 h-4" />
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAccount(account.id);
                      }}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Add Another Account Button */}
          {showAddForm && !showForm && (
            <Card className="border-dashed border-2 border-muted-foreground/25">
              <CardContent className="pt-6">
                <div className="text-center py-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowForm(true)}
                    className="w-full"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Another Bank Account
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Add Account Form */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Add Bank Account
            </CardTitle>
            <CardDescription>
              Add and verify a new Nigerian bank account
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Bank Selection with Search */}
            <div className="space-y-2">
              <Label>Bank Name</Label>
              <Popover open={bankSearchOpen} onOpenChange={setBankSearchOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={bankSearchOpen}
                    className="w-full justify-between"
                  >
                    {formData.bankName ? formData.bankName : "Select your bank..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <Command>
                    <CommandInput
                      placeholder="Search banks..."
                      value={bankSearchValue}
                      onValueChange={setBankSearchValue}
                    />
                    <CommandList>
                      <CommandEmpty>No bank found.</CommandEmpty>
                      <CommandGroup>
                        {filteredBanks.map((bank, index) => (
                          <CommandItem
                            key={`${bank.code}-${index}`}
                            value={bank.name}
                            onSelect={() => {
                              setFormData(prev => ({
                                ...prev,
                                bankName: bank.name,
                                bankCode: bank.code
                              }));
                              setBankSearchOpen(false);
                              setBankSearchValue('');
                            }}
                          >
                            <Check
                              className={`mr-2 h-4 w-4 ${
                                formData.bankName === bank.name ? "opacity-100" : "opacity-0"
                              }`}
                            />
                            {bank.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <p className="text-xs text-muted-foreground">
                Search from 200+ Nigerian banks including Opay, Kuda, Access, GTB, etc.
              </p>
            </div>

            {/* Account Number */}
            <div className="space-y-2">
              <Label>Account Number</Label>
              <Input
                type="text"
                placeholder="**********"
                value={formData.accountNumber}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  accountNumber: e.target.value.replace(/\D/g, '').slice(0, 10)
                }))}
                maxLength={10}
              />
            </div>

            {/* Verification Result */}
            {verificationResult && (
              <Alert className={verificationResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                {verificationResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={verificationResult.success ? 'text-green-700' : 'text-red-700'}>
                  {verificationResult.success 
                    ? `Account verified: ${verificationResult.accountName}`
                    : verificationResult.message
                  }
                </AlertDescription>
              </Alert>
            )}

            {/* Set as Default */}
            {verificationResult?.success && (
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="setDefault"
                  checked={formData.setAsDefault}
                  onChange={(e) => setFormData(prev => ({ ...prev, setAsDefault: e.target.checked }))}
                  className="rounded"
                />
                <Label htmlFor="setDefault" className="text-sm">
                  Set as default account
                </Label>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowForm(false);
                  setFormData({ bankName: '', bankCode: '', accountNumber: '', setAsDefault: false });
                  setVerificationResult(null);
                }}
                className="flex-1"
              >
                Cancel
              </Button>

              {!verificationResult?.success ? (
                <Button
                  onClick={handleVerifyAccount}
                  disabled={isVerifying || !formData.bankName || !formData.accountNumber}
                  className="flex-1"
                >
                  {isVerifying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      Verifying...
                    </>
                  ) : (
                    <>
                      <Shield className="w-4 h-4 mr-2" />
                      Verify Account
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleAddAccount}
                  disabled={isAdding}
                  className="flex-1"
                >
                  {isAdding ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Account
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {accounts.length === 0 && !showForm && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Building className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium mb-2">No Bank Accounts</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Add a Nigerian bank account to receive your NGN withdrawals
              </p>
              {showAddForm && (
                <Button onClick={() => setShowForm(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Account
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
